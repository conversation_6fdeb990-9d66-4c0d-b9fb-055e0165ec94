#!/usr/bin/env python3
"""Auto Trade Planner

Intelligent trade planning system that can:
- Plan trades to meet specific profit targets
- "Make me $X today" complete automation
- Optimal strategy selection based on market conditions
- Risk-adjusted position sizing
- Multi-setup portfolio construction
"""
import json
import math
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging


class TradePlan:
    """Represents a complete trade plan to meet profit targets."""
    
    def __init__(self, target_profit: float, max_risk: float, account_size: float):
        self.target_profit = target_profit
        self.max_risk = max_risk
        self.account_size = account_size
        self.created_at = datetime.now()
        
        # Plan components
        self.selected_setups = []
        self.total_risk = 0.0
        self.expected_profit = 0.0
        self.success_probability = 0.0
        self.plan_confidence = 0.0
        
        # Execution details
        self.trades = []
        self.status = "planned"  # planned, executing, completed, failed
    
    def add_trade(self, setup, position_size: int, risk_amount: float, 
                  expected_return: float, probability: float):
        """Add a trade to the plan."""
        trade = {
            'setup': setup,
            'symbol': setup.symbol,
            'position_size': position_size,
            'risk_amount': risk_amount,
            'expected_return': expected_return,
            'probability': probability,
            'entry_price': setup.current_price,
            'stop_loss': self._calculate_stop_loss(setup, risk_amount, position_size),
            'take_profit': self._calculate_take_profit(setup, expected_return, position_size),
            'confidence': setup.confidence_score
        }
        
        self.trades.append(trade)
        self.total_risk += risk_amount
        self.expected_profit += expected_return
        
        # Update plan metrics
        self._update_plan_metrics()
    
    def _calculate_stop_loss(self, setup, risk_amount: float, position_size: int) -> float:
        """Calculate stop loss price based on risk amount."""
        risk_per_share = risk_amount / position_size if position_size > 0 else 0
        return setup.current_price - risk_per_share
    
    def _calculate_take_profit(self, setup, expected_return: float, position_size: int) -> float:
        """Calculate take profit price based on expected return."""
        profit_per_share = expected_return / position_size if position_size > 0 else 0
        return setup.current_price + profit_per_share
    
    def _update_plan_metrics(self):
        """Update overall plan metrics."""
        if not self.trades:
            return
        
        # Calculate weighted success probability
        total_weight = sum(trade['risk_amount'] for trade in self.trades)
        if total_weight > 0:
            self.success_probability = sum(
                trade['probability'] * (trade['risk_amount'] / total_weight)
                for trade in self.trades
            )
        
        # Calculate plan confidence (average of setup confidences)
        self.plan_confidence = sum(trade['confidence'] for trade in self.trades) / len(self.trades)
    
    def get_plan_summary(self) -> Dict:
        """Get plan summary."""
        return {
            'target_profit': self.target_profit,
            'expected_profit': self.expected_profit,
            'total_risk': self.total_risk,
            'risk_reward_ratio': self.expected_profit / self.total_risk if self.total_risk > 0 else 0,
            'success_probability': self.success_probability,
            'plan_confidence': self.plan_confidence,
            'num_trades': len(self.trades),
            'account_risk_pct': (self.total_risk / self.account_size) * 100,
            'status': self.status
        }
    
    def to_dict(self) -> Dict:
        """Convert plan to dictionary."""
        return {
            'target_profit': self.target_profit,
            'max_risk': self.max_risk,
            'account_size': self.account_size,
            'created_at': self.created_at.isoformat(),
            'trades': self.trades,
            'summary': self.get_plan_summary()
        }


class AutoTradePlanner:
    """Intelligent trade planner for profit targets."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Market condition factors
        self.market_conditions = {
            'volatility': 'normal',  # low, normal, high
            'trend': 'neutral',      # bullish, bearish, neutral
            'volume': 'normal'       # low, normal, high
        }
        
        # Strategy preferences by market condition
        self.strategy_preferences = {
            ('high', 'bullish', 'high'): ['momentum_breakout', 'trend_following'],
            ('high', 'bearish', 'high'): ['short_squeeze', 'reversal'],
            ('low', 'neutral', 'normal'): ['range_trading', 'mean_reversion'],
            ('normal', 'bullish', 'normal'): ['ttm_squeeze', 'momentum_breakout'],
            ('normal', 'bearish', 'normal'): ['ttm_squeeze', 'reversal']
        }
        
        # Default risk parameters
        self.default_risk_per_trade = 0.02  # 2% per trade
        self.max_portfolio_risk = 0.10      # 10% total portfolio risk
        self.min_reward_risk_ratio = 2.0    # Minimum 2:1 reward:risk
    
    def create_profit_plan(self, target_profit: float, account_size: float,
                          max_risk_pct: float = 10.0, timeframe: str = "1D") -> TradePlan:
        """Create a complete trade plan to achieve target profit."""
        max_risk = account_size * (max_risk_pct / 100)
        plan = TradePlan(target_profit, max_risk, account_size)
        
        try:
            # Get available setups
            setups = self._get_available_setups(timeframe)
            if not setups:
                self.logger.warning("No TTM setups available for planning")
                return plan
            
            # Filter and rank setups
            viable_setups = self._filter_viable_setups(setups, target_profit, max_risk)
            
            # Build optimal portfolio
            self._build_optimal_portfolio(plan, viable_setups)
            
            # Validate plan
            self._validate_plan(plan)
            
            self.logger.info(f"Created trade plan: {len(plan.trades)} trades, "
                           f"${plan.expected_profit:.0f} expected profit")
            
        except Exception as e:
            self.logger.error(f"Error creating trade plan: {e}")
            plan.status = "failed"
        
        return plan
    
    def execute_plan(self, plan: TradePlan) -> Dict:
        """Execute a trade plan using automation engine."""
        try:
            from automation_engine import get_automation_engine
            from enhanced_order_execution import get_order_executor
            
            automation = get_automation_engine()
            executor = get_order_executor()
            
            plan.status = "executing"
            execution_results = []
            
            for trade in plan.trades:
                try:
                    # Execute bracket order
                    result = executor.execute_bracket_order(
                        symbol=trade['symbol'],
                        quantity=trade['position_size'],
                        side="buy",
                        entry_price=trade['entry_price'],
                        stop_loss_pct=self._calculate_stop_pct(trade),
                        take_profit_pct=self._calculate_target_pct(trade)
                    )
                    
                    execution_results.append({
                        'symbol': trade['symbol'],
                        'success': result['success'],
                        'order_id': result.get('order_id'),
                        'error': result.get('error')
                    })
                    
                    if result['success']:
                        self.logger.info(f"Executed trade: {trade['symbol']} {trade['position_size']} shares")
                    else:
                        self.logger.error(f"Failed to execute {trade['symbol']}: {result.get('error')}")
                
                except Exception as e:
                    self.logger.error(f"Error executing trade {trade['symbol']}: {e}")
                    execution_results.append({
                        'symbol': trade['symbol'],
                        'success': False,
                        'error': str(e)
                    })
            
            # Update plan status
            successful_trades = sum(1 for r in execution_results if r['success'])
            if successful_trades == len(plan.trades):
                plan.status = "completed"
            elif successful_trades > 0:
                plan.status = "partially_executed"
            else:
                plan.status = "failed"
            
            return {
                'success': successful_trades > 0,
                'executed_trades': successful_trades,
                'total_trades': len(plan.trades),
                'execution_results': execution_results,
                'plan_status': plan.status
            }
            
        except Exception as e:
            self.logger.error(f"Error executing plan: {e}")
            plan.status = "failed"
            return {'success': False, 'error': str(e)}
    
    def _get_available_setups(self, timeframe: str) -> List:
        """Get available TTM setups."""
        try:
            from unified_ttm_scanner import get_unified_scanner
            
            scanner = get_unified_scanner()
            setups = scanner.scan_all_opportunities(min_confidence=60.0)
            
            # Filter by timeframe
            return [setup for setup in setups if setup.timeframe == timeframe]
            
        except Exception as e:
            self.logger.error(f"Error getting setups: {e}")
            return []
    
    def _filter_viable_setups(self, setups: List, target_profit: float, max_risk: float) -> List:
        """Filter setups that can contribute to profit target."""
        viable_setups = []
        
        for setup in setups:
            # Calculate potential profit and risk
            potential_profit = self._estimate_profit_potential(setup, target_profit)
            required_risk = self._estimate_required_risk(setup, potential_profit)
            
            # Check if setup is viable
            if (potential_profit > 0 and 
                required_risk <= max_risk * 0.5 and  # Max 50% of total risk per trade
                setup.confidence_score >= 70):       # Minimum confidence
                
                viable_setups.append({
                    'setup': setup,
                    'potential_profit': potential_profit,
                    'required_risk': required_risk,
                    'efficiency': potential_profit / required_risk if required_risk > 0 else 0
                })
        
        # Sort by efficiency (profit per dollar risked)
        viable_setups.sort(key=lambda x: x['efficiency'], reverse=True)
        
        return viable_setups
    
    def _build_optimal_portfolio(self, plan: TradePlan, viable_setups: List):
        """Build optimal portfolio of trades."""
        remaining_profit = plan.target_profit
        remaining_risk = plan.max_risk
        
        for setup_data in viable_setups:
            if remaining_profit <= 0 or remaining_risk <= 0:
                break
            
            setup = setup_data['setup']
            
            # Calculate optimal position size
            target_profit_per_trade = min(remaining_profit * 0.6, setup_data['potential_profit'])
            risk_per_trade = min(remaining_risk * 0.4, setup_data['required_risk'])
            
            position_size = self._calculate_position_size(setup, target_profit_per_trade, risk_per_trade)
            
            if position_size > 0:
                # Calculate success probability based on setup quality
                success_prob = self._estimate_success_probability(setup)
                
                # Add trade to plan
                plan.add_trade(
                    setup=setup,
                    position_size=position_size,
                    risk_amount=risk_per_trade,
                    expected_return=target_profit_per_trade,
                    probability=success_prob
                )
                
                remaining_profit -= target_profit_per_trade
                remaining_risk -= risk_per_trade
                
                # Limit to 5 trades max for manageable portfolio
                if len(plan.trades) >= 5:
                    break
    
    def _estimate_profit_potential(self, setup, target_profit: float) -> float:
        """Estimate profit potential for a setup."""
        # Base potential on setup grade and confidence
        grade_multipliers = {"A+": 1.0, "A": 0.8, "B+": 0.6, "B": 0.4, "C+": 0.2, "C": 0.1}
        base_multiplier = grade_multipliers.get(setup.grade, 0.1)
        
        # Adjust for confidence
        confidence_factor = setup.confidence_score / 100
        
        # Estimate based on typical TTM moves (2-8% price movement)
        typical_move_pct = 0.04 * base_multiplier * confidence_factor
        estimated_profit = setup.current_price * typical_move_pct * 100  # Assume 100 shares base
        
        return min(estimated_profit, target_profit * 0.4)  # Cap at 40% of target
    
    def _estimate_required_risk(self, setup, potential_profit: float) -> float:
        """Estimate required risk for potential profit."""
        # Use 2:1 reward:risk ratio as baseline
        return potential_profit / self.min_reward_risk_ratio
    
    def _calculate_position_size(self, setup, target_profit: float, max_risk: float) -> int:
        """Calculate optimal position size."""
        # Estimate price movement needed for target profit
        estimated_move = target_profit / setup.current_price if setup.current_price > 0 else 0
        
        # Calculate shares needed
        if estimated_move > 0:
            shares_needed = target_profit / (setup.current_price * estimated_move)
            
            # Limit by risk
            max_shares_by_risk = max_risk / (setup.current_price * 0.02)  # 2% stop loss
            
            return int(min(shares_needed, max_shares_by_risk, 1000))  # Cap at 1000 shares
        
        return 0
    
    def _estimate_success_probability(self, setup) -> float:
        """Estimate success probability based on setup quality."""
        # Base probability on confidence score
        base_prob = setup.confidence_score / 100
        
        # Adjust for grade
        grade_adjustments = {"A+": 0.1, "A": 0.05, "B+": 0.0, "B": -0.05, "C+": -0.1, "C": -0.15}
        adjustment = grade_adjustments.get(setup.grade, 0)
        
        return max(0.3, min(0.9, base_prob + adjustment))  # Keep between 30-90%
    
    def _calculate_stop_pct(self, trade: Dict) -> float:
        """Calculate stop loss percentage for trade."""
        stop_price = trade['stop_loss']
        entry_price = trade['entry_price']
        return ((entry_price - stop_price) / entry_price) * 100
    
    def _calculate_target_pct(self, trade: Dict) -> float:
        """Calculate take profit percentage for trade."""
        target_price = trade['take_profit']
        entry_price = trade['entry_price']
        return ((target_price - entry_price) / entry_price) * 100
    
    def _validate_plan(self, plan: TradePlan):
        """Validate trade plan before execution."""
        summary = plan.get_plan_summary()
        
        # Check risk limits
        if summary['account_risk_pct'] > 15:  # 15% max account risk
            self.logger.warning(f"Plan exceeds risk limits: {summary['account_risk_pct']:.1f}%")
        
        # Check reward:risk ratio
        if summary['risk_reward_ratio'] < 1.5:
            self.logger.warning(f"Low reward:risk ratio: {summary['risk_reward_ratio']:.2f}")
        
        # Check plan confidence
        if summary['plan_confidence'] < 70:
            self.logger.warning(f"Low plan confidence: {summary['plan_confidence']:.1f}%")


# Global planner instance
_auto_planner = None

def get_auto_planner() -> AutoTradePlanner:
    """Get the global auto trade planner instance."""
    global _auto_planner
    if _auto_planner is None:
        _auto_planner = AutoTradePlanner()
    return _auto_planner


# Convenience function for chat integration
def make_profit_plan(target_amount: float, account_size: float = 25000, 
                    max_risk_pct: float = 8.0) -> str:
    """Create and optionally execute a profit plan."""
    try:
        planner = get_auto_planner()
        plan = planner.create_profit_plan(target_amount, account_size, max_risk_pct)
        
        summary = plan.get_plan_summary()
        
        response = f"💰 **PROFIT PLAN: ${target_amount:.0f} TARGET**\n\n"
        response += f"**📊 Plan Summary:**\n"
        response += f"• Expected Profit: ${summary['expected_profit']:.0f}\n"
        response += f"• Total Risk: ${summary['total_risk']:.0f} ({summary['account_risk_pct']:.1f}% of account)\n"
        response += f"• Reward:Risk Ratio: {summary['risk_reward_ratio']:.2f}:1\n"
        response += f"• Success Probability: {summary['success_probability']:.1f}%\n"
        response += f"• Plan Confidence: {summary['plan_confidence']:.1f}%\n"
        response += f"• Number of Trades: {summary['num_trades']}\n\n"
        
        if plan.trades:
            response += f"**🎯 Planned Trades:**\n"
            for i, trade in enumerate(plan.trades, 1):
                response += f"{i}. {trade['symbol']}: {trade['position_size']} shares @ ${trade['entry_price']:.2f}\n"
                response += f"   Stop: ${trade['stop_loss']:.2f} | Target: ${trade['take_profit']:.2f}\n"
                response += f"   Risk: ${trade['risk_amount']:.0f} | Expected: ${trade['expected_return']:.0f}\n\n"
            
            response += f"💡 **Ready to execute?** Use 'execute profit plan' to start trading!"
        else:
            response += f"⚠️ **No viable trades found** for ${target_amount:.0f} target.\n"
            response += f"Try a smaller target or check market conditions."
        
        return response
        
    except Exception as e:
        return f"❌ Error creating profit plan: {str(e)}"


if __name__ == "__main__":
    # Test the auto trade planner
    planner = AutoTradePlanner()
    
    print("💰 Testing Auto Trade Planner")
    print("=" * 40)
    
    # Test profit plan creation
    plan = planner.create_profit_plan(target_profit=200, account_size=10000, max_risk_pct=5.0)
    
    summary = plan.get_plan_summary()
    print(f"✅ Created plan for $200 target:")
    print(f"   Expected profit: ${summary['expected_profit']:.0f}")
    print(f"   Total risk: ${summary['total_risk']:.0f}")
    print(f"   Number of trades: {summary['num_trades']}")
    print(f"   Plan confidence: {summary['plan_confidence']:.1f}%")
    
    # Test convenience function
    result = make_profit_plan(100, 10000, 5.0)
    print(f"✅ Convenience function: {len(result)} characters")
    
    print("💰 Auto trade planner ready!")
