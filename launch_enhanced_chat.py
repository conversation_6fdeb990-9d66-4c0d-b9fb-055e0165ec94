#!/usr/bin/env python3
"""
Launch Enhanced Chat Interface
Your existing desktop app now with complete Incite AI features!
"""
import os
import sys
import subprocess
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are available"""
    print("🔍 Checking dependencies...")
    
    missing = []
    optional_missing = []
    
    # Required dependencies
    try:
        import tkinter
        print("   ✅ tkinter (GUI framework)")
    except ImportError:
        missing.append("tkinter")
        print("   ❌ tkinter")
    
    # Optional dependencies for enhanced features
    try:
        from PIL import Image
        print("   ✅ PIL/Pillow (image processing)")
    except ImportError:
        optional_missing.append("pillow")
        print("   ⚠️  PIL/Pillow (for chart upload)")
    
    try:
        import openai
        print("   ✅ OpenAI (AI vision)")
    except ImportError:
        optional_missing.append("openai")
        print("   ⚠️  OpenAI (for AI vision analysis)")
    
    if missing:
        print(f"\n❌ Missing required packages: {', '.join(missing)}")
        return False
    
    if optional_missing:
        print(f"\n💡 Optional packages for full features:")
        for pkg in optional_missing:
            print(f"   pip install {pkg}")
    
    return True

def setup_environment():
    """Setup environment for enhanced features"""
    # Add current directory to Python path
    current_dir = Path(__file__).parent.absolute()
    
    paths_to_add = [
        str(current_dir),
        str(current_dir / 'core'),
        str(current_dir / 'gui'),
        str(current_dir / 'scanners'),
        str(current_dir / 'trading')
    ]
    
    for path in paths_to_add:
        if path not in sys.path:
            sys.path.insert(0, path)
    
    # Create data directory
    os.makedirs('data', exist_ok=True)

def test_incite_features():
    """Test Incite AI features"""
    print("🧪 Testing Incite AI features...")
    
    try:
        # Test Deep Search
        from core.simple_deep_search import SimpleDeepSearch
        search = SimpleDeepSearch("data/test_search.db")
        
        # Add test data
        search.add_trade_decision("AAPL", "BUY", "Strong TTM squeeze", {"confidence": 92})
        search.add_scanner_result("NVDA", "A", 88.5, "Volume surge", {"timeframe": "15min"})
        
        # Test search
        results = search.search("AAPL", limit=1)
        if results:
            print("   ✅ Deep Search working")
        else:
            print("   ⚠️  Deep Search - no results")
        
    except Exception as e:
        print(f"   ⚠️  Deep Search error: {e}")
    
    try:
        # Test Chart Analyzer
        from core.chart_vision_analyzer import ChartVisionAnalyzer
        from PIL import Image
        
        analyzer = ChartVisionAnalyzer()
        test_image = Image.new('RGB', (800, 600), color='lightblue')
        analysis = analyzer.analyze_chart(test_image, "TEST")
        
        if analysis.symbol == "TEST":
            print("   ✅ Chart Analyzer working")
        else:
            print("   ⚠️  Chart Analyzer - unexpected result")
            
    except Exception as e:
        print(f"   ⚠️  Chart Analyzer error: {e}")
    
    try:
        # Test Endpoints
        from core.incite_ai_endpoints import InciteAIEndpoints
        
        endpoints = InciteAIEndpoints()
        status = endpoints.get_system_status()
        
        if status.interface_status:
            print("   ✅ Incite AI Endpoints working")
        else:
            print("   ⚠️  Incite AI Endpoints - no status")
            
    except Exception as e:
        print(f"   ⚠️  Incite AI Endpoints error: {e}")

def launch_enhanced_interface():
    """Launch the enhanced desktop interface"""
    
    print("🚀 LAUNCHING ENHANCED DESKTOP INTERFACE")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        print("\n❌ Cannot launch - missing required dependencies")
        return False
    
    # Setup environment
    setup_environment()
    
    # Test features
    test_incite_features()
    
    # Check if interface file exists
    interface_file = Path('gui/tkinter_trading_interface.py')
    
    if not interface_file.exists():
        print(f"❌ Interface file not found: {interface_file}")
        print("💡 Make sure you're running from the project root directory")
        return False
    
    print("\n🎨 Starting enhanced desktop interface...")
    print("🎯 New features: Intent Detection, Chart Upload, Deep Search")
    
    try:
        # Launch the interface
        cmd = [sys.executable, str(interface_file)]
        
        print(f"🔧 Command: {' '.join(cmd)}")
        
        # Start the process
        process = subprocess.Popen(cmd)
        
        print("✅ Enhanced desktop interface launched successfully!")
        print()
        print("🖥️  **YOUR ENHANCED DESKTOP INTERFACE IS NOW RUNNING!**")
        print()
        print("🎨 **NEW INCITE AI FEATURES IN YOUR CHAT:**")
        print("   🎯 Intent Detection - AI understands what you want")
        print("   📈 Chart Upload - Upload Think or Swim screenshots")
        print("   🔍 Deep Search - Search all your trading data")
        print("   💬 Enhanced AI - Smart responses based on intent")
        print("   📊 System Status - Real-time system awareness")
        print()
        print("📋 **HOW TO USE NEW FEATURES:**")
        print()
        print("   1️⃣ **Intent Detection (Automatic):**")
        print("      • Type any message in chat")
        print("      • See intent detection below your message")
        print("      • AI responds intelligently based on intent")
        print()
        print("   2️⃣ **Chart Upload:**")
        print("      • Look for '📈 Upload Chart' button in chat")
        print("      • Click to upload Think or Swim screenshots")
        print("      • Get instant AI vision analysis!")
        print()
        print("   3️⃣ **Deep Search:**")
        print("      • Look for '🔍 Deep Search' button in chat")
        print("      • Search all your trading decisions and data")
        print("      • Get instant results with relevance scoring")
        print()
        print("   4️⃣ **Quick Commands:**")
        print("      • '📊 System Status' - Get real-time status")
        print("      • '🎯 Best Setups' - Get top TTM opportunities")
        print()
        print("⚡ **QUICK TESTS TO TRY:**")
        print("   💬 Chat: 'What's happening in my system right now?'")
        print("   🎯 Intent: System Status (will be detected automatically)")
        print("   📈 Upload: Any Think or Swim chart screenshot")
        print("   🔍 Search: 'TTM squeeze opportunities'")
        print("   💡 Ask: 'Is buying AAPL a good idea?'")
        print()
        print("🏆 **YOU NOW HAVE INCITE AI FEATURES IN YOUR DESKTOP APP!**")
        print("🎉 **Better than Incite AI at no monthly cost!**")
        
        # Wait for process to complete
        try:
            process.wait()
        except KeyboardInterrupt:
            print("\n🛑 Shutting down interface...")
            process.terminate()
            process.wait()
            print("✅ Interface stopped")
        
        return True
        
    except Exception as e:
        print(f"❌ Error launching interface: {e}")
        return False

def show_help():
    """Show help information"""
    print("🎨 ENHANCED DESKTOP INTERFACE LAUNCHER")
    print("=" * 45)
    print()
    print("🎯 **DESCRIPTION:**")
    print("   Launches your existing desktop trading interface")
    print("   now enhanced with complete Incite AI features!")
    print()
    print("✨ **NEW FEATURES ADDED TO CHAT:**")
    print("   🎯 Intent Detection - AI understands your intent")
    print("   📈 Chart Upload - Upload Think or Swim charts")
    print("   🔍 Deep Search - Search all trading data")
    print("   💬 Enhanced AI - Smart responses based on intent")
    print("   📊 System Status - Real-time system awareness")
    print()
    print("🎨 **INCITE AI FEATURES:**")
    print("   • Professional intent detection")
    print("   • Chart upload with AI vision analysis")
    print("   • Deep Search through all trading decisions")
    print("   • Smart AI responses based on what you ask")
    print("   • Real-time system status and awareness")
    print()
    print("🚀 **USAGE:**")
    print("   python launch_enhanced_chat.py")
    print()
    print("📋 **REQUIREMENTS:**")
    print("   • Python 3.8+")
    print("   • tkinter (usually included)")
    print("   • PIL/Pillow for chart upload (optional)")
    print("   • OpenAI for AI vision (optional)")
    print()
    print("💡 **TIPS:**")
    print("   • Look for new buttons in the chat interface")
    print("   • Try uploading chart screenshots")
    print("   • Use the Deep Search functionality")
    print("   • Notice intent detection in chat")

def main():
    """Main launcher function"""
    
    if len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h', 'help']:
        show_help()
        return
    
    print("🎨 ENHANCED DESKTOP INTERFACE LAUNCHER")
    print("🖥️  Adding Complete Incite AI Features to Your Chat")
    print("=" * 60)
    
    success = launch_enhanced_interface()
    
    if success:
        print("\n🎉 **LAUNCH SUCCESSFUL!**")
        print("Your desktop chat now has complete Incite AI features!")
        print("Enjoy the most advanced trading interface ever built! 🏆")
    else:
        print("\n❌ **LAUNCH FAILED**")
        print("Please check the error messages above.")
        print("\nFor help: python launch_enhanced_chat.py --help")

if __name__ == "__main__":
    main()
