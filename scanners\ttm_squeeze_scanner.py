"""TTM Squeeze Scanner - Advanced momentum detection with A-F grading system.

This module implements the famous TTM Squeeze indicator that combines:
- Bollinger Bands (20-period, 2 standard deviations)
- Keltner Channels (20-period, 1.5 ATR)
- Momentum oscillator for directional bias
- A-F grading system for signal quality
- Multi-symbol scanning capabilities

The squeeze occurs when Bollinger Bands are inside Keltner Channels,
indicating low volatility that often precedes explosive moves.
"""
from __future__ import annotations

import statistics
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum
import math

import requests
from config import get_api_key
from logger_util import info, warning

# FMP API endpoints
FMP_BASE = "https://financialmodelingprep.com/api/v3"


class SqueezeStatus(Enum):
    """TTM Squeeze status."""
    SQUEEZE_ON = "squeeze_on"
    SQUEEZE_OFF = "squeeze_off"
    NO_SQUEEZE = "no_squeeze"


class MomentumDirection(Enum):
    """Momentum direction."""
    BULLISH = "bullish"
    BEARISH = "bearish"
    NEUTRAL = "neutral"


@dataclass
class SqueezeSignal:
    """TTM Squeeze signal with all relevant data."""
    symbol: str
    squeeze_status: SqueezeStatus
    momentum_direction: MomentumDirection
    momentum_value: float
    bollinger_upper: float
    bollinger_lower: float
    keltner_upper: float
    keltner_lower: float
    current_price: float
    volume: int
    grade: str  # A-F grading
    confidence: float  # 0-1
    breakout_probability: float  # 0-1
    recommendation: str
    entry_price: float
    stop_loss: float
    target_price: float


class TTMSqueezeScanner:
    """TTM Squeeze Scanner implementation."""

    def __init__(self):
        self.api_key = get_api_key("FMP_API_KEY")
        self.default_symbols = [
            "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "META", "NVDA", "NFLX",
            "SPY", "QQQ", "IWM", "DIA", "XLF", "XLE", "XLK", "XLV",
            "AMD", "INTC", "CRM", "ORCL", "ADBE", "PYPL", "SHOP", "SQ",
            "BA", "CAT", "JPM", "GS", "WMT", "HD", "DIS", "V",
            "JNJ", "PFE", "KO", "PEP", "MCD", "NKE", "SBUX", "COST",
            "BABA", "JD", "NIO", "PLTR", "COIN", "HOOD", "SOFI", "UPST"
        ]

    def _get_historical_data(self, symbol: str, period: int = 50) -> List[Dict]:
        """Get historical price data for calculations."""
        url = f"{FMP_BASE}/historical-price-full/{symbol}?limit={period}&apikey={self.api_key}"
        try:
            response = requests.get(url, timeout=15)
            data = response.json()
            return data.get("historical", [])[::-1]  # Reverse to get chronological order
        except Exception as exc:
            warning("Failed to fetch historical data", symbol=symbol, error=str(exc))
            return []

    def _calculate_bollinger_bands(self, prices: List[float], period: int = 20, std_dev: float = 2.0) -> tuple:
        """Calculate Bollinger Bands."""
        if len(prices) < period:
            return None, None, None

        # Simple Moving Average
        sma = statistics.mean(prices[-period:])

        # Standard Deviation
        variance = sum((x - sma) ** 2 for x in prices[-period:]) / period
        std = math.sqrt(variance)

        upper_band = sma + (std_dev * std)
        lower_band = sma - (std_dev * std)

        return upper_band, sma, lower_band

    def _calculate_keltner_channels(self, highs: List[float], lows: List[float],
                                  closes: List[float], period: int = 20, multiplier: float = 1.5) -> tuple:
        """Calculate Keltner Channels."""
        if len(closes) < period:
            return None, None, None

        # Exponential Moving Average of close prices
        ema = self._calculate_ema(closes, period)

        # Average True Range
        atr = self._calculate_atr(highs, lows, closes, period)

        upper_channel = ema + (multiplier * atr)
        lower_channel = ema - (multiplier * atr)

        return upper_channel, ema, lower_channel

    def _calculate_ema(self, prices: List[float], period: int) -> float:
        """Calculate Exponential Moving Average."""
        if len(prices) < period:
            return prices[-1] if prices else 0

        multiplier = 2 / (period + 1)
        ema = prices[0]

        for price in prices[1:]:
            ema = (price * multiplier) + (ema * (1 - multiplier))

        return ema

    def _calculate_atr(self, highs: List[float], lows: List[float],
                      closes: List[float], period: int = 14) -> float:
        """Calculate Average True Range."""
        if len(closes) < 2:
            return 0

        true_ranges = []
        for i in range(1, len(closes)):
            high_low = highs[i] - lows[i]
            high_close_prev = abs(highs[i] - closes[i-1])
            low_close_prev = abs(lows[i] - closes[i-1])

            true_range = max(high_low, high_close_prev, low_close_prev)
            true_ranges.append(true_range)

        # Return average of last 'period' true ranges
        return statistics.mean(true_ranges[-period:]) if true_ranges else 0

    def _calculate_momentum(self, closes: List[float], period: int = 20) -> float:
        """Calculate momentum oscillator."""
        if len(closes) < period + 1:
            return 0

        # Linear regression slope as momentum indicator
        x_values = list(range(period))
        y_values = closes[-period:]

        n = len(x_values)
        sum_x = sum(x_values)
        sum_y = sum(y_values)
        sum_xy = sum(x * y for x, y in zip(x_values, y_values))
        sum_x2 = sum(x * x for x in x_values)

        # Calculate slope (momentum)
        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)

        return slope

    def _analyze_squeeze(self, symbol: str) -> Optional[SqueezeSignal]:
        """Analyze TTM Squeeze for a single symbol."""
        historical_data = self._get_historical_data(symbol, 50)

        if len(historical_data) < 25:
            warning("Insufficient data for squeeze analysis", symbol=symbol)
            return None

        # Extract price data
        closes = [float(d["close"]) for d in historical_data]
        highs = [float(d["high"]) for d in historical_data]
        lows = [float(d["low"]) for d in historical_data]
        volumes = [int(d["volume"]) for d in historical_data]

        current_price = closes[-1]
        current_volume = volumes[-1]

        # Calculate indicators
        bb_upper, bb_middle, bb_lower = self._calculate_bollinger_bands(closes)
        kc_upper, kc_middle, kc_lower = self._calculate_keltner_channels(highs, lows, closes)

        if None in [bb_upper, bb_lower, kc_upper, kc_lower]:
            warning("Failed to calculate indicators", symbol=symbol)
            return None

        # Determine squeeze status
        squeeze_status = SqueezeStatus.NO_SQUEEZE
        if bb_upper <= kc_upper and bb_lower >= kc_lower:
            squeeze_status = SqueezeStatus.SQUEEZE_ON
        elif bb_upper > kc_upper or bb_lower < kc_lower:
            # Check if squeeze just ended
            prev_closes = closes[:-1]
            prev_bb_upper, _, prev_bb_lower = self._calculate_bollinger_bands(prev_closes)
            prev_kc_upper, _, prev_kc_lower = self._calculate_keltner_channels(highs[:-1], lows[:-1], prev_closes)

            if (prev_bb_upper <= prev_kc_upper and prev_bb_lower >= prev_kc_lower):
                squeeze_status = SqueezeStatus.SQUEEZE_OFF

        # Calculate momentum
        momentum = self._calculate_momentum(closes)
        momentum_direction = MomentumDirection.NEUTRAL
        if momentum > 0.1:
            momentum_direction = MomentumDirection.BULLISH
        elif momentum < -0.1:
            momentum_direction = MomentumDirection.BEARISH

        # Grade the signal (A-F)
        grade, confidence = self._grade_signal(squeeze_status, momentum_direction, current_volume, volumes)

        # Calculate breakout probability
        breakout_prob = self._calculate_breakout_probability(squeeze_status, momentum, current_volume, volumes)

        # Generate recommendation and targets
        recommendation, entry_price, stop_loss, target_price = self._generate_recommendation(
            current_price, squeeze_status, momentum_direction, bb_upper, bb_lower, kc_upper, kc_lower
        )

        return SqueezeSignal(
            symbol=symbol,
            squeeze_status=squeeze_status,
            momentum_direction=momentum_direction,
            momentum_value=momentum,
            bollinger_upper=bb_upper,
            bollinger_lower=bb_lower,
            keltner_upper=kc_upper,
            keltner_lower=kc_lower,
            current_price=current_price,
            volume=current_volume,
            grade=grade,
            confidence=confidence,
            breakout_probability=breakout_prob,
            recommendation=recommendation,
            entry_price=entry_price,
            stop_loss=stop_loss,
            target_price=target_price
        )

    def _grade_signal(self, squeeze_status: SqueezeStatus, momentum_direction: MomentumDirection,
                     current_volume: int, volumes: List[int]) -> tuple:
        """Grade the signal quality A-F."""
        score = 0

        # Squeeze status scoring
        if squeeze_status == SqueezeStatus.SQUEEZE_OFF:
            score += 40  # Highest score for squeeze release
        elif squeeze_status == SqueezeStatus.SQUEEZE_ON:
            score += 25  # Good score for active squeeze
        else:
            score += 10  # Lower score for no squeeze

        # Momentum direction scoring
        if momentum_direction != MomentumDirection.NEUTRAL:
            score += 25  # Clear directional bias
        else:
            score += 10  # Neutral momentum

        # Volume scoring
        avg_volume = statistics.mean(volumes[-10:]) if len(volumes) >= 10 else current_volume
        volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1

        if volume_ratio > 1.5:
            score += 25  # High volume
        elif volume_ratio > 1.2:
            score += 15  # Above average volume
        else:
            score += 5   # Normal/low volume

        # Additional quality factors
        if squeeze_status == SqueezeStatus.SQUEEZE_OFF and momentum_direction != MomentumDirection.NEUTRAL:
            score += 10  # Bonus for squeeze release with momentum

        # Convert score to grade
        if score >= 85:
            grade = "A"
        elif score >= 75:
            grade = "B"
        elif score >= 65:
            grade = "C"
        elif score >= 55:
            grade = "D"
        else:
            grade = "F"

        confidence = min(score / 100, 1.0)

        return grade, confidence

    def _calculate_breakout_probability(self, squeeze_status: SqueezeStatus, momentum: float,
                                      current_volume: int, volumes: List[int]) -> float:
        """Calculate probability of breakout."""
        prob = 0.3  # Base probability

        if squeeze_status == SqueezeStatus.SQUEEZE_OFF:
            prob += 0.4  # High probability for squeeze release
        elif squeeze_status == SqueezeStatus.SQUEEZE_ON:
            prob += 0.2  # Moderate probability for active squeeze

        # Momentum factor
        if abs(momentum) > 0.2:
            prob += 0.2
        elif abs(momentum) > 0.1:
            prob += 0.1

        # Volume factor
        avg_volume = statistics.mean(volumes[-5:]) if len(volumes) >= 5 else current_volume
        volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1

        if volume_ratio > 1.5:
            prob += 0.1

        return min(prob, 0.95)  # Cap at 95%

    def _generate_recommendation(self, current_price: float, squeeze_status: SqueezeStatus,
                               momentum_direction: MomentumDirection, bb_upper: float, bb_lower: float,
                               kc_upper: float, kc_lower: float) -> tuple:
        """Generate trading recommendation with entry, stop, and target."""

        if squeeze_status == SqueezeStatus.SQUEEZE_OFF:
            if momentum_direction == MomentumDirection.BULLISH:
                recommendation = f"BULLISH BREAKOUT: Buy above ${current_price:.2f}"
                entry_price = current_price * 1.005  # 0.5% above current
                stop_loss = bb_lower * 0.98  # 2% below Bollinger lower
                target_price = current_price * 1.08  # 8% target
            elif momentum_direction == MomentumDirection.BEARISH:
                recommendation = f"BEARISH BREAKOUT: Short below ${current_price:.2f}"
                entry_price = current_price * 0.995  # 0.5% below current
                stop_loss = bb_upper * 1.02  # 2% above Bollinger upper
                target_price = current_price * 0.92  # 8% target down
            else:
                recommendation = f"SQUEEZE RELEASED: Watch for direction at ${current_price:.2f}"
                entry_price = current_price
                stop_loss = current_price * 0.95  # 5% stop
                target_price = current_price * 1.05  # 5% target

        elif squeeze_status == SqueezeStatus.SQUEEZE_ON:
            recommendation = f"SQUEEZE ACTIVE: Prepare for breakout around ${current_price:.2f}"
            entry_price = current_price
            stop_loss = (bb_upper + bb_lower) / 2 * 0.97  # 3% below middle
            target_price = current_price * 1.06  # 6% target

        else:
            recommendation = f"NO SQUEEZE: Normal trading at ${current_price:.2f}"
            entry_price = current_price
            stop_loss = current_price * 0.95  # 5% stop
            target_price = current_price * 1.05  # 5% target

        return recommendation, entry_price, stop_loss, target_price

    def scan_symbols(self, symbols: List[str] = None, min_grade: str = "C",
                    max_results: int = 10) -> List[SqueezeSignal]:
        """Scan multiple symbols for TTM Squeeze opportunities."""
        if symbols is None:
            symbols = self.default_symbols

        info("🔍 Scanning TTM Squeeze opportunities", symbols_count=len(symbols), min_grade=min_grade)

        signals = []
        grade_values = {"A": 5, "B": 4, "C": 3, "D": 2, "F": 1}
        min_grade_value = grade_values.get(min_grade, 3)

        for symbol in symbols:
            try:
                signal = self._analyze_squeeze(symbol)
                if signal and grade_values.get(signal.grade, 0) >= min_grade_value:
                    signals.append(signal)
            except Exception as exc:
                warning("Squeeze analysis failed", symbol=symbol, error=str(exc))
                continue

        # Sort by grade and confidence
        signals.sort(key=lambda s: (grade_values.get(s.grade, 0), s.confidence), reverse=True)

        return signals[:max_results]

    def get_squeeze_status(self, symbol: str) -> Dict[str, Any]:
        """Get detailed squeeze status for a single symbol."""
        signal = self._analyze_squeeze(symbol)

        if not signal:
            return {
                "error": f"Unable to analyze squeeze for {symbol}",
                "suggestion": "Check if symbol exists and has sufficient trading history"
            }

        return {
            "symbol": signal.symbol,
            "squeeze_status": signal.squeeze_status.value,
            "momentum_direction": signal.momentum_direction.value,
            "grade": signal.grade,
            "confidence": signal.confidence,
            "breakout_probability": signal.breakout_probability,
            "recommendation": signal.recommendation,
            "current_price": signal.current_price,
            "entry_price": signal.entry_price,
            "stop_loss": signal.stop_loss,
            "target_price": signal.target_price,
            "bollinger_bands": {
                "upper": signal.bollinger_upper,
                "lower": signal.bollinger_lower
            },
            "keltner_channels": {
                "upper": signal.keltner_upper,
                "lower": signal.keltner_lower
            },
            "volume": signal.volume,
            "momentum_value": signal.momentum_value
        }


# ============================================================================
# CONVENIENCE FUNCTIONS FOR CHAT INTEGRATION
# ============================================================================

def scan_ttm_squeeze_opportunities(symbols: List[str] = None, min_grade: str = "C",
                                 max_results: int = 10) -> Dict[str, Any]:
    """Convenience function for chat integration."""
    try:
        scanner = TTMSqueezeScanner()
        signals = scanner.scan_symbols(symbols, min_grade, max_results)

        if not signals:
            return {
                "message": f"No TTM Squeeze opportunities found with grade {min_grade} or better",
                "suggestion": "Try lowering the minimum grade or checking different symbols"
            }

        results = {
            "scan_summary": {
                "symbols_scanned": len(symbols) if symbols else len(scanner.default_symbols),
                "opportunities_found": len(signals),
                "min_grade_filter": min_grade
            },
            "top_opportunities": []
        }

        for signal in signals:
            results["top_opportunities"].append({
                "symbol": signal.symbol,
                "grade": signal.grade,
                "squeeze_status": signal.squeeze_status.value,
                "momentum_direction": signal.momentum_direction.value,
                "confidence": signal.confidence,
                "breakout_probability": signal.breakout_probability,
                "recommendation": signal.recommendation,
                "current_price": signal.current_price,
                "entry_price": signal.entry_price,
                "target_price": signal.target_price,
                "stop_loss": signal.stop_loss
            })

        return results

    except Exception as exc:
        warning("TTM Squeeze scan failed", error=str(exc))
        return {
            "error": f"TTM Squeeze scan failed: {str(exc)}",
            "suggestion": "Check market hours and data availability"
        }


def get_ttm_squeeze_analysis(symbol: str) -> Dict[str, Any]:
    """Get detailed TTM Squeeze analysis for a single symbol."""
    try:
        scanner = TTMSqueezeScanner()
        return scanner.get_squeeze_status(symbol)

    except Exception as exc:
        warning("TTM Squeeze analysis failed", symbol=symbol, error=str(exc))
        return {
            "error": f"TTM Squeeze analysis failed for {symbol}: {str(exc)}",
            "suggestion": "Check if symbol exists and has sufficient trading history"
        }
