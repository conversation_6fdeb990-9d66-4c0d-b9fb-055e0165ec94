#!/usr/bin/env python3
"""
Enhanced TTM Squeeze Scanner - Advanced Pattern Recognition System
Implements sophisticated algorithms to identify only high-quality TTM squeeze setups
with clear breakout signals and pronounced patterns.
"""

import asyncio
import aiohttp
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
import requests
import time
import os

try:
    from core.logger_util import info, warning, error
except ImportError:
    def info(msg): print(f"ℹ️ {msg}")
    def warning(msg): print(f"⚠️ {msg}")
    def error(msg): print(f"❌ {msg}")

try:
    from config import get_api_key
    FMP_API_KEY = get_api_key('FMP_API_KEY')
except ImportError:
    FMP_API_KEY = os.getenv('FMP_API_KEY', 'demo')


@dataclass
class EnhancedTTMSetup:
    """Enhanced TTM Setup with comprehensive pattern analysis."""
    symbol: str
    timeframe: str
    timestamp: datetime
    
    # Price data
    price: float
    volume: float
    avg_volume_20: float
    volume_ratio: float
    
    # Technical indicators
    bb_upper: float
    bb_lower: float
    bb_width: float
    kc_upper: float
    kc_lower: float
    kc_width: float
    squeeze_ratio: float
    
    # EMAs and momentum
    ema5: float
    ema8: float
    ema21: float
    momentum: float
    momentum_change: float
    momentum_acceleration: float
    
    # Pattern recognition scores
    compression_score: float
    breakout_probability: float
    momentum_divergence_score: float
    volume_confirmation_score: float
    directional_bias_score: float
    pattern_quality_score: float
    
    # Setup classification
    grade: str
    confidence: float
    criteria_count: int
    criteria_details: Dict[str, bool]
    
    # Trade levels
    entry_price: float
    stop_loss: float
    target_price: float
    risk_reward_ratio: float
    
    # Additional data
    sector: str = ""
    market_cap: float = 0.0
    missing_criteria: List[str] = None
    
    def __post_init__(self):
        if self.missing_criteria is None:
            self.missing_criteria = []


class EnhancedPatternRecognition:
    """Advanced pattern recognition algorithms for TTM squeeze analysis."""
    
    @staticmethod
    def analyze_compression_quality(bb_width: np.ndarray, kc_width: np.ndarray, 
                                  lookback: int = 20) -> float:
        """
        Analyze the quality of Bollinger Band compression inside Keltner Channels.
        Returns a score from 0-1 indicating compression quality.
        """
        try:
            if len(bb_width) < lookback or len(kc_width) < lookback:
                return 0.0
            
            # Calculate compression ratio
            recent_bb_width = bb_width[-lookback:]
            recent_kc_width = kc_width[-lookback:]
            
            # Avoid division by zero
            valid_ratios = recent_bb_width[recent_kc_width > 0] / recent_kc_width[recent_kc_width > 0]
            
            if len(valid_ratios) == 0:
                return 0.0
            
            # Current compression ratio
            current_ratio = valid_ratios[-1] if len(valid_ratios) > 0 else 1.0
            
            # Historical compression percentile
            compression_percentile = np.percentile(valid_ratios, 20)  # 20th percentile = tight compression
            
            # Score based on how compressed current state is
            compression_score = max(0, 1 - (current_ratio / compression_percentile))
            
            # Bonus for sustained compression
            sustained_compression = np.sum(valid_ratios[-10:] < compression_percentile) / 10
            
            # Final score with sustained compression bonus
            final_score = min(1.0, compression_score + (sustained_compression * 0.3))
            
            return final_score
            
        except Exception as e:
            warning(f"Error analyzing compression quality: {e}")
            return 0.0
    
    @staticmethod
    def detect_momentum_divergence(prices: np.ndarray, momentum: np.ndarray, 
                                 lookback: int = 10) -> float:
        """
        Detect momentum divergence patterns that indicate potential breakouts.
        Returns a score from 0-1 indicating divergence strength.
        """
        try:
            if len(prices) < lookback or len(momentum) < lookback:
                return 0.0
            
            recent_prices = prices[-lookback:]
            recent_momentum = momentum[-lookback:]
            
            # Calculate price trend
            price_slope = np.polyfit(range(len(recent_prices)), recent_prices, 1)[0]
            
            # Calculate momentum trend
            momentum_slope = np.polyfit(range(len(recent_momentum)), recent_momentum, 1)[0]
            
            # Detect bullish divergence (price down, momentum up)
            if price_slope < 0 and momentum_slope > 0:
                divergence_strength = abs(momentum_slope) / (abs(price_slope) + 1e-6)
                return min(1.0, divergence_strength * 0.5)
            
            # Detect momentum acceleration (both trending same direction but momentum accelerating)
            if price_slope > 0 and momentum_slope > 0:
                momentum_acceleration = momentum_slope / (abs(price_slope) + 1e-6)
                return min(1.0, momentum_acceleration * 0.3)
            
            return 0.0
            
        except Exception as e:
            warning(f"Error detecting momentum divergence: {e}")
            return 0.0
    
    @staticmethod
    def analyze_volume_confirmation(volume: np.ndarray, avg_volume: float, 
                                  price_change: float) -> float:
        """
        Analyze volume confirmation for breakout potential.
        Returns a score from 0-1 indicating volume confirmation strength.
        """
        try:
            if len(volume) < 5:
                return 0.0
            
            current_volume = volume[-1]
            recent_avg_volume = np.mean(volume[-5:])
            
            # Volume ratio vs average
            volume_ratio = current_volume / (avg_volume + 1e-6)
            
            # Volume trend (increasing volume)
            volume_trend = np.polyfit(range(len(volume[-5:])), volume[-5:], 1)[0]
            volume_trend_score = max(0, min(1, volume_trend / avg_volume))
            
            # Volume confirmation based on price movement
            if abs(price_change) > 0.01:  # Significant price movement
                volume_price_correlation = volume_ratio * abs(price_change)
                volume_confirmation = min(1.0, volume_price_correlation)
            else:
                volume_confirmation = min(1.0, volume_ratio * 0.5)
            
            # Combine scores
            final_score = (volume_confirmation * 0.7) + (volume_trend_score * 0.3)
            
            return min(1.0, final_score)
            
        except Exception as e:
            warning(f"Error analyzing volume confirmation: {e}")
            return 0.0
    
    @staticmethod
    def calculate_directional_bias(ema5: np.ndarray, ema8: np.ndarray, 
                                 ema21: np.ndarray, momentum: np.ndarray) -> float:
        """
        Calculate directional bias score based on EMA alignment and momentum.
        Returns a score from 0-1 indicating directional clarity.
        """
        try:
            if len(ema5) < 3 or len(ema8) < 3 or len(ema21) < 3 or len(momentum) < 3:
                return 0.0
            
            # Current EMA values
            current_ema5 = ema5[-1]
            current_ema8 = ema8[-1]
            current_ema21 = ema21[-1]
            current_momentum = momentum[-1]
            
            # EMA alignment score
            if current_ema5 > current_ema8 > current_ema21:  # Bullish alignment
                ema_alignment_score = 1.0
            elif current_ema5 < current_ema8 < current_ema21:  # Bearish alignment
                ema_alignment_score = 1.0
            else:
                # Partial alignment
                ema_alignment_score = 0.5
            
            # EMA slope consistency
            ema5_slope = ema5[-1] - ema5[-3]
            ema8_slope = ema8[-1] - ema8[-3]
            ema21_slope = ema21[-1] - ema21[-3]
            
            # Check if slopes are in same direction
            slopes = [ema5_slope, ema8_slope, ema21_slope]
            slope_consistency = 1.0 if all(s > 0 for s in slopes) or all(s < 0 for s in slopes) else 0.5
            
            # Momentum confirmation
            momentum_direction = 1.0 if current_momentum > 0 else -1.0
            ema_direction = 1.0 if current_ema5 > current_ema21 else -1.0
            momentum_confirmation = 1.0 if momentum_direction == ema_direction else 0.3
            
            # Final directional bias score
            directional_score = (ema_alignment_score * 0.4) + (slope_consistency * 0.3) + (momentum_confirmation * 0.3)
            
            return min(1.0, directional_score)
            
        except Exception as e:
            warning(f"Error calculating directional bias: {e}")
            return 0.0
    
    @staticmethod
    def calculate_breakout_probability(compression_score: float, momentum_score: float,
                                     volume_score: float, directional_score: float,
                                     squeeze_duration: int) -> float:
        """
        Calculate overall breakout probability based on multiple factors.
        Returns a probability from 0-1.
        """
        try:
            # Base probability from individual scores
            base_probability = (compression_score * 0.3 + 
                              momentum_score * 0.25 + 
                              volume_score * 0.25 + 
                              directional_score * 0.2)
            
            # Squeeze duration bonus (longer squeezes tend to have bigger breakouts)
            duration_bonus = min(0.2, squeeze_duration * 0.01)
            
            # Final probability
            final_probability = min(1.0, base_probability + duration_bonus)
            
            return final_probability
            
        except Exception as e:
            warning(f"Error calculating breakout probability: {e}")
            return 0.0


class EnhancedTTMSqueezeScanner:
    """Enhanced TTM Squeeze Scanner with sophisticated pattern recognition."""
    
    def __init__(self):
        self.api_key = FMP_API_KEY
        self.pattern_analyzer = EnhancedPatternRecognition()
        
        # Enhanced quality thresholds
        self.quality_thresholds = {
            "A+": {
                "min_compression_score": 0.8,
                "min_momentum_score": 0.7,
                "min_volume_score": 0.6,
                "min_directional_score": 0.8,
                "min_breakout_probability": 0.75,
                "min_pattern_quality": 0.8
            },
            "A": {
                "min_compression_score": 0.7,
                "min_momentum_score": 0.6,
                "min_volume_score": 0.5,
                "min_directional_score": 0.7,
                "min_breakout_probability": 0.65,
                "min_pattern_quality": 0.7
            },
            "B": {
                "min_compression_score": 0.6,
                "min_momentum_score": 0.5,
                "min_volume_score": 0.4,
                "min_directional_score": 0.6,
                "min_breakout_probability": 0.55,
                "min_pattern_quality": 0.6
            },
            "C": {
                "min_compression_score": 0.5,
                "min_momentum_score": 0.4,
                "min_volume_score": 0.3,
                "min_directional_score": 0.5,
                "min_breakout_probability": 0.45,
                "min_pattern_quality": 0.5
            }
        }
    
    async def get_market_data(self, symbol: str, timeframe: str = "15min", 
                            limit: int = 100) -> pd.DataFrame:
        """Get market data for analysis."""
        try:
            # Convert timeframe to FMP format
            interval_map = {
                "1min": "1min", "5min": "5min", "15min": "15min", 
                "30min": "30min", "1hour": "1hour", "4hour": "4hour"
            }
            
            fmp_interval = interval_map.get(timeframe, "15min")
            
            url = f"https://financialmodelingprep.com/api/v3/historical-chart/{fmp_interval}/{symbol}"
            params = {"apikey": self.api_key, "limit": limit}
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        if not data:
                            return pd.DataFrame()
                        
                        df = pd.DataFrame(data)
                        df['date'] = pd.to_datetime(df['date'])
                        df = df.sort_values('date').reset_index(drop=True)
                        
                        # Ensure we have required columns
                        required_cols = ['open', 'high', 'low', 'close', 'volume']
                        for col in required_cols:
                            if col not in df.columns:
                                warning(f"Missing column {col} for {symbol}")
                                return pd.DataFrame()
                        
                        return df
                    else:
                        warning(f"Failed to get data for {symbol}: {response.status}")
                        return pd.DataFrame()
                        
        except Exception as e:
            error(f"Error getting market data for {symbol}: {e}")
            return pd.DataFrame()
    
    def calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate comprehensive technical indicators."""
        try:
            if len(df) < 50:
                return df
            
            # Price data
            high = df['high'].values
            low = df['low'].values
            close = df['close'].values
            volume = df['volume'].values
            
            # Bollinger Bands (20, 2)
            bb_period = 20
            bb_std = 2
            bb_sma = pd.Series(close).rolling(bb_period).mean()
            bb_std_dev = pd.Series(close).rolling(bb_period).std()
            df['bb_upper'] = bb_sma + (bb_std_dev * bb_std)
            df['bb_lower'] = bb_sma - (bb_std_dev * bb_std)
            df['bb_middle'] = bb_sma
            df['bb_width'] = df['bb_upper'] - df['bb_lower']
            
            # Keltner Channels (20, 1.5)
            kc_period = 20
            kc_multiplier = 1.5
            kc_sma = pd.Series(close).rolling(kc_period).mean()
            
            # True Range for ATR
            tr1 = high - low
            tr2 = np.abs(high - np.roll(close, 1))
            tr3 = np.abs(low - np.roll(close, 1))
            true_range = np.maximum(tr1, np.maximum(tr2, tr3))
            atr = pd.Series(true_range).rolling(kc_period).mean()
            
            df['kc_upper'] = kc_sma + (atr * kc_multiplier)
            df['kc_lower'] = kc_sma - (atr * kc_multiplier)
            df['kc_middle'] = kc_sma
            df['kc_width'] = df['kc_upper'] - df['kc_lower']
            
            # EMAs
            df['ema5'] = pd.Series(close).ewm(span=5).mean()
            df['ema8'] = pd.Series(close).ewm(span=8).mean()
            df['ema21'] = pd.Series(close).ewm(span=21).mean()
            
            # Momentum (12-period)
            momentum_period = 12
            df['momentum'] = close - np.roll(close, momentum_period)
            
            # Volume analysis
            df['avg_volume_20'] = pd.Series(volume).rolling(20).mean()
            df['volume_ratio'] = volume / df['avg_volume_20']
            
            # Squeeze detection
            df['squeeze_on'] = (df['bb_lower'] > df['kc_lower']) & (df['bb_upper'] < df['kc_upper'])
            df['squeeze_ratio'] = np.where(df['kc_width'] > 0, df['bb_width'] / df['kc_width'], 1.0)
            
            return df

        except Exception as e:
            error(f"Error calculating technical indicators: {e}")
            return df

    def analyze_ttm_setup(self, df: pd.DataFrame, symbol: str, timeframe: str) -> Optional[EnhancedTTMSetup]:
        """Analyze DataFrame for enhanced TTM squeeze setup."""
        try:
            if len(df) < 50:
                return None

            # Get current values
            current = df.iloc[-1]

            # Check if currently in squeeze
            if not current['squeeze_on']:
                return None

            # Extract arrays for pattern analysis
            bb_width = df['bb_width'].values
            kc_width = df['kc_width'].values
            prices = df['close'].values
            momentum = df['momentum'].values
            volume = df['volume'].values
            ema5 = df['ema5'].values
            ema8 = df['ema8'].values
            ema21 = df['ema21'].values

            # Advanced pattern recognition
            compression_score = self.pattern_analyzer.analyze_compression_quality(bb_width, kc_width)
            momentum_score = self.pattern_analyzer.detect_momentum_divergence(prices, momentum)
            volume_score = self.pattern_analyzer.analyze_volume_confirmation(
                volume, current['avg_volume_20'], (prices[-1] - prices[-2]) / prices[-2]
            )
            directional_score = self.pattern_analyzer.calculate_directional_bias(ema5, ema8, ema21, momentum)

            # Calculate squeeze duration
            squeeze_duration = 0
            for i in range(len(df) - 1, -1, -1):
                if df.iloc[i]['squeeze_on']:
                    squeeze_duration += 1
                else:
                    break

            # Calculate breakout probability
            breakout_probability = self.pattern_analyzer.calculate_breakout_probability(
                compression_score, momentum_score, volume_score, directional_score, squeeze_duration
            )

            # Overall pattern quality score
            pattern_quality_score = (compression_score * 0.3 + momentum_score * 0.25 +
                                   volume_score * 0.25 + directional_score * 0.2)

            # Enhanced criteria evaluation
            criteria_details = self._evaluate_enhanced_criteria(
                df, compression_score, momentum_score, volume_score,
                directional_score, breakout_probability
            )

            criteria_count = sum(criteria_details.values())

            # Grade assignment based on enhanced criteria
            grade = self._assign_enhanced_grade(
                compression_score, momentum_score, volume_score,
                directional_score, breakout_probability, pattern_quality_score
            )

            # Calculate confidence
            confidence = min(1.0, (criteria_count / 5) * pattern_quality_score)

            # Calculate trade levels
            entry_price, stop_loss, target_price = self._calculate_trade_levels(df, directional_score)
            risk_reward_ratio = self._calculate_risk_reward(entry_price, stop_loss, target_price)

            # Create enhanced setup
            setup = EnhancedTTMSetup(
                symbol=symbol,
                timeframe=timeframe,
                timestamp=datetime.now(),
                price=current['close'],
                volume=current['volume'],
                avg_volume_20=current['avg_volume_20'],
                volume_ratio=current['volume_ratio'],
                bb_upper=current['bb_upper'],
                bb_lower=current['bb_lower'],
                bb_width=current['bb_width'],
                kc_upper=current['kc_upper'],
                kc_lower=current['kc_lower'],
                kc_width=current['kc_width'],
                squeeze_ratio=current['squeeze_ratio'],
                ema5=current['ema5'],
                ema8=current['ema8'],
                ema21=current['ema21'],
                momentum=current['momentum'],
                momentum_change=momentum[-1] - momentum[-2] if len(momentum) > 1 else 0,
                momentum_acceleration=momentum[-1] - 2*momentum[-2] + momentum[-3] if len(momentum) > 2 else 0,
                compression_score=compression_score,
                breakout_probability=breakout_probability,
                momentum_divergence_score=momentum_score,
                volume_confirmation_score=volume_score,
                directional_bias_score=directional_score,
                pattern_quality_score=pattern_quality_score,
                grade=grade,
                confidence=confidence,
                criteria_count=criteria_count,
                criteria_details=criteria_details,
                entry_price=entry_price,
                stop_loss=stop_loss,
                target_price=target_price,
                risk_reward_ratio=risk_reward_ratio,
                missing_criteria=[k for k, v in criteria_details.items() if not v]
            )

            return setup

        except Exception as e:
            error(f"Error analyzing TTM setup for {symbol}: {e}")
            return None

    def _evaluate_enhanced_criteria(self, df: pd.DataFrame, compression_score: float,
                                  momentum_score: float, volume_score: float,
                                  directional_score: float, breakout_probability: float) -> Dict[str, bool]:
        """Evaluate enhanced 5-point criteria system."""
        try:
            current = df.iloc[-1]

            # Criterion 1: Clear, pronounced BB compression inside KC
            criterion_1 = compression_score >= 0.6 and current['squeeze_on']

            # Criterion 2: Strong momentum divergence patterns
            criterion_2 = momentum_score >= 0.5

            # Criterion 3: Significant volume confirmation
            criterion_3 = volume_score >= 0.4 and current['volume_ratio'] >= 1.2

            # Criterion 4: Clear directional bias indicators
            criterion_4 = directional_score >= 0.6

            # Criterion 5: High probability breakout patterns
            criterion_5 = breakout_probability >= 0.5

            return {
                "pronounced_compression": criterion_1,
                "momentum_divergence": criterion_2,
                "volume_confirmation": criterion_3,
                "directional_bias": criterion_4,
                "breakout_probability": criterion_5
            }

        except Exception as e:
            error(f"Error evaluating enhanced criteria: {e}")
            return {k: False for k in ["pronounced_compression", "momentum_divergence",
                                     "volume_confirmation", "directional_bias", "breakout_probability"]}

    def _assign_enhanced_grade(self, compression_score: float, momentum_score: float,
                             volume_score: float, directional_score: float,
                             breakout_probability: float, pattern_quality_score: float) -> str:
        """Assign grade based on enhanced scoring system."""
        try:
            # Check each grade level against thresholds
            for grade, thresholds in self.quality_thresholds.items():
                if (compression_score >= thresholds["min_compression_score"] and
                    momentum_score >= thresholds["min_momentum_score"] and
                    volume_score >= thresholds["min_volume_score"] and
                    directional_score >= thresholds["min_directional_score"] and
                    breakout_probability >= thresholds["min_breakout_probability"] and
                    pattern_quality_score >= thresholds["min_pattern_quality"]):
                    return grade

            return "D"  # Below all thresholds

        except Exception as e:
            error(f"Error assigning enhanced grade: {e}")
            return "F"

    def _calculate_trade_levels(self, df: pd.DataFrame, directional_score: float) -> Tuple[float, float, float]:
        """Calculate entry, stop loss, and target prices."""
        try:
            current = df.iloc[-1]
            current_price = current['close']

            # Determine direction based on directional score and momentum
            is_bullish = current['momentum'] > 0 and current['ema5'] > current['ema21']

            if is_bullish:
                # Bullish setup
                entry_price = current_price * 1.002  # Small premium for entry
                stop_loss = min(current['bb_lower'], current['kc_lower']) * 0.995

                # Target based on channel width and directional strength
                channel_width = current['kc_upper'] - current['kc_lower']
                target_multiplier = 1.5 + (directional_score * 0.5)  # 1.5x to 2.0x channel width
                target_price = entry_price + (channel_width * target_multiplier)

            else:
                # Bearish setup
                entry_price = current_price * 0.998  # Small discount for entry
                stop_loss = max(current['bb_upper'], current['kc_upper']) * 1.005

                # Target based on channel width
                channel_width = current['kc_upper'] - current['kc_lower']
                target_multiplier = 1.5 + (directional_score * 0.5)
                target_price = entry_price - (channel_width * target_multiplier)

            return entry_price, stop_loss, target_price

        except Exception as e:
            error(f"Error calculating trade levels: {e}")
            return current_price, current_price * 0.95, current_price * 1.05

    def _calculate_risk_reward(self, entry: float, stop: float, target: float) -> float:
        """Calculate risk/reward ratio."""
        try:
            risk = abs(entry - stop)
            reward = abs(target - entry)
            return reward / risk if risk > 0 else 0.0
        except:
            return 0.0

    async def scan_symbol(self, symbol: str, timeframe: str = "15min") -> Optional[EnhancedTTMSetup]:
        """Scan a single symbol for enhanced TTM setup."""
        try:
            # Get market data
            df = await self.get_market_data(symbol, timeframe, 100)

            if df.empty:
                return None

            # Calculate technical indicators
            df = self.calculate_technical_indicators(df)

            # Analyze for TTM setup
            setup = self.analyze_ttm_setup(df, symbol, timeframe)

            return setup

        except Exception as e:
            error(f"Error scanning {symbol}: {e}")
            return None
