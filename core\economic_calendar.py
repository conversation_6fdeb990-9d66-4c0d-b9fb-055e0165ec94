#!/usr/bin/env python3
"""Economic Calendar Integration - FREE

Integrates economic events and their impact on TTM trading.
Uses FMP API economic calendar (already included in our plan).
"""
import requests
from typing import Dict, List, Optional
from datetime import datetime, timedelta
import json


class EconomicCalendarAnalyzer:
    """Economic calendar analysis for TTM trading decisions."""
    
    def __init__(self):
        self.fmp_key = None
        try:
            from config import get_api_key
            self.fmp_key = get_api_key('FMP_API_KEY')
        except:
            pass
        
        # High impact events that affect TTM trading
        self.high_impact_events = [
            'Federal Funds Rate',
            'FOMC Meeting',
            'Non-Farm Payrolls',
            'CPI',
            'Core CPI',
            'GDP',
            'Unemployment Rate',
            'FOMC Press Conference',
            'Fed Chair Speech',
            'PCE Price Index'
        ]
        
        # Market impact levels
        self.impact_levels = {
            'High': ['Federal Funds Rate', 'FOMC Meeting', 'Non-Farm Payrolls', 'CPI', 'Core CPI'],
            'Medium': ['GDP', 'Unemployment Rate', 'PCE Price Index', 'Fed Chair Speech'],
            'Low': ['Initial Jobless Claims', 'Retail Sales', 'Industrial Production']
        }
    
    def get_upcoming_events(self, days_ahead: int = 7) -> Dict:
        """Get upcoming economic events that could impact TTM trading."""
        if not self.fmp_key:
            return self._mock_economic_data()
        
        try:
            # Get economic calendar from FMP
            from_date = datetime.now().strftime('%Y-%m-%d')
            to_date = (datetime.now() + timedelta(days=days_ahead)).strftime('%Y-%m-%d')
            
            url = f"https://financialmodelingprep.com/api/v3/economic_calendar?from={from_date}&to={to_date}&apikey={self.fmp_key}"
            response = requests.get(url, timeout=10)
            
            if response.status_code != 200:
                return self._mock_economic_data()
            
            events = response.json()
            
            # Filter and categorize events
            high_impact = []
            medium_impact = []
            low_impact = []
            
            for event in events:
                event_name = event.get('event', '')
                event_date = event.get('date', '')
                country = event.get('country', '')
                impact = event.get('impact', 'Low')
                
                # Focus on US events
                if country.upper() != 'US':
                    continue
                
                event_info = {
                    'name': event_name,
                    'date': event_date,
                    'time': event.get('time', ''),
                    'impact': impact,
                    'previous': event.get('previous', ''),
                    'estimate': event.get('estimate', ''),
                    'actual': event.get('actual', ''),
                    'ttm_impact': self._assess_ttm_impact(event_name, impact)
                }
                
                if any(high_event in event_name for high_event in self.impact_levels['High']):
                    high_impact.append(event_info)
                elif any(med_event in event_name for med_event in self.impact_levels['Medium']):
                    medium_impact.append(event_info)
                else:
                    low_impact.append(event_info)
            
            return {
                'high_impact': high_impact[:5],  # Top 5 high impact
                'medium_impact': medium_impact[:3],  # Top 3 medium impact
                'low_impact': low_impact[:2],  # Top 2 low impact
                'total_events': len(high_impact) + len(medium_impact) + len(low_impact),
                'market_outlook': self._assess_market_outlook(high_impact, medium_impact),
                'ttm_trading_advice': self._generate_ttm_advice(high_impact, medium_impact)
            }
            
        except Exception as e:
            return self._mock_economic_data(f"Error: {str(e)}")
    
    def _assess_ttm_impact(self, event_name: str, impact: str) -> str:
        """Assess how an economic event impacts TTM trading."""
        if 'Fed' in event_name or 'FOMC' in event_name:
            return "High volatility expected - TTM breakouts may be more violent"
        elif 'CPI' in event_name or 'Inflation' in event_name:
            return "Inflation data affects market sentiment - monitor TTM setups closely"
        elif 'Employment' in event_name or 'Payrolls' in event_name:
            return "Employment data impacts market direction - good for TTM momentum"
        elif 'GDP' in event_name:
            return "Economic growth data - affects overall market trend for TTM"
        else:
            return "Monitor for market volatility around event time"
    
    def _assess_market_outlook(self, high_impact: List, medium_impact: List) -> str:
        """Assess overall market outlook based on upcoming events."""
        total_high = len(high_impact)
        total_medium = len(medium_impact)
        
        if total_high >= 3:
            return "High volatility week - expect significant market moves"
        elif total_high >= 1 or total_medium >= 3:
            return "Moderate volatility expected - good for TTM breakouts"
        else:
            return "Low volatility week - TTM squeezes may build slowly"
    
    def _generate_ttm_advice(self, high_impact: List, medium_impact: List) -> str:
        """Generate TTM trading advice based on economic calendar."""
        if not high_impact and not medium_impact:
            return "Clear calendar - focus on technical TTM setups without macro interference"
        
        next_event = None
        if high_impact:
            next_event = high_impact[0]
        elif medium_impact:
            next_event = medium_impact[0]
        
        if next_event:
            event_date = next_event.get('date', '')
            event_name = next_event.get('name', '')
            
            try:
                event_dt = datetime.strptime(event_date, '%Y-%m-%d')
                days_until = (event_dt - datetime.now()).days
                
                if days_until <= 1:
                    return f"⚠️ {event_name} in {days_until} day(s) - consider tighter stops on TTM positions"
                elif days_until <= 3:
                    return f"📅 {event_name} in {days_until} days - good time for TTM entries before volatility"
                else:
                    return f"📊 {event_name} coming up - normal TTM trading conditions"
            except:
                return "Monitor upcoming economic events for TTM timing"
        
        return "Normal economic calendar - focus on TTM technical analysis"
    
    def _mock_economic_data(self, error: str = None) -> Dict:
        """Return mock economic data when API is unavailable."""
        mock_events = [
            {
                'name': 'FOMC Meeting Minutes',
                'date': (datetime.now() + timedelta(days=2)).strftime('%Y-%m-%d'),
                'time': '14:00',
                'impact': 'High',
                'ttm_impact': 'High volatility expected - TTM breakouts may be more violent'
            },
            {
                'name': 'Non-Farm Payrolls',
                'date': (datetime.now() + timedelta(days=5)).strftime('%Y-%m-%d'),
                'time': '08:30',
                'impact': 'High',
                'ttm_impact': 'Employment data impacts market direction - good for TTM momentum'
            }
        ]
        
        return {
            'high_impact': mock_events,
            'medium_impact': [],
            'low_impact': [],
            'total_events': len(mock_events),
            'market_outlook': 'Moderate volatility expected - good for TTM breakouts',
            'ttm_trading_advice': 'FOMC Meeting in 2 days - consider tighter stops on TTM positions',
            'note': error or 'Using mock data - configure FMP API key for live economic calendar'
        }


def get_economic_calendar_for_ttm() -> str:
    """Get economic calendar analysis for TTM trading decisions."""
    analyzer = EconomicCalendarAnalyzer()
    result = analyzer.get_upcoming_events()
    
    # Format the response
    response = "📅 **Economic Calendar - TTM Trading Impact:**\n\n"
    
    # High impact events
    if result['high_impact']:
        response += "🔴 **HIGH IMPACT EVENTS:**\n"
        for event in result['high_impact']:
            response += f"• {event['date']} - {event['name']}\n"
            response += f"  Impact: {event['ttm_impact']}\n"
        response += "\n"
    
    # Medium impact events
    if result['medium_impact']:
        response += "🟡 **MEDIUM IMPACT EVENTS:**\n"
        for event in result['medium_impact'][:2]:  # Show top 2
            response += f"• {event['date']} - {event['name']}\n"
        response += "\n"
    
    # Market outlook
    response += f"📊 **Market Outlook:** {result['market_outlook']}\n\n"
    
    # TTM trading advice
    response += f"🎯 **TTM Trading Advice:** {result['ttm_trading_advice']}\n"
    
    # Add note if using mock data
    if 'note' in result:
        response += f"\n💡 *{result['note']}*"
    
    return response


def check_event_impact_today() -> str:
    """Check if there are any high-impact events today."""
    analyzer = EconomicCalendarAnalyzer()
    result = analyzer.get_upcoming_events(days_ahead=1)
    
    today = datetime.now().strftime('%Y-%m-%d')
    
    today_events = []
    for event in result['high_impact'] + result['medium_impact']:
        if event['date'] == today:
            today_events.append(event)
    
    if not today_events:
        return "✅ No major economic events today - normal TTM trading conditions"
    
    response = "⚠️ **Economic Events Today:**\n"
    for event in today_events:
        response += f"• {event['time']} - {event['name']}\n"
        response += f"  TTM Impact: {event['ttm_impact']}\n"
    
    return response


if __name__ == "__main__":
    # Test the economic calendar analyzer
    analyzer = EconomicCalendarAnalyzer()
    
    print("🧪 Testing Economic Calendar Analyzer")
    print("=" * 50)
    
    result = analyzer.get_upcoming_events()
    print(f"High Impact Events: {len(result['high_impact'])}")
    print(f"Market Outlook: {result['market_outlook']}")
    print(f"TTM Advice: {result['ttm_trading_advice']}")
    
    if result['high_impact']:
        print(f"Next Event: {result['high_impact'][0]['name']} on {result['high_impact'][0]['date']}")
    
    print("\n" + get_economic_calendar_for_ttm())
