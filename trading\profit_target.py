"""Profit Target Planner (Phase 1)

Generates a basic trade plan to achieve a desired absolute profit target
using a single equity position and a fixed-fraction stop-loss.

Assumptions (MVP):
• We use a market order to enter.
• Stop-loss distance = 2 % of entry price.
• Take-profit distance = target_profit / qty.
• Only one symbol at a time (user can change later).
• Paper account by default – no real orders are sent.
"""
from __future__ import annotations

import math
from typing import Dict

from alpaca.trading.client import TradingClient
from alpaca.common.exceptions import APIError
from types import SimpleNamespace

from config import get_api_key
from logger_util import info, warning
from advanced_ttm_squeeze_scanner import run_ttm_squeeze_scan
from proper_ttm_squeeze_scanner import run_proper_ttm_scan
from dynamic_stop_loss import DynamicStopLossManager
import requests

FMP_QUOTE_URL = "https://financialmodelingprep.com/api/v3/quote/{sym}?apikey={key}"


class ProfitTargetPlanner:
    def __init__(self) -> None:
        try:
            self.client = TradingClient(
                get_api_key("ALPACA_API_KEY"),
                get_api_key("ALPACA_API_SECRET"),
                paper=True,
            )
            self.account = self._fetch_account()
        except (APIError, RuntimeError):
            # If Alpaca connection fails, use mock account for analysis
            warning("Alpaca connection failed, using mock account for analysis")
            self.client = None
            self.account = self._create_mock_account()

        # Initialize dynamic stop loss manager
        try:
            self.stop_loss_manager = DynamicStopLossManager()
        except Exception as e:
            warning(f"Failed to initialize dynamic stop loss manager: {e}")
            self.stop_loss_manager = None

    def _fetch_account(self):
        try:
            acct = self.client.get_account()
            info("💵 Account loaded", equity=acct.equity, buying_power=acct.buying_power)
            return acct
        except APIError as exc:
            raise RuntimeError(
                "Alpaca API credentials are invalid or unauthorized. "
                "Please verify ALPACA_API_KEY / SECRET and ensure paper trading is enabled."
            ) from exc

    def _create_mock_account(self):
        """Create a mock account with reasonable paper trading values."""
        mock_account = SimpleNamespace()
        mock_account.equity = "100000.00"  # $100k paper account
        mock_account.buying_power = "100000.00"  # Full buying power
        mock_account.cash = "100000.00"
        info("💵 Mock account created", equity=mock_account.equity, buying_power=mock_account.buying_power)
        return mock_account

    # ---------------------------------------------------------------------
    def _get_price(self, symbol: str) -> float:
        resp = requests.get(
            FMP_QUOTE_URL.format(sym=symbol, key=get_api_key("FMP_API_KEY")), timeout=10
        )
        price = resp.json()[0]["price"]
        return float(price)

    def find_ttm_squeeze_opportunities(self) -> str:
        """Find the best TTM Squeeze opportunities using proper scanner that matches Think or Swim."""
        try:
            info("🔍 Scanning for TTM Squeeze opportunities...")
            opportunities = run_proper_ttm_scan()
            return opportunities
        except Exception as e:
            warning(f"TTM Squeeze scan failed: {e}")
            return "TTM Squeeze scan failed - using fallback"

    # ------------------------------------------------------------------
    def plan(self, symbol: str = None, target_profit_dollars: float = 50, risk_pct: float = 0.02) -> Dict:
        opportunities = "No TTM scan performed"

        # If no symbol provided, find TTM Squeeze opportunities
        if symbol is None:
            info("🎯 No symbol provided, scanning for TTM Squeeze opportunities...")
            opportunities = self.find_ttm_squeeze_opportunities()
            symbol = None  # Reset to ensure we find one

            # Parse the opportunities and find the best one
            if "🎯 TOP TTM SQUEEZE OPPORTUNITIES" in opportunities:
                # Extract the first opportunity from the formatted response
                lines = opportunities.split('\n')
                for line in lines:
                    if '📈' in line and '$' in line:
                        # Extract symbol from line like "📈 NVDA: $141.935 ..."
                        parts = line.split()
                        for part in parts:
                            if part.endswith(':'):
                                symbol = part.rstrip(':')
                                break
                        if symbol:
                            info(f"🎯 Selected TTM Squeeze opportunity: {symbol}")
                            break

            if not symbol:
                # Fallback to high-momentum stocks when no TTM squeeze found
                momentum_stocks = ["NVDA", "TSLA", "AAPL", "MSFT", "GOOGL"]
                symbol = momentum_stocks[0]  # Use NVDA as primary momentum play
                info(f"🎯 No TTM Squeeze opportunities found, using high-momentum fallback: {symbol}")
                opportunities = f"No TTM Squeeze setups found in current market. Using {symbol} as high-momentum alternative."

        price = self._get_price(symbol)

        # Use dynamic stop loss if available
        if self.stop_loss_manager:
            stop_info = self.stop_loss_manager.calculate_dynamic_stop_loss(
                symbol=symbol,
                entry_price=price,
                position_type="long",
                base_stop_pct=risk_pct,
                use_trailing=True
            )
            stop_loss = stop_info["stop_price"]
            stop_analysis = stop_info["analysis"]
            stop_type = stop_info["stop_type"]
        else:
            # Fallback to basic stop loss
            stop_loss = price * (1 - risk_pct)
            stop_analysis = f"Basic {risk_pct*100}% stop loss"
            stop_type = "fixed"

        stop_distance = price - stop_loss
        qty = math.ceil(target_profit_dollars / stop_distance)

        required_cash = qty * price
        if required_cash > float(self.account.buying_power):
            warning("Not enough buying power for plan", required=required_cash)

        take_profit = price + (target_profit_dollars / qty)

        plan = {
            "symbol": symbol,
            "entry_price": price,
            "quantity": qty,
            "stop_loss": round(stop_loss, 2),
            "stop_type": stop_type,
            "stop_analysis": stop_analysis,
            "take_profit": round(take_profit, 2),
            "required_cash": round(required_cash, 2),
            "ttm_analysis": opportunities if symbol else "No TTM scan performed"
        }
        return plan

    def update_stop_loss(self, symbol: str, entry_price: float, current_stop: float) -> Dict:
        """Update stop loss for an existing position using dynamic management."""
        if not self.stop_loss_manager:
            return {
                "error": "Dynamic stop loss manager not available",
                "current_stop": current_stop
            }

        info(f"🔄 Updating stop loss for {symbol}")

        # Get updated dynamic stop loss
        new_stop_info = self.stop_loss_manager.calculate_dynamic_stop_loss(
            symbol=symbol,
            entry_price=entry_price,
            position_type="long",
            use_trailing=True
        )

        # Also check trailing stop update
        trailing_update = self.stop_loss_manager.update_trailing_stop(
            symbol=symbol,
            entry_price=entry_price,
            current_stop=current_stop,
            position_type="long"
        )

        # Use the higher of the two stops (more conservative)
        recommended_stop = max(new_stop_info["stop_price"], trailing_update["new_stop"])

        return {
            "symbol": symbol,
            "current_stop": current_stop,
            "recommended_stop": round(recommended_stop, 2),
            "stop_type": new_stop_info["stop_type"],
            "analysis": new_stop_info["analysis"],
            "trailing_update": trailing_update["reason"],
            "should_update": recommended_stop > current_stop
        }