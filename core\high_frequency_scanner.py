#!/usr/bin/env python3
"""
High-Frequency TTM Scanner - Orchestrates enhanced scanning across large symbol universe
Scans S&P 500 + $100B+ market cap stocks every 5 minutes during market hours
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import threading
from concurrent.futures import ThreadPoolExecutor
import queue

try:
    from core.logger_util import info, warning, error
except ImportError:
    def info(msg): print(f"ℹ️ {msg}")
    def warning(msg): print(f"⚠️ {msg}")
    def error(msg): print(f"❌ {msg}")

try:
    from scanners.enhanced_ttm_squeeze_scanner import EnhancedTTMSqueezeScanner, EnhancedTTMSetup
    from core.symbol_manager import get_symbol_manager, get_trading_symbols
    from core.ttm_setup_manager import store_ttm_setup
    DEPENDENCIES_AVAILABLE = True
except ImportError as e:
    warning(f"Dependencies not available: {e}")
    DEPENDENCIES_AVAILABLE = False


class HighFrequencyScanner:
    """High-frequency scanning system for enhanced TTM setups."""
    
    def __init__(self):
        self.is_running = False
        self.scanner_thread = None
        self.enhanced_scanner = None
        self.symbol_manager = None
        
        # Scanning configuration
        self.scan_interval = 300  # 5 minutes in seconds
        self.max_concurrent_scans = 20  # Concurrent symbol scans
        self.timeframes = ["15min", "30min", "1hour"]  # Multiple timeframes
        
        # Performance tracking
        self.stats = {
            "total_scans": 0,
            "symbols_scanned": 0,
            "setups_found": 0,
            "high_quality_setups": 0,
            "scan_errors": 0,
            "last_scan_time": None,
            "scan_duration": 0,
            "symbols_per_minute": 0
        }
        
        # Market hours (Eastern Time)
        self.market_hours = {
            "premarket_start": (4, 0),   # 4:00 AM ET
            "market_open": (9, 30),      # 9:30 AM ET  
            "market_close": (16, 0),     # 4:00 PM ET
            "afterhours_end": (20, 0)    # 8:00 PM ET
        }
        
        # Results queue for real-time processing
        self.results_queue = queue.Queue()
        
        # Initialize components
        if DEPENDENCIES_AVAILABLE:
            self.enhanced_scanner = EnhancedTTMSqueezeScanner()
            self.symbol_manager = get_symbol_manager()
            info("✅ High-Frequency Scanner initialized")
        else:
            error("❌ High-Frequency Scanner dependencies not available")
    
    def is_market_time(self) -> bool:
        """Check if current time is within extended market hours."""
        try:
            import pytz
            et_tz = pytz.timezone('US/Eastern')
            current_et = datetime.now(et_tz)
            current_time = current_et.time()
            current_weekday = current_et.weekday()
            
            # Skip weekends (Saturday=5, Sunday=6)
            if current_weekday >= 5:
                return False
            
            # Check if within extended trading hours
            start_time = datetime.combine(current_et.date(), 
                                        datetime.min.time().replace(hour=self.market_hours["premarket_start"][0],
                                                                   minute=self.market_hours["premarket_start"][1])).time()
            end_time = datetime.combine(current_et.date(),
                                      datetime.min.time().replace(hour=self.market_hours["afterhours_end"][0],
                                                                 minute=self.market_hours["afterhours_end"][1])).time()
            
            return start_time <= current_time <= end_time
            
        except ImportError:
            # Fallback without timezone support
            current_time = datetime.now().time()
            current_weekday = datetime.now().weekday()
            
            if current_weekday >= 5:
                return False
                
            return (datetime.min.time().replace(hour=4, minute=0) <= current_time <= 
                   datetime.min.time().replace(hour=20, minute=0))
        except Exception as e:
            warning(f"Market time check failed: {e}")
            return True  # Default to allowing scans
    
    def get_scan_symbols(self) -> List[str]:
        """Get comprehensive list of symbols to scan."""
        try:
            if not self.symbol_manager:
                # Fallback symbol list
                return ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'META', 'TSLA', 'PLTR']
            
            # Get all active symbols (S&P 500 + $100B+ market cap + priority)
            symbols = get_trading_symbols()
            
            # Ensure priority symbols are included
            priority_symbols = ['PLTR', 'AAPL']
            for symbol in priority_symbols:
                if symbol not in symbols:
                    symbols.append(symbol)
            
            info(f"📊 Scanning {len(symbols)} symbols across {len(self.timeframes)} timeframes")
            return symbols
            
        except Exception as e:
            error(f"Error getting scan symbols: {e}")
            return ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'META', 'TSLA', 'PLTR']
    
    async def scan_symbol_all_timeframes(self, symbol: str) -> List[EnhancedTTMSetup]:
        """Scan a symbol across all timeframes."""
        setups = []
        
        for timeframe in self.timeframes:
            try:
                setup = await self.enhanced_scanner.scan_symbol(symbol, timeframe)
                if setup:
                    setups.append(setup)
                    
                # Small delay between timeframes
                await asyncio.sleep(0.1)
                
            except Exception as e:
                warning(f"Error scanning {symbol} {timeframe}: {e}")
                self.stats["scan_errors"] += 1
                continue
        
        return setups
    
    async def scan_batch_symbols(self, symbols: List[str]) -> List[EnhancedTTMSetup]:
        """Scan a batch of symbols concurrently."""
        try:
            # Create semaphore to limit concurrent scans
            semaphore = asyncio.Semaphore(self.max_concurrent_scans)
            
            async def scan_with_semaphore(symbol):
                async with semaphore:
                    return await self.scan_symbol_all_timeframes(symbol)
            
            # Execute concurrent scans
            tasks = [scan_with_semaphore(symbol) for symbol in symbols]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Collect all setups
            all_setups = []
            for result in results:
                if isinstance(result, list):
                    all_setups.extend(result)
                elif isinstance(result, Exception):
                    self.stats["scan_errors"] += 1
            
            return all_setups
            
        except Exception as e:
            error(f"Error in batch scanning: {e}")
            return []
    
    async def perform_comprehensive_scan(self) -> Dict[str, Any]:
        """Perform a comprehensive scan of all symbols."""
        try:
            scan_start_time = time.time()
            self.stats["last_scan_time"] = datetime.now()
            
            info("🚀 Starting comprehensive TTM scan...")
            
            # Get symbols to scan
            symbols = self.get_scan_symbols()
            self.stats["symbols_scanned"] = len(symbols)
            
            # Scan all symbols
            all_setups = await self.scan_batch_symbols(symbols)
            
            # Filter and categorize setups
            high_quality_setups = []
            all_quality_setups = []
            
            for setup in all_setups:
                if setup.grade in ['A+', 'A']:
                    high_quality_setups.append(setup)
                all_quality_setups.append(setup)
                
                # Store setup in database
                try:
                    setup_data = {
                        'symbol': setup.symbol,
                        'timeframe': setup.timeframe,
                        'grade': setup.grade,
                        'confidence': setup.confidence,
                        'criteria_count': setup.criteria_count,
                        'price': setup.price,
                        'volume': setup.volume,
                        'entry_price': setup.entry_price,
                        'stop_loss': setup.stop_loss,
                        'target_price': setup.target_price,
                        'momentum_value': setup.momentum,
                        'breakout_probability': setup.breakout_probability,
                        'sector': setup.sector,
                        'market_cap': setup.market_cap
                    }
                    store_ttm_setup(setup_data)
                except Exception as e:
                    warning(f"Failed to store setup {setup.symbol}: {e}")
            
            # Update statistics
            scan_duration = time.time() - scan_start_time
            self.stats["total_scans"] += 1
            self.stats["setups_found"] = len(all_quality_setups)
            self.stats["high_quality_setups"] = len(high_quality_setups)
            self.stats["scan_duration"] = scan_duration
            self.stats["symbols_per_minute"] = (len(symbols) / scan_duration) * 60 if scan_duration > 0 else 0
            
            # Create scan results
            scan_results = {
                "scan_timestamp": self.stats["last_scan_time"].isoformat(),
                "symbols_scanned": len(symbols),
                "total_setups_found": len(all_quality_setups),
                "high_quality_setups": len(high_quality_setups),
                "scan_duration_seconds": scan_duration,
                "symbols_per_minute": self.stats["symbols_per_minute"],
                "timeframes_scanned": self.timeframes,
                "setups_by_grade": self._categorize_setups_by_grade(all_quality_setups),
                "top_setups": [self._setup_to_dict(setup) for setup in high_quality_setups[:10]]
            }
            
            info(f"✅ Scan completed: {len(all_quality_setups)} setups found ({len(high_quality_setups)} high-quality) in {scan_duration:.1f}s")
            
            # Add to results queue for real-time processing
            self.results_queue.put(scan_results)
            
            return scan_results
            
        except Exception as e:
            error(f"Error in comprehensive scan: {e}")
            self.stats["scan_errors"] += 1
            return {"error": str(e)}
    
    def _categorize_setups_by_grade(self, setups: List[EnhancedTTMSetup]) -> Dict[str, int]:
        """Categorize setups by grade."""
        grade_counts = {"A+": 0, "A": 0, "B": 0, "C": 0, "D": 0}
        for setup in setups:
            if setup.grade in grade_counts:
                grade_counts[setup.grade] += 1
        return grade_counts
    
    def _setup_to_dict(self, setup: EnhancedTTMSetup) -> Dict[str, Any]:
        """Convert setup to dictionary for JSON serialization."""
        return {
            "symbol": setup.symbol,
            "timeframe": setup.timeframe,
            "grade": setup.grade,
            "confidence": round(setup.confidence, 3),
            "price": round(setup.price, 2),
            "entry_price": round(setup.entry_price, 2),
            "stop_loss": round(setup.stop_loss, 2),
            "target_price": round(setup.target_price, 2),
            "risk_reward_ratio": round(setup.risk_reward_ratio, 2),
            "breakout_probability": round(setup.breakout_probability, 3),
            "pattern_quality_score": round(setup.pattern_quality_score, 3),
            "volume_ratio": round(setup.volume_ratio, 2),
            "criteria_count": setup.criteria_count
        }
    
    def start_high_frequency_scanning(self) -> bool:
        """Start the high-frequency scanning system."""
        if self.is_running:
            warning("High-frequency scanner already running")
            return False
        
        if not DEPENDENCIES_AVAILABLE:
            error("Cannot start scanner: dependencies not available")
            return False
        
        info("🚀 Starting high-frequency TTM scanning system...")
        
        self.is_running = True
        self.scanner_thread = threading.Thread(target=self._scanning_loop, daemon=True)
        self.scanner_thread.start()
        
        info(f"✅ High-frequency scanner started (5-minute intervals)")
        return True
    
    def stop_high_frequency_scanning(self) -> bool:
        """Stop the high-frequency scanning system."""
        if not self.is_running:
            warning("High-frequency scanner not running")
            return False
        
        info("⏹️ Stopping high-frequency TTM scanning system...")
        
        self.is_running = False
        
        if self.scanner_thread and self.scanner_thread.is_alive():
            self.scanner_thread.join(timeout=10)
        
        info("✅ High-frequency scanner stopped")
        return True
    
    def _scanning_loop(self):
        """Main scanning loop."""
        info("🔄 High-frequency scanning loop started")
        
        while self.is_running:
            try:
                # Check if market is open
                if not self.is_market_time():
                    info("⏰ Market closed - waiting for next session")
                    time.sleep(300)  # Check every 5 minutes when market closed
                    continue
                
                # Perform comprehensive scan
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                try:
                    scan_results = loop.run_until_complete(self.perform_comprehensive_scan())
                    
                    if "error" not in scan_results:
                        info(f"📊 Scan completed: {scan_results['total_setups_found']} setups found")
                    
                finally:
                    loop.close()
                
                # Wait for next scan interval
                time.sleep(self.scan_interval)
                
            except Exception as e:
                error(f"Error in scanning loop: {e}")
                self.stats["scan_errors"] += 1
                time.sleep(60)  # Wait 1 minute on error
    
    def get_scanner_statistics(self) -> Dict[str, Any]:
        """Get comprehensive scanner statistics."""
        return {
            **self.stats,
            "is_running": self.is_running,
            "scan_interval_minutes": self.scan_interval / 60,
            "timeframes": self.timeframes,
            "max_concurrent_scans": self.max_concurrent_scans,
            "is_market_time": self.is_market_time(),
            "dependencies_available": DEPENDENCIES_AVAILABLE
        }
    
    def get_latest_results(self) -> Optional[Dict[str, Any]]:
        """Get latest scan results from queue."""
        try:
            return self.results_queue.get_nowait()
        except queue.Empty:
            return None


# Global high-frequency scanner instance
_hf_scanner = HighFrequencyScanner()


def get_high_frequency_scanner() -> HighFrequencyScanner:
    """Get the global high-frequency scanner instance."""
    return _hf_scanner


def start_high_frequency_scanning() -> bool:
    """Start high-frequency scanning."""
    return _hf_scanner.start_high_frequency_scanning()


def stop_high_frequency_scanning() -> bool:
    """Stop high-frequency scanning."""
    return _hf_scanner.stop_high_frequency_scanning()


def get_scanner_statistics() -> Dict[str, Any]:
    """Get scanner statistics."""
    return _hf_scanner.get_scanner_statistics()


def get_latest_scan_results() -> Optional[Dict[str, Any]]:
    """Get latest scan results."""
    return _hf_scanner.get_latest_results()
