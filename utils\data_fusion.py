"""Data Fusion Engine – combines multiple market data sources into one view.

Current implementation is a placeholder returning the first non-empty dataset.
Future versions will perform weighted, conflict-resolving merges.
"""
from __future__ import annotations

from typing import Any, Dict, List

import asyncio

from logger_util import info

async def fuse_market_data(symbol: str, datasets: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Merge data dictionaries coming from different vendors.

    Parameters
    ----------
    symbol : str
        Stock/asset symbol (e.g. "AAPL").
    datasets : list of dict
        Individual vendor responses already parsed into Python dicts.

    Returns
    -------
    dict
        The fused dataset (currently the first non-empty one).
    """
    info("🔄 Fusing data", symbol=symbol, vendor_count=len(datasets))
    for d in datasets:
        if d:
            return d
    return {} 