#!/usr/bin/env python3
"""
Enhanced Chat Integration for TotalRecall TTM Trading System

This module seamlessly integrates the enhanced MCP capabilities
into the existing chat system without disrupting current functionality.

Features:
- Automatic detection of enhanced MCP capabilities
- Seamless fallback to existing functionality
- Enhanced command recognition
- Improved response formatting
"""

import re
from typing import Dict, Any, Optional, List
from pathlib import Path

# Import existing chat system
try:
    from core.logger_util import info, warning, error
    LOGGER_AVAILABLE = True
except ImportError:
    LOGGER_AVAILABLE = False
    def info(msg): print(f"ℹ️ {msg}")
    def warning(msg): print(f"⚠️ {msg}")
    def error(msg): print(f"❌ {msg}")

try:
    from core.chat_core import chat_gpt, TOOLS
    CHAT_CORE_AVAILABLE = True
except ImportError:
    CHAT_CORE_AVAILABLE = False
    def chat_gpt(msg): return f"Chat system not available: {msg}"
    TOOLS = {}

try:
    from core.enhanced_mcp_integration import get_enhanced_mcp, is_enhanced_mcp_available, get_enhanced_capabilities
    MCP_INTEGRATION_AVAILABLE = True
except ImportError:
    MCP_INTEGRATION_AVAILABLE = False
    def get_enhanced_mcp(): return None
    def is_enhanced_mcp_available(): return False
    def get_enhanced_capabilities(): return []

CHAT_AVAILABLE = CHAT_CORE_AVAILABLE and MCP_INTEGRATION_AVAILABLE

class EnhancedChatIntegration:
    """Enhanced chat integration that adds MCP capabilities to existing system."""
    
    def __init__(self):
        self.enhanced_mcp = None
        self.enhanced_commands = {}
        self.is_initialized = False
        
        if CHAT_AVAILABLE:
            self._initialize_enhanced_commands()
    
    def _initialize_enhanced_commands(self):
        """Initialize enhanced command patterns."""
        self.enhanced_commands = {
            # Options analysis patterns
            'options_greeks': [
                r'(?:greeks?|delta|gamma|theta|vega|rho)\s+(?:for\s+)?(\w+)',
                r'options?\s+analysis\s+(?:for\s+)?(\w+)',
                r'analyze\s+options?\s+(?:for\s+)?(\w+)'
            ],
            
            # Portfolio analysis patterns  
            'portfolio_analysis': [
                r'portfolio\s+(?:analysis|performance|risk)',
                r'my\s+(?:positions?|portfolio|account)',
                r'show\s+(?:my\s+)?(?:positions?|portfolio)'
            ],
            
            # Market intelligence patterns
            'market_intelligence': [
                r'earnings?\s+(?:calendar|schedule|upcoming)',
                r'(?:dividend|split)s?\s+(?:calendar|schedule)',
                r'market\s+calendar',
                r'corporate\s+actions?'
            ],
            
            # Smart order patterns
            'smart_orders': [
                r'(?:buy|sell)\s+(\d+)\s+(?:shares?\s+of\s+)?(\w+)\s+(?:with\s+)?(?:stop|limit|trailing)',
                r'place\s+(?:a\s+)?(?:stop|limit|trailing)\s+order',
                r'advanced\s+order'
            ],
            
            # Watchlist patterns
            'watchlist_management': [
                r'create\s+watchlist',
                r'(?:add|update)\s+(?:to\s+)?watchlist',
                r'analyze\s+watchlist',
                r'watchlist\s+(?:analysis|performance)'
            ]
        }
        
        self.is_initialized = True
        info("✅ Enhanced chat command patterns initialized")
    
    def enhance_chat_response(self, user_message: str, original_response: str) -> str:
        """Enhance chat response with MCP capabilities if available."""
        try:
            if not is_enhanced_mcp_available():
                return self._add_enhancement_notice(original_response)
            
            # Check if message matches enhanced patterns
            enhanced_response = self._check_for_enhanced_commands(user_message)
            
            if enhanced_response:
                return enhanced_response
            else:
                return self._enhance_existing_response(original_response)
                
        except Exception as e:
            error(f"Chat enhancement failed: {e}")
            return original_response
    
    def _check_for_enhanced_commands(self, message: str) -> Optional[str]:
        """Check if message matches enhanced command patterns."""
        message_lower = message.lower()
        
        # Options analysis
        for pattern in self.enhanced_commands.get('options_greeks', []):
            match = re.search(pattern, message_lower)
            if match:
                symbol = match.group(1).upper() if match.groups() else None
                if symbol:
                    return self._handle_enhanced_options_analysis(symbol, message)
        
        # Portfolio analysis
        for pattern in self.enhanced_commands.get('portfolio_analysis', []):
            if re.search(pattern, message_lower):
                return self._handle_enhanced_portfolio_analysis(message)
        
        # Market intelligence
        for pattern in self.enhanced_commands.get('market_intelligence', []):
            if re.search(pattern, message_lower):
                return self._handle_enhanced_market_intelligence(message)
        
        # Smart orders
        for pattern in self.enhanced_commands.get('smart_orders', []):
            match = re.search(pattern, message_lower)
            if match:
                return self._handle_enhanced_order_execution(message, match)
        
        # Watchlist management
        for pattern in self.enhanced_commands.get('watchlist_management', []):
            if re.search(pattern, message_lower):
                return self._handle_enhanced_watchlist_management(message)
        
        return None
    
    def _handle_enhanced_options_analysis(self, symbol: str, message: str) -> str:
        """Handle enhanced options analysis."""
        try:
            enhanced_mcp = get_enhanced_mcp()
            
            # Determine analysis type from message
            analysis_type = "greeks"
            if "iv" in message.lower() or "volatility" in message.lower():
                analysis_type = "iv_analysis"
            elif "risk" in message.lower() or "reward" in message.lower():
                analysis_type = "risk_reward"
            
            # Use async wrapper for sync context
            import asyncio
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            result = loop.run_until_complete(
                enhanced_mcp.advanced_options_analysis(symbol, analysis_type)
            )
            
            return f"🎯 **Enhanced Options Analysis**\n\n{result}\n\n" \
                   f"💡 *Powered by Enhanced MCP Integration*"
            
        except Exception as e:
            error(f"Enhanced options analysis failed: {e}")
            return f"📊 Options analysis for {symbol} - using existing system capabilities"
    
    def _handle_enhanced_portfolio_analysis(self, message: str) -> str:
        """Handle enhanced portfolio analysis."""
        try:
            enhanced_mcp = get_enhanced_mcp()
            
            # Determine analysis type
            analysis_type = "full"
            if "risk" in message.lower():
                analysis_type = "risk"
            elif "performance" in message.lower():
                analysis_type = "performance"
            elif "positions" in message.lower():
                analysis_type = "positions"
            
            import asyncio
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            result = loop.run_until_complete(
                enhanced_mcp.enhanced_portfolio_analysis(analysis_type, True)
            )
            
            return f"💼 **Enhanced Portfolio Analysis**\n\n{result}\n\n" \
                   f"💡 *Powered by Enhanced MCP Integration*"
            
        except Exception as e:
            error(f"Enhanced portfolio analysis failed: {e}")
            return "💼 Portfolio analysis - using existing system capabilities"
    
    def _handle_enhanced_market_intelligence(self, message: str) -> str:
        """Handle enhanced market intelligence."""
        try:
            enhanced_mcp = get_enhanced_mcp()
            
            # Determine query type
            query_type = "market_calendar"
            if "earnings" in message.lower():
                query_type = "earnings"
            elif "dividend" in message.lower():
                query_type = "dividends"
            elif "split" in message.lower():
                query_type = "splits"
            
            import asyncio
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            result = loop.run_until_complete(
                enhanced_mcp.advanced_market_intelligence(query_type, None, 30)
            )
            
            return f"📈 **Enhanced Market Intelligence**\n\n{result}\n\n" \
                   f"💡 *Powered by Enhanced MCP Integration*"
            
        except Exception as e:
            error(f"Enhanced market intelligence failed: {e}")
            return "📈 Market intelligence - using existing system capabilities"
    
    def _handle_enhanced_order_execution(self, message: str, match) -> str:
        """Handle enhanced order execution."""
        try:
            # Extract order details from message
            # This is a simplified parser - could be enhanced further
            words = message.lower().split()
            
            symbol = None
            quantity = None
            action = "buy"
            order_type = "market"
            
            # Find symbol and quantity
            for i, word in enumerate(words):
                if word in ["buy", "sell"]:
                    action = word
                if word.isdigit():
                    quantity = float(word)
                if len(word) >= 2 and word.isalpha() and word.isupper():
                    symbol = word
            
            if not symbol or not quantity:
                return "❌ Could not parse order details. Please specify symbol and quantity."
            
            enhanced_mcp = get_enhanced_mcp()
            
            import asyncio
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            result = loop.run_until_complete(
                enhanced_mcp.smart_order_execution(symbol, action, quantity, order_type)
            )
            
            return f"✅ **Enhanced Order Execution**\n\n{result}\n\n" \
                   f"💡 *Powered by Enhanced MCP Integration*"
            
        except Exception as e:
            error(f"Enhanced order execution failed: {e}")
            return "📊 Order execution - using existing system capabilities"
    
    def _handle_enhanced_watchlist_management(self, message: str) -> str:
        """Handle enhanced watchlist management."""
        try:
            enhanced_mcp = get_enhanced_mcp()
            
            # Determine action
            action = "analyze"
            if "create" in message.lower():
                action = "create"
            elif "update" in message.lower() or "add" in message.lower():
                action = "update"
            
            import asyncio
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            result = loop.run_until_complete(
                enhanced_mcp.watchlist_intelligence(action, None, None, True)
            )
            
            return f"📋 **Enhanced Watchlist Management**\n\n{result}\n\n" \
                   f"💡 *Powered by Enhanced MCP Integration*"
            
        except Exception as e:
            error(f"Enhanced watchlist management failed: {e}")
            return "📋 Watchlist management - using existing system capabilities"
    
    def _enhance_existing_response(self, response: str) -> str:
        """Enhance existing response with MCP capabilities notice."""
        if is_enhanced_mcp_available():
            capabilities = get_enhanced_capabilities()
            enhancement_notice = f"\n\n🚀 **Enhanced Capabilities Available:**\n"
            for cap in capabilities[:3]:  # Show top 3
                enhancement_notice += f"• {cap}\n"
            enhancement_notice += f"• And {len(capabilities)-3} more...\n"
            enhancement_notice += f"\n💡 *Try: 'Greeks for AAPL' or 'Portfolio analysis'*"
            
            return response + enhancement_notice
        else:
            return response
    
    def _add_enhancement_notice(self, response: str) -> str:
        """Add notice about available enhancements."""
        notice = f"\n\n💡 **Enhanced MCP Features Available**\n" \
                f"Add alpaca-mcp-server for advanced capabilities:\n" \
                f"• Advanced options Greeks analysis\n" \
                f"• Enhanced portfolio metrics\n" \
                f"• Corporate actions intelligence\n" \
                f"• Smart order execution"
        
        return response + notice

# Global instance
_enhanced_chat = None

def get_enhanced_chat() -> EnhancedChatIntegration:
    """Get the global enhanced chat integration instance."""
    global _enhanced_chat
    if _enhanced_chat is None:
        _enhanced_chat = EnhancedChatIntegration()
    return _enhanced_chat

def enhanced_chat_gpt(message: str) -> str:
    """Enhanced chat function that adds MCP capabilities to existing chat."""
    try:
        if not CHAT_AVAILABLE:
            return "❌ Chat system not available"
        
        # Get original response from existing system
        original_response = chat_gpt(message)
        
        # Enhance with MCP capabilities if available
        enhanced_chat = get_enhanced_chat()
        enhanced_response = enhanced_chat.enhance_chat_response(message, original_response)
        
        return enhanced_response
        
    except Exception as e:
        error(f"Enhanced chat failed: {e}")
        # Fallback to original chat if available
        if CHAT_AVAILABLE:
            return chat_gpt(message)
        else:
            return f"❌ Chat system error: {str(e)}"

def initialize_enhanced_chat() -> bool:
    """Initialize enhanced chat integration."""
    try:
        enhanced_chat = get_enhanced_chat()
        
        if enhanced_chat.is_initialized:
            info("✅ Enhanced chat integration ready")
            info("💡 Enhanced commands available:")
            info("   • 'Greeks for AAPL' - Advanced options analysis")
            info("   • 'Portfolio analysis' - Enhanced portfolio metrics")
            info("   • 'Earnings calendar' - Corporate actions intelligence")
            info("   • 'Buy 100 AAPL with stop' - Smart order execution")
            return True
        else:
            warning("⚠️ Enhanced chat initialization incomplete")
            return False
            
    except Exception as e:
        error(f"Enhanced chat initialization failed: {e}")
        return False
