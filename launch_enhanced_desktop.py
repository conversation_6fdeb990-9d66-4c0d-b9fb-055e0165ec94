#!/usr/bin/env python3
"""
Launch Enhanced Desktop Interface
Your existing desktop app now with Incite AI features!
"""
import os
import sys
import subprocess
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are available"""
    required = ['tkinter', 'PIL']
    optional = ['openai']
    
    print("🔍 Checking dependencies...")
    
    missing = []
    for package in required:
        try:
            if package == 'tkinter':
                import tkinter
            elif package == 'PIL':
                from PIL import Image
            print(f"   ✅ {package}")
        except ImportError:
            missing.append(package)
            print(f"   ❌ {package}")
    
    for package in optional:
        try:
            if package == 'openai':
                import openai
            print(f"   ✅ {package} (optional)")
        except ImportError:
            print(f"   ⚠️  {package} (optional - for AI vision)")
    
    if missing:
        print(f"\n📦 Install missing packages:")
        if 'PIL' in missing:
            print("   pip install pillow")
        return False
    
    return True

def setup_directories():
    """Create necessary directories"""
    directories = ['data', 'logs', 'uploads']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)

def launch_desktop_interface():
    """Launch the enhanced desktop interface"""
    
    print("🖥️  LAUNCHING ENHANCED DESKTOP INTERFACE")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        print("\n❌ Cannot launch - missing required dependencies")
        return False
    
    # Setup directories
    setup_directories()
    
    # Check if interface file exists
    interface_file = Path('gui/tkinter_trading_interface.py')
    
    if not interface_file.exists():
        print(f"❌ Interface file not found: {interface_file}")
        print("💡 Make sure you're running from the project root directory")
        return False
    
    print("🚀 Starting enhanced desktop interface...")
    print("🎨 New features: Chart Upload, Deep Search, Intent Detection")
    
    try:
        # Launch the interface
        cmd = [sys.executable, str(interface_file)]
        
        print(f"🔧 Command: {' '.join(cmd)}")
        
        # Start the process
        process = subprocess.Popen(cmd)
        
        print("✅ Desktop interface launched successfully!")
        print()
        print("🖥️  **YOUR ENHANCED DESKTOP INTERFACE IS NOW RUNNING!**")
        print()
        print("🎨 **NEW INCITE AI FEATURES ADDED:**")
        print("   📈 Chart Upload & AI Vision Analysis")
        print("   🔍 Deep Search through trading data")
        print("   🎯 Intent detection in chat")
        print("   💬 Enhanced AI conversations")
        print()
        print("📋 **HOW TO USE NEW FEATURES:**")
        print()
        print("   1️⃣ **Chart Upload:**")
        print("      • Go to '🎨 Incite AI Features' tab")
        print("      • Click 'Upload Chart Image'")
        print("      • Select Think or Swim screenshot")
        print("      • Get AI vision analysis!")
        print()
        print("   2️⃣ **Deep Search:**")
        print("      • Use the search box in Incite AI tab")
        print("      • Search: 'AAPL trades', 'TTM setups', etc.")
        print("      • Get instant results from all data")
        print()
        print("   3️⃣ **Intent Detection:**")
        print("      • Chat normally in the AI Chat tab")
        print("      • See intent detection below your messages")
        print("      • AI understands what you want!")
        print()
        print("⚡ **QUICK TESTS:**")
        print("   💬 Chat: 'What's happening in my system?'")
        print("   📈 Upload: Any chart screenshot")
        print("   🔍 Search: 'TTM squeeze opportunities'")
        print()
        print("🏆 **YOU NOW HAVE INCITE AI FEATURES IN YOUR DESKTOP APP!**")
        
        # Wait for process to complete
        try:
            process.wait()
        except KeyboardInterrupt:
            print("\n🛑 Shutting down interface...")
            process.terminate()
            process.wait()
            print("✅ Interface stopped")
        
        return True
        
    except Exception as e:
        print(f"❌ Error launching interface: {e}")
        return False

def show_help():
    """Show help information"""
    print("🖥️  ENHANCED DESKTOP INTERFACE LAUNCHER")
    print("=" * 45)
    print()
    print("🎯 **DESCRIPTION:**")
    print("   Launches your existing desktop trading interface")
    print("   now enhanced with Incite AI style features!")
    print()
    print("✨ **NEW FEATURES ADDED:**")
    print("   📈 Chart Upload & AI Vision Analysis")
    print("   🔍 Deep Search through trading data")
    print("   🎯 Intent detection in chat")
    print("   💬 Enhanced AI conversations")
    print()
    print("🎨 **INCITE AI FEATURES:**")
    print("   • Upload Think or Swim charts")
    print("   • AI vision pattern recognition")
    print("   • Search all trading decisions")
    print("   • Smart intent detection")
    print("   • Professional styling")
    print()
    print("🚀 **USAGE:**")
    print("   python launch_enhanced_desktop.py")
    print()
    print("📋 **REQUIREMENTS:**")
    print("   • Python 3.8+")
    print("   • tkinter (usually included)")
    print("   • PIL/Pillow for image processing")
    print("   • Optional: OpenAI for AI vision")
    print()
    print("💡 **TIPS:**")
    print("   • Look for the new '🎨 Incite AI Features' tab")
    print("   • Try uploading chart screenshots")
    print("   • Use the Deep Search functionality")
    print("   • Notice intent detection in chat")

def main():
    """Main launcher function"""
    
    if len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h', 'help']:
        show_help()
        return
    
    print("🎨 ENHANCED DESKTOP INTERFACE LAUNCHER")
    print("🖥️  Adding Incite AI Features to Your Desktop App")
    print("=" * 55)
    
    success = launch_desktop_interface()
    
    if success:
        print("\n🎉 **LAUNCH SUCCESSFUL!**")
        print("Your desktop interface now has Incite AI features!")
        print("Enjoy the enhanced trading experience! 🏆")
    else:
        print("\n❌ **LAUNCH FAILED**")
        print("Please check the error messages above.")
        print("\nFor help: python launch_enhanced_desktop.py --help")

if __name__ == "__main__":
    main()
