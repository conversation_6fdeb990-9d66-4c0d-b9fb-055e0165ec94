"""Launch the Ultimate Trading Expert GUI Interface

Simple launcher for the comprehensive trading interface with:
- Chat with AI trading expert
- TTM Squeeze scanner with A-F grading
- Position management and dynamic stops
- Real-time alerts for A/B grade opportunities

Usage:
    python launch_gui.py
"""
from __future__ import annotations

import sys
from pathlib import Path

# Add necessary directories to path for imports
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))  # core/
sys.path.insert(0, str(current_dir.parent / 'gui'))  # gui/
sys.path.insert(0, str(current_dir.parent / 'scanners'))  # scanners/
sys.path.insert(0, str(current_dir.parent / 'trading'))  # trading/
sys.path.insert(0, str(current_dir.parent / 'utils'))  # utils/

try:
    from tkinter_trading_interface import main

    if __name__ == "__main__":
        print("🚀 Launching Ultimate Trading Expert GUI...")
        print("📊 Features:")
        print("  • AI Chat Interface")
        print("  • TTM Squeeze Scanner with A-F Grading")
        print("  • Dynamic Stop Loss Management")
        print("  • Real-time Alerts for High-Grade Opportunities")
        print("  • Position Sizing and Risk Management")
        print("\n🎯 Starting tkinter interface...")

        main()

except ImportError as e:
    print(f"❌ Import Error: {e}")
    print("\n📋 Required dependencies:")
    print("  • tkinter (built-in with Python)")
    print("  • All trading modules")
    print("\n💡 tkinter should be included with Python by default")

except Exception as e:
    print(f"❌ Error launching interface: {e}")
    print("\n📞 Check that all configuration files are set up correctly.")
