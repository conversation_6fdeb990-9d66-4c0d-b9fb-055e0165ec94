#!/usr/bin/env python3
"""
Deep Search RAG System - Like OpenAI Deep Search
Vector embeddings + retrieval for complete trading system awareness
"""
import os
import json
import sqlite3
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import numpy as np
from dataclasses import dataclass
import logging

# Try to import required libraries
try:
    import openai
    from sentence_transformers import SentenceTransformer
    import faiss
    import chromadb
    EMBEDDINGS_AVAILABLE = True
except ImportError:
    EMBEDDINGS_AVAILABLE = False
    print("⚠️  Deep Search requires: pip install openai sentence-transformers faiss-cpu chromadb")

@dataclass
class SearchResult:
    """Search result with relevance score"""
    content: str
    metadata: Dict[str, Any]
    score: float
    timestamp: datetime
    source: str

class DeepSearchRAG:
    """
    Deep Search RAG System for Trading Intelligence
    
    Embeds and searches through:
    - Trade logs and decisions
    - Scanner results and analysis
    - AI brain state and reasoning
    - Performance data and insights
    - Chart analysis and patterns
    """
    
    def __init__(self, db_path: str = "data/deep_search.db"):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        
        # Initialize embedding model
        if EMBEDDINGS_AVAILABLE:
            try:
                self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
                self.embedding_dim = 384
                self.embeddings_ready = True
            except Exception as e:
                self.logger.warning(f"Failed to load embedding model: {e}")
                self.embeddings_ready = False
        else:
            self.embeddings_ready = False
        
        # Initialize vector store
        self.vector_store = None
        self.document_store = {}
        
        self._setup_database()
        self._initialize_vector_store()
    
    def _setup_database(self):
        """Setup SQLite database for document storage"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS documents (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    content TEXT NOT NULL,
                    metadata TEXT NOT NULL,
                    source TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    embedding_id INTEGER
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS embeddings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    vector BLOB NOT NULL,
                    document_id INTEGER,
                    FOREIGN KEY (document_id) REFERENCES documents (id)
                )
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_source ON documents(source)
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_timestamp ON documents(timestamp)
            """)
    
    def _initialize_vector_store(self):
        """Initialize FAISS vector store"""
        if not self.embeddings_ready:
            return
        
        try:
            # Try to load existing index
            index_path = self.db_path.replace('.db', '.faiss')
            if os.path.exists(index_path):
                self.vector_store = faiss.read_index(index_path)
                self.logger.info("Loaded existing FAISS index")
            else:
                # Create new index
                self.vector_store = faiss.IndexFlatIP(self.embedding_dim)
                self.logger.info("Created new FAISS index")
        except Exception as e:
            self.logger.error(f"Failed to initialize vector store: {e}")
            self.vector_store = None
    
    def add_document(self, content: str, metadata: Dict[str, Any], source: str):
        """Add document to the search index"""
        if not content.strip():
            return
        
        timestamp = datetime.now().isoformat()
        
        # Store in database
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                INSERT INTO documents (content, metadata, source, timestamp)
                VALUES (?, ?, ?, ?)
            """, (content, json.dumps(metadata), source, timestamp))
            
            doc_id = cursor.lastrowid
        
        # Create and store embedding
        if self.embeddings_ready and self.vector_store is not None:
            try:
                embedding = self.embedding_model.encode([content])[0]
                
                # Add to FAISS index
                self.vector_store.add(np.array([embedding], dtype=np.float32))
                
                # Store embedding in database
                with sqlite3.connect(self.db_path) as conn:
                    conn.execute("""
                        INSERT INTO embeddings (vector, document_id)
                        VALUES (?, ?)
                    """, (embedding.tobytes(), doc_id))
                
                self.logger.debug(f"Added document {doc_id} to search index")
                
            except Exception as e:
                self.logger.error(f"Failed to create embedding: {e}")
    
    def search(self, query: str, top_k: int = 5, source_filter: Optional[str] = None) -> List[SearchResult]:
        """Search for relevant documents"""
        if not self.embeddings_ready or self.vector_store is None:
            return self._fallback_search(query, top_k, source_filter)
        
        try:
            # Create query embedding
            query_embedding = self.embedding_model.encode([query])[0]
            
            # Search vector store
            scores, indices = self.vector_store.search(
                np.array([query_embedding], dtype=np.float32), 
                min(top_k * 2, self.vector_store.ntotal)  # Get more results for filtering
            )
            
            results = []
            
            # Retrieve documents
            with sqlite3.connect(self.db_path) as conn:
                for score, idx in zip(scores[0], indices[0]):
                    if idx == -1:  # FAISS returns -1 for invalid indices
                        continue
                    
                    # Get document by embedding index (idx + 1 because FAISS is 0-indexed)
                    cursor = conn.execute("""
                        SELECT d.content, d.metadata, d.source, d.timestamp
                        FROM documents d
                        JOIN embeddings e ON d.id = e.document_id
                        WHERE e.id = ?
                    """, (idx + 1,))
                    
                    row = cursor.fetchone()
                    if row:
                        content, metadata_json, source, timestamp = row
                        
                        # Apply source filter
                        if source_filter and source != source_filter:
                            continue
                        
                        metadata = json.loads(metadata_json)
                        
                        results.append(SearchResult(
                            content=content,
                            metadata=metadata,
                            score=float(score),
                            timestamp=datetime.fromisoformat(timestamp),
                            source=source
                        ))
                        
                        if len(results) >= top_k:
                            break
            
            return results
            
        except Exception as e:
            self.logger.error(f"Search failed: {e}")
            return self._fallback_search(query, top_k, source_filter)
    
    def _fallback_search(self, query: str, top_k: int, source_filter: Optional[str]) -> List[SearchResult]:
        """Fallback text-based search when embeddings unavailable"""
        query_terms = query.lower().split()
        results = []
        
        with sqlite3.connect(self.db_path) as conn:
            # Build WHERE clause
            where_clause = "WHERE 1=1"
            params = []
            
            if source_filter:
                where_clause += " AND source = ?"
                params.append(source_filter)
            
            # Simple text matching
            for term in query_terms:
                where_clause += " AND LOWER(content) LIKE ?"
                params.append(f"%{term}%")
            
            cursor = conn.execute(f"""
                SELECT content, metadata, source, timestamp
                FROM documents
                {where_clause}
                ORDER BY timestamp DESC
                LIMIT ?
            """, params + [top_k])
            
            for row in cursor.fetchall():
                content, metadata_json, source, timestamp = row
                metadata = json.loads(metadata_json)
                
                # Simple relevance scoring
                score = sum(1 for term in query_terms if term in content.lower()) / len(query_terms)
                
                results.append(SearchResult(
                    content=content,
                    metadata=metadata,
                    score=score,
                    timestamp=datetime.fromisoformat(timestamp),
                    source=source
                ))
        
        return sorted(results, key=lambda x: x.score, reverse=True)
    
    def add_trade_decision(self, symbol: str, action: str, reasoning: str, metadata: Dict[str, Any]):
        """Add trade decision to search index"""
        content = f"Trade Decision: {action} {symbol}\nReasoning: {reasoning}"
        
        trade_metadata = {
            "type": "trade_decision",
            "symbol": symbol,
            "action": action,
            **metadata
        }
        
        self.add_document(content, trade_metadata, "trade_decisions")
    
    def add_scanner_result(self, symbol: str, grade: str, confidence: float, reasoning: str, metadata: Dict[str, Any]):
        """Add scanner result to search index"""
        content = f"TTM Scanner: {symbol} Grade {grade} (Confidence: {confidence})\nAnalysis: {reasoning}"
        
        scanner_metadata = {
            "type": "scanner_result",
            "symbol": symbol,
            "grade": grade,
            "confidence": confidence,
            **metadata
        }
        
        self.add_document(content, scanner_metadata, "scanner_results")
    
    def add_ai_insight(self, insight_type: str, content: str, metadata: Dict[str, Any]):
        """Add AI insight to search index"""
        ai_metadata = {
            "type": "ai_insight",
            "insight_type": insight_type,
            **metadata
        }
        
        self.add_document(content, ai_metadata, "ai_insights")
    
    def add_performance_data(self, period: str, metrics: Dict[str, Any], analysis: str):
        """Add performance analysis to search index"""
        content = f"Performance Analysis ({period}): {analysis}"
        
        perf_metadata = {
            "type": "performance",
            "period": period,
            "metrics": metrics
        }
        
        self.add_document(content, perf_metadata, "performance")
    
    def add_chart_analysis(self, symbol: str, timeframe: str, analysis: str, metadata: Dict[str, Any]):
        """Add chart analysis to search index"""
        content = f"Chart Analysis: {symbol} ({timeframe})\n{analysis}"
        
        chart_metadata = {
            "type": "chart_analysis",
            "symbol": symbol,
            "timeframe": timeframe,
            **metadata
        }
        
        self.add_document(content, chart_metadata, "chart_analysis")
    
    def get_context_for_query(self, query: str, max_context_length: int = 2000) -> str:
        """Get relevant context for AI query"""
        results = self.search(query, top_k=5)
        
        context_parts = []
        current_length = 0
        
        for result in results:
            content_preview = result.content[:300] + "..." if len(result.content) > 300 else result.content
            
            context_part = f"[{result.source}] {content_preview}"
            
            if current_length + len(context_part) > max_context_length:
                break
            
            context_parts.append(context_part)
            current_length += len(context_part)
        
        return "\n\n".join(context_parts)
    
    def save_index(self):
        """Save FAISS index to disk"""
        if self.vector_store is not None:
            try:
                index_path = self.db_path.replace('.db', '.faiss')
                faiss.write_index(self.vector_store, index_path)
                self.logger.info(f"Saved FAISS index to {index_path}")
            except Exception as e:
                self.logger.error(f"Failed to save index: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get search index statistics"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("SELECT COUNT(*) FROM documents")
            total_docs = cursor.fetchone()[0]
            
            cursor = conn.execute("""
                SELECT source, COUNT(*) 
                FROM documents 
                GROUP BY source
            """)
            by_source = dict(cursor.fetchall())
            
            cursor = conn.execute("""
                SELECT DATE(timestamp) as date, COUNT(*) 
                FROM documents 
                WHERE timestamp >= ?
                GROUP BY DATE(timestamp)
                ORDER BY date DESC
                LIMIT 7
            """, ((datetime.now() - timedelta(days=7)).isoformat(),))
            recent_activity = dict(cursor.fetchall())
        
        return {
            "total_documents": total_docs,
            "by_source": by_source,
            "recent_activity": recent_activity,
            "embeddings_enabled": self.embeddings_ready,
            "vector_store_size": self.vector_store.ntotal if self.vector_store else 0
        }

# Global instance
_deep_search = None

def get_deep_search() -> DeepSearchRAG:
    """Get global deep search instance"""
    global _deep_search
    if _deep_search is None:
        _deep_search = DeepSearchRAG()
    return _deep_search

def search_trading_data(query: str, top_k: int = 5) -> List[SearchResult]:
    """Convenience function for searching trading data"""
    return get_deep_search().search(query, top_k)

def add_to_search_index(content: str, metadata: Dict[str, Any], source: str):
    """Convenience function for adding to search index"""
    get_deep_search().add_document(content, metadata, source)

if __name__ == "__main__":
    # Test the deep search system
    deep_search = DeepSearchRAG()
    
    # Add some test data
    deep_search.add_trade_decision(
        "AAPL", "BUY", 
        "Strong TTM squeeze with A+ grade and high volume confirmation",
        {"confidence": 92, "entry_price": 150.50}
    )
    
    deep_search.add_scanner_result(
        "NVDA", "A", 88.5,
        "Volume surge with momentum confirmation, semiconductor strength",
        {"timeframe": "15min", "volume_ratio": 1.8}
    )
    
    # Test search
    results = deep_search.search("AAPL trade decision")
    print(f"Found {len(results)} results")
    
    for result in results:
        print(f"Score: {result.score:.2f}")
        print(f"Source: {result.source}")
        print(f"Content: {result.content[:100]}...")
        print("---")
    
    # Show stats
    stats = deep_search.get_stats()
    print(f"Index stats: {stats}")
