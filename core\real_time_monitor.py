#!/usr/bin/env python3
"""Real-Time TTM Monitoring System

Provides live monitoring of:
- Active TTM positions with real-time P&L
- New TTM setup alerts
- Automatic stop-loss adjustments
- Market condition changes
- Risk management alerts
"""
import threading
import time
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable
import json


class RealTimeMonitor:
    """Real-time monitoring system for TTM trading."""
    
    def __init__(self):
        self.is_running = False
        self.monitor_thread = None
        self.positions = {}
        self.alerts = []
        self.callbacks = {
            'new_setup': [],
            'position_update': [],
            'stop_loss_hit': [],
            'target_reached': [],
            'risk_alert': []
        }
        
        # Configuration
        self.update_interval = 30  # seconds
        self.max_alerts = 100
        
        # API setup
        self.fmp_key = None
        try:
            from config import get_api_key
            self.fmp_key = get_api_key('FMP_API_KEY')
        except:
            pass
    
    def start_monitoring(self):
        """Start the real-time monitoring system."""
        if self.is_running:
            return "⚠️ Monitoring already running"

        # Reset any previous state
        self.is_running = True
        self.alerts = []  # Clear old alerts

        # Start monitoring thread
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()

        self.add_alert("🚀 Real-time monitoring started", "SYSTEM")
        return "✅ Real-time monitoring started successfully"
    
    def stop_monitoring(self):
        """Stop the real-time monitoring system."""
        self.is_running = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        
        self.add_alert("⏹️ Real-time monitoring stopped", "SYSTEM")
        return "✅ Real-time monitoring stopped"
    
    def add_position(self, symbol: str, entry_price: float, shares: int, 
                    stop_loss: float, take_profit: float, strategy: str = "TTM"):
        """Add a position to monitor."""
        position_id = f"{symbol}_{int(time.time())}"
        
        self.positions[position_id] = {
            'symbol': symbol,
            'entry_price': entry_price,
            'shares': shares,
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'strategy': strategy,
            'entry_time': datetime.now(),
            'current_price': entry_price,
            'unrealized_pnl': 0.0,
            'status': 'ACTIVE',
            'alerts_sent': []
        }
        
        self.add_alert(f"📊 Added position: {symbol} ({shares} shares @ ${entry_price:.2f})", "POSITION")
        return position_id
    
    def remove_position(self, position_id: str):
        """Remove a position from monitoring."""
        if position_id in self.positions:
            position = self.positions[position_id]
            del self.positions[position_id]
            self.add_alert(f"❌ Removed position: {position['symbol']}", "POSITION")
            return True
        return False
    
    def update_stop_loss(self, position_id: str, new_stop: float):
        """Update stop loss for a position."""
        if position_id in self.positions:
            old_stop = self.positions[position_id]['stop_loss']
            self.positions[position_id]['stop_loss'] = new_stop
            
            symbol = self.positions[position_id]['symbol']
            self.add_alert(f"🛡️ Updated stop loss for {symbol}: ${old_stop:.2f} → ${new_stop:.2f}", "STOP_LOSS")
            return True
        return False
    
    def get_positions_status(self) -> Dict:
        """Get current status of all positions."""
        if not self.positions:
            return {
                'total_positions': 0,
                'total_pnl': 0.0,
                'active_positions': [],
                'summary': "No active positions"
            }
        
        total_pnl = sum(pos['unrealized_pnl'] for pos in self.positions.values())
        active_positions = []
        
        for pos_id, pos in self.positions.items():
            active_positions.append({
                'id': pos_id,
                'symbol': pos['symbol'],
                'shares': pos['shares'],
                'entry_price': pos['entry_price'],
                'current_price': pos['current_price'],
                'unrealized_pnl': pos['unrealized_pnl'],
                'pnl_percent': (pos['unrealized_pnl'] / (pos['entry_price'] * pos['shares'])) * 100,
                'stop_loss': pos['stop_loss'],
                'take_profit': pos['take_profit'],
                'status': pos['status'],
                'duration': str(datetime.now() - pos['entry_time']).split('.')[0]
            })
        
        return {
            'total_positions': len(self.positions),
            'total_pnl': total_pnl,
            'active_positions': active_positions,
            'summary': f"{len(self.positions)} active positions, ${total_pnl:.2f} total P&L"
        }
    
    def get_recent_alerts(self, limit: int = 10) -> List[Dict]:
        """Get recent alerts."""
        return self.alerts[-limit:] if self.alerts else []
    
    def add_alert(self, message: str, alert_type: str = "INFO"):
        """Add an alert to the system."""
        alert = {
            'timestamp': datetime.now(),
            'message': message,
            'type': alert_type
        }
        
        self.alerts.append(alert)
        
        # Keep only recent alerts
        if len(self.alerts) > self.max_alerts:
            self.alerts = self.alerts[-self.max_alerts:]
        
        # Trigger callbacks
        for callback in self.callbacks.get(alert_type.lower(), []):
            try:
                callback(alert)
            except:
                pass
    
    def register_callback(self, event_type: str, callback: Callable):
        """Register a callback for specific events."""
        if event_type in self.callbacks:
            self.callbacks[event_type].append(callback)
    
    def _monitoring_loop(self):
        """Main monitoring loop with enhanced error handling."""
        consecutive_errors = 0
        max_consecutive_errors = 5

        self.add_alert("🚀 Real-time monitoring loop started", "SYSTEM")

        while self.is_running:
            try:
                # Update position prices (with timeout protection)
                try:
                    self._update_position_prices()
                except Exception as e:
                    self.add_alert(f"⚠️ Position update error: {str(e)}", "ERROR")

                # Check for stop losses and targets
                try:
                    self._check_position_triggers()
                except Exception as e:
                    self.add_alert(f"⚠️ Position trigger error: {str(e)}", "ERROR")

                # Scan for new TTM setups (with timeout protection)
                try:
                    self._scan_for_new_setups()
                except Exception as e:
                    self.add_alert(f"⚠️ Setup scan error: {str(e)}", "ERROR")

                # Check market conditions (with timeout protection)
                try:
                    self._check_market_conditions()
                except Exception as e:
                    self.add_alert(f"⚠️ Market check error: {str(e)}", "ERROR")

                # Reset error counter on successful loop
                consecutive_errors = 0

                # Sleep between iterations
                time.sleep(self.update_interval)

            except Exception as e:
                consecutive_errors += 1
                error_msg = f"⚠️ Monitoring loop error #{consecutive_errors}: {str(e)}"
                self.add_alert(error_msg, "ERROR")

                # If too many consecutive errors, stop monitoring
                if consecutive_errors >= max_consecutive_errors:
                    self.add_alert(f"🚨 Too many errors ({consecutive_errors}), stopping monitoring", "ERROR")
                    self.is_running = False
                    break

                # Wait longer after errors
                time.sleep(min(self.update_interval * consecutive_errors, 60))

        self.add_alert("⏹️ Real-time monitoring loop stopped", "SYSTEM")

        # If monitoring stopped due to errors but should still be running, try to restart
        if self.is_running and consecutive_errors >= max_consecutive_errors:
            self.add_alert("🔄 Attempting to restart monitoring after errors...", "SYSTEM")
            time.sleep(10)  # Wait before restart
            if self.is_running:  # Check if still supposed to be running
                try:
                    self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
                    self.monitor_thread.start()
                    self.add_alert("✅ Monitoring restarted successfully", "SYSTEM")
                except Exception as e:
                    self.add_alert(f"❌ Failed to restart monitoring: {str(e)}", "ERROR")
                    self.is_running = False
    
    def _update_position_prices(self):
        """Update current prices for all positions."""
        if not self.positions or not self.fmp_key:
            return
        
        # Get unique symbols
        symbols = list(set(pos['symbol'] for pos in self.positions.values()))
        
        for symbol in symbols:
            try:
                # Get current price
                url = f"https://financialmodelingprep.com/api/v3/quote/{symbol}?apikey={self.fmp_key}"
                response = requests.get(url, timeout=10)
                
                if response.status_code == 200:
                    data = response.json()
                    if data and len(data) > 0:
                        current_price = data[0].get('price', 0)
                        
                        # Update all positions for this symbol
                        for pos_id, pos in self.positions.items():
                            if pos['symbol'] == symbol:
                                old_price = pos['current_price']
                                pos['current_price'] = current_price
                                
                                # Calculate P&L
                                pos['unrealized_pnl'] = (current_price - pos['entry_price']) * pos['shares']
                                
                                # Check for significant price changes
                                price_change_pct = abs(current_price - old_price) / old_price * 100
                                if price_change_pct > 2:  # 2% change
                                    self.add_alert(f"📈 {symbol} price update: ${old_price:.2f} → ${current_price:.2f} ({price_change_pct:.1f}%)", "PRICE_UPDATE")
                
            except Exception as e:
                self.add_alert(f"⚠️ Failed to update price for {symbol}: {str(e)}", "ERROR")
    
    def _check_position_triggers(self):
        """Check if any positions hit stop loss or take profit."""
        positions_to_close = []
        
        for pos_id, pos in self.positions.items():
            current_price = pos['current_price']
            symbol = pos['symbol']
            
            # Check stop loss
            if current_price <= pos['stop_loss']:
                loss = (pos['stop_loss'] - pos['entry_price']) * pos['shares']
                self.add_alert(f"🛑 STOP LOSS HIT: {symbol} @ ${current_price:.2f} (Loss: ${loss:.2f})", "STOP_LOSS")
                positions_to_close.append(pos_id)
                
                # Trigger callback
                for callback in self.callbacks['stop_loss_hit']:
                    try:
                        callback(pos)
                    except:
                        pass
            
            # Check take profit
            elif current_price >= pos['take_profit']:
                profit = (pos['take_profit'] - pos['entry_price']) * pos['shares']
                self.add_alert(f"🎯 TARGET REACHED: {symbol} @ ${current_price:.2f} (Profit: ${profit:.2f})", "TARGET")
                positions_to_close.append(pos_id)
                
                # Trigger callback
                for callback in self.callbacks['target_reached']:
                    try:
                        callback(pos)
                    except:
                        pass
        
        # Close triggered positions
        for pos_id in positions_to_close:
            self.positions[pos_id]['status'] = 'CLOSED'
    
    def _scan_for_new_setups(self):
        """Scan for new TTM setups using real scanner."""
        try:
            # Check every 5 minutes for new setups
            current_time = datetime.now()
            if not hasattr(self, '_last_scan') or (current_time - self._last_scan).seconds > 300:
                self._last_scan = current_time

                # Use real TTM scanner with timeout protection
                try:
                    from scanners.proper_ttm_squeeze_scanner import run_proper_ttm_scan

                    # Add timeout protection using threading
                    import threading
                    import queue

                    def run_scanner_with_timeout():
                        result_queue = queue.Queue()

                        def scanner_thread():
                            try:
                                result = run_proper_ttm_scan()
                                result_queue.put(('success', result))
                            except Exception as e:
                                result_queue.put(('error', str(e)))

                        # Start scanner in separate thread
                        thread = threading.Thread(target=scanner_thread, daemon=True)
                        thread.start()

                        # Wait for result with timeout
                        try:
                            status, result = result_queue.get(timeout=30)  # 30 second timeout
                            if status == 'success':
                                return result
                            else:
                                raise Exception(result)
                        except queue.Empty:
                            self.add_alert("⚠️ TTM scanner timeout (30s), using fallback", "WARNING")
                            raise ImportError("Scanner timeout")

                    scan_result = run_scanner_with_timeout()

                    # Parse scan results for high-grade setups
                    if isinstance(scan_result, str) and "📈" in scan_result:
                        setups = self._parse_ttm_scan_results(scan_result)

                        # Process each setup for automation
                        for setup in setups:
                            if setup['grade'] in ['A+', 'A'] and setup['confidence'] >= 80:
                                self.add_alert(f"🔥 NEW TTM SETUP: {setup['symbol']} Grade {setup['grade']} ({setup['confidence']:.0f}%) - {setup['timeframe']}", "NEW_SETUP")

                                # Trigger callback for automation with full setup data
                                for callback in self.callbacks['new_setup']:
                                    try:
                                        callback(setup)
                                    except Exception as e:
                                        print(f"Callback error: {e}")

                except ImportError:
                    # Scanner not available - no fallback mock data
                    self.add_alert("⚠️ TTM scanner not available - install scanner dependencies", "WARNING")

        except Exception as e:
            self.add_alert(f"⚠️ Setup scan error: {str(e)}", "ERROR")

    def _parse_ttm_scan_results(self, scan_result: str) -> List[Dict]:
        """Parse TTM scan results into structured setup data."""
        setups = []
        lines = scan_result.split('\n')

        current_setup = None

        for line in lines:
            line = line.strip()

            # Look for setup lines starting with 📈
            if line.startswith('📈'):
                try:
                    # Parse main setup line: 📈 AAPL (5min) - Grade A+ (100%)
                    parts = line.split(' - ')
                    if len(parts) >= 2:
                        # Extract symbol and timeframe
                        symbol_part = parts[0].replace('📈', '').strip()
                        symbol_timeframe = symbol_part.split('(')
                        symbol = symbol_timeframe[0].strip()
                        timeframe = symbol_timeframe[1].replace(')', '').strip() if len(symbol_timeframe) > 1 else '15min'

                        # Extract grade and confidence
                        grade_part = parts[1]
                        grade = 'B'
                        confidence = 85

                        if 'Grade A+' in grade_part:
                            grade = 'A+'
                        elif 'Grade A' in grade_part:
                            grade = 'A'
                        elif 'Grade B+' in grade_part:
                            grade = 'B+'
                        elif 'Grade B' in grade_part:
                            grade = 'B'

                        # Extract confidence percentage
                        if '(' in grade_part and '%' in grade_part:
                            try:
                                conf_part = grade_part.split('(')[-1].split('%')[0]
                                confidence = float(conf_part)
                            except:
                                pass

                        current_setup = {
                            'symbol': symbol,
                            'timeframe': timeframe,
                            'grade': grade,
                            'confidence': confidence,
                            'timestamp': datetime.now(),
                            'squeeze_release': False,
                            'momentum_up': False,
                            'price': 0.0,
                            'entry_price': 0.0,
                            'stop_loss': 0.0,
                            'target_price': 0.0
                        }

                except Exception as e:
                    print(f"Error parsing setup line: {e}")
                    continue

            # Look for status and momentum line
            elif current_setup and ('🔥 SQUEEZE RELEASE' in line or '⏳ IN SQUEEZE' in line):
                try:
                    current_setup['squeeze_release'] = '🔥 SQUEEZE RELEASE' in line
                    current_setup['momentum_up'] = '📈 UP' in line

                    # Extract current price
                    if '$' in line:
                        price_part = line.split('$')[-1].strip()
                        current_setup['price'] = float(price_part)

                except Exception as e:
                    print(f"Error parsing status line: {e}")

            # Look for entry/stop/target line
            elif current_setup and 'Entry:' in line and 'Stop:' in line and 'Target:' in line:
                try:
                    # Parse: Entry: $205.87 | Stop: $201.75 | Target: $218.22
                    parts = line.split('|')

                    for part in parts:
                        part = part.strip()
                        if part.startswith('Entry:'):
                            entry_str = part.replace('Entry:', '').replace('$', '').strip()
                            current_setup['entry_price'] = float(entry_str)
                        elif part.startswith('Stop:'):
                            stop_str = part.replace('Stop:', '').replace('$', '').strip()
                            current_setup['stop_loss'] = float(stop_str)
                        elif part.startswith('Target:'):
                            target_str = part.replace('Target:', '').replace('$', '').strip()
                            current_setup['target_price'] = float(target_str)

                    # Setup is complete, add to list
                    if current_setup['entry_price'] > 0:
                        setups.append(current_setup.copy())

                    current_setup = None

                except Exception as e:
                    print(f"Error parsing entry/stop/target line: {e}")

        return setups
    
    def _check_market_conditions(self):
        """Check overall market conditions."""
        try:
            # Check VIX for volatility changes
            if not self.fmp_key:
                return
            
            url = f"https://financialmodelingprep.com/api/v3/quote/^VIX?apikey={self.fmp_key}"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data and len(data) > 0:
                    vix = data[0].get('price', 0)
                    
                    # Alert on high volatility
                    if vix > 30 and not hasattr(self, '_high_vix_alerted'):
                        self.add_alert(f"⚠️ HIGH VOLATILITY: VIX at {vix:.1f} - Consider tighter stops", "RISK_ALERT")
                        self._high_vix_alerted = True
                    elif vix <= 25:
                        self._high_vix_alerted = False
                        
        except Exception as e:
            self.add_alert(f"⚠️ Market condition check error: {str(e)}", "ERROR")


# Global monitor instance
_monitor = None

def get_monitor() -> RealTimeMonitor:
    """Get the global monitor instance."""
    global _monitor
    if _monitor is None:
        _monitor = RealTimeMonitor()
    return _monitor


def start_real_time_monitoring() -> str:
    """Start real-time monitoring."""
    monitor = get_monitor()
    return monitor.start_monitoring()


def stop_real_time_monitoring() -> str:
    """Stop real-time monitoring."""
    monitor = get_monitor()
    return monitor.stop_monitoring()


def get_monitoring_status() -> str:
    """Get current monitoring status."""
    monitor = get_monitor()
    
    if not monitor.is_running:
        return "📊 **Real-Time Monitoring: STOPPED**\n\nUse 'start monitoring' to begin live tracking."
    
    status = monitor.get_positions_status()
    recent_alerts = monitor.get_recent_alerts(5)
    
    response = f"📊 **Real-Time Monitoring: ACTIVE**\n\n"
    response += f"**Positions:** {status['summary']}\n\n"
    
    if status['active_positions']:
        response += "**Active Positions:**\n"
        for pos in status['active_positions']:
            pnl_emoji = "🟢" if pos['unrealized_pnl'] >= 0 else "🔴"
            response += f"• {pos['symbol']}: {pos['shares']} shares @ ${pos['current_price']:.2f} "
            response += f"{pnl_emoji} ${pos['unrealized_pnl']:.2f} ({pos['pnl_percent']:.1f}%)\n"
        response += "\n"
    
    if recent_alerts:
        response += "**Recent Alerts:**\n"
        for alert in recent_alerts[-3:]:
            try:
                # Handle different alert formats
                if isinstance(alert, dict):
                    if 'timestamp' in alert and hasattr(alert['timestamp'], 'strftime'):
                        time_str = alert['timestamp'].strftime('%H:%M:%S')
                        message = alert.get('message', str(alert))
                    else:
                        time_str = datetime.now().strftime('%H:%M:%S')
                        message = alert.get('message', str(alert))
                else:
                    # Handle string alerts
                    time_str = datetime.now().strftime('%H:%M:%S')
                    message = str(alert)

                response += f"• {time_str}: {message}\n"
            except Exception as e:
                # Fallback for any alert format issues
                response += f"• {datetime.now().strftime('%H:%M:%S')}: {str(alert)}\n"
    
    return response


if __name__ == "__main__":
    # Test the monitoring system
    monitor = RealTimeMonitor()
    
    print("🧪 Testing Real-Time Monitor")
    print("=" * 40)
    
    # Start monitoring
    print(monitor.start_monitoring())
    
    # Add a test position
    pos_id = monitor.add_position("NVDA", 100.0, 10, 97.0, 105.0)
    print(f"Added position: {pos_id}")
    
    # Get status
    time.sleep(2)
    print("\nStatus:")
    print(get_monitoring_status())
    
    # Stop monitoring
    print(monitor.stop_monitoring())
