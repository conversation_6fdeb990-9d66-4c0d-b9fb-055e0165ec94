"""Market Reasoning Engine – derives actionable insights from fused data.

At this stage, it returns a canned response and confidence level based on
presence of key fields in the data.
"""
from __future__ import annotations

from typing import Any, Dict, Tuple

from logger_util import info


def generate_market_reasoning(fused_data: Dict[str, Any]) -> dict:
    """Produce a human-readable recommendation and confidence score.

    Parameters
    ----------
    fused_data : dict
        Result from `data_fusion.fuse_market_data`.

    Returns
    -------
    dict
        Structured recommendation::
            {
              "action": "long" | "short" | "standby",
              "confidence": float (0-1),
              "rationale": str
            }
    """
    if not fused_data:
        return {
            "action": "standby",
            "confidence": 0.0,
            "rationale": "No data available – unable to form opinion.",
        }

    change_pct_raw = fused_data.get("changesPercentage") or fused_data.get("changePercent")
    try:
        change_pct = float(str(change_pct_raw).strip("()%"))
    except Exception:  # noqa: BLE001
        change_pct = None

    if change_pct is None:
        return {
            "action": "standby",
            "confidence": 0.2,
            "rationale": "Insufficient data for price change percentage.",
        }

    if change_pct > 1:
        action, conf, rationale = (
            "long",
            min(1.0, 0.5 + change_pct / 100),
            f"Price up {change_pct:.2f}% today – bullish momentum.",
        )
    elif change_pct < -1:
        action, conf, rationale = (
            "short",
            min(1.0, 0.5 + abs(change_pct) / 100),
            f"Price down {change_pct:.2f}% today – bearish momentum.",
        )
    else:
        action, conf, rationale = (
            "standby",
            0.4,
            f"Price move only {change_pct:.2f}% – no strong bias.",
        )

    info("🧠 Market reasoning", change_pct=change_pct, action=action, confidence=conf)
    return {"action": action, "confidence": round(conf, 2), "rationale": rationale}

def scan_options_strategy_opportunities(symbols: list[str], min_grade: str = "C", include_momentum: bool = True, max_results: int = 10) -> list[dict]:
    """Placeholder options strategy analyzer scan.

    Parameters
    ----------
    symbols : list[str]
        Symbols to scan.
    min_grade : str
        Minimum rating A–F.
    include_momentum : bool
        Whether to include momentum filter.
    max_results : int
        Maximum number of results to return.
    """
    info("📈 Running options strategy analyzer", symbols=symbols, min_grade=min_grade)
    # TODO: implement real logic – for now return dummy signal
    results = []
    for sym in symbols[:max_results]:
        results.append({
            "symbol": sym,
            "grade": "B",
            "strategy": "bull call spread",
            "expected_return_pct": 15.0,
        })
    return results 