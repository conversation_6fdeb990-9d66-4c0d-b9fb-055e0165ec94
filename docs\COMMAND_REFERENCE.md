# 📋 Complete Command Reference for TotalRecall Enhanced

## 🎯 **OVERVIEW**

TotalRecall Enhanced features **90+ tools** accessible through natural language commands. This reference covers all available commands with examples and expected outputs.

## 💼 **ACCOUNT & PORTFOLIO MANAGEMENT**

### **Account Information**
| Command | Natural Language | Expected Output |
|---------|------------------|-----------------|
| `get_account_balance` | "What's my account balance?" | Account balance, buying power, equity |
| `get_current_positions` | "Show my positions" | All open positions with P&L |
| `get_specific_position` | "Get details for my AAPL position" | Specific position analysis |

**Example:**
```
User: "What's my account balance?"
AI: 💼 Account Balance: $25,450.32
    📈 Buying Power: $12,225.16
    📊 Total Equity: $25,450.32
    🎯 Day Trading Buying Power: $50,900.64
```

### **Position Management**
| Command | Natural Language | Expected Output |
|---------|------------------|-----------------|
| `close_position` | "Close my AAPL position" | Position closure confirmation |
| `close_all_positions` | "Close all my positions" | All positions closed |
| `update_stop_loss` | "Update stop loss for AAPL to $145" | Stop loss updated |

## 📈 **MARKET DATA & ANALYSIS**

### **Real-Time Quotes**
| Command | Natural Language | Expected Output |
|---------|------------------|-----------------|
| `get_enhanced_quote` | "Get quote for AAPL" | Real-time price, volume, change |
| `get_market_quote` | "What's TSLA trading at?" | Current market price |
| `get_latest_trade` | "Latest trade for NVDA" | Most recent trade details |

**Example:**
```
User: "Get quote for AAPL"
AI: 📊 AAPL Quote (Real-time)
    💰 Price: $150.25 (+$2.15, +1.45%)
    📊 Volume: 45.2M (vs 52.1M avg)
    📈 Day Range: $148.10 - $151.50
    🎯 52-Week: $124.17 - $199.62
```

### **Historical Analysis**
| Command | Natural Language | Expected Output |
|---------|------------------|-----------------|
| `get_stock_history` | "Show me 30 days of AAPL history" | Historical price data |
| `get_stock_trades` | "Recent trades for TSLA" | Trade history analysis |
| `analyze_stock_comprehensive` | "Analyze AAPL comprehensively" | Deep multi-source analysis |

## 🎯 **TTM SQUEEZE ANALYSIS**

### **TTM Scanning**
| Command | Natural Language | Expected Output |
|---------|------------------|-----------------|
| `scan_ttm_squeeze_opportunities` | "Scan for TTM opportunities" | TTM setups with grades |
| `get_ttm_squeeze_analysis` | "TTM analysis for AAPL" | Detailed TTM breakdown |
| `analyze_ttm_setup` | "Analyze TSLA TTM setup" | Setup quality analysis |
| `unified_ttm_scan` | "Run unified TTM scan" | Multi-scanner results |

**Example:**
```
User: "Scan for TTM opportunities"
AI: 🔍 TTM Squeeze Scan Results
    
    1. PLTR - Grade A+ (88.5% confidence)
       Entry: $18.45, Target: $19.85, Stop: $17.90
    
    2. AAPL - Grade A- (85.2% confidence)
       Entry: $150.50, Target: $158.00, Stop: $145.00
    
    📊 Scanned: 503 symbols | Found: 12 opportunities
```

### **TTM Strategy Tools**
| Command | Natural Language | Expected Output |
|---------|------------------|-----------------|
| `best_ttm_trade_now` | "What's the best TTM trade now?" | Top TTM opportunity |
| `fastest_ttm_profits` | "Fastest TTM profits available" | Quick profit setups |
| `ttm_grade_analysis` | "TTM grade analysis" | Grade performance stats |

## 🦅 **PROFESSIONAL OPTIONS STRATEGIES**

### **Iron Condors**
| Command | Natural Language | Expected Output |
|---------|------------------|-----------------|
| `create_iron_condor` | "Create Iron Condor for AAPL" | 4-leg strategy analysis |
| `create_iron_condor` | "Iron Condor for TSLA expiring 2024-07-19" | Specific expiration setup |

**Example:**
```
User: "Create Iron Condor for AAPL"
AI: 🦅 Iron Condor Strategy for AAPL
    
    📊 Strategy Setup:
    • Short Call: $155 (Sell)
    • Long Call: $160 (Buy)
    • Short Put: $145 (Sell)
    • Long Put: $140 (Buy)
    
    💰 Max Profit: $325 (65% probability)
    ⚠️ Max Loss: $175
    🎯 Breakevens: $142.25 / $157.75
    
    📈 Greeks: Delta-neutral, Theta-positive
```

### **Butterfly Spreads**
| Command | Natural Language | Expected Output |
|---------|------------------|-----------------|
| `create_butterfly_spread` | "Create Butterfly Spread for TSLA" | 3-leg strategy setup |
| `volatility_strategy_selector` | "Best options strategy for NVDA based on volatility" | AI strategy selection |

### **Options Analysis**
| Command | Natural Language | Expected Output |
|---------|------------------|-----------------|
| `get_options_greeks` | "Get Greeks for AAPL options" | Delta, Gamma, Theta, Vega |
| `options_pricing` | "Price AAPL $150 call" | Black-Scholes pricing |
| `options_strategy` | "Options strategy for bullish TSLA" | Strategy recommendation |

## 🤖 **ALGORITHMIC TRADING**

### **Momentum Algorithms**
| Command | Natural Language | Expected Output |
|---------|------------------|-----------------|
| `run_momentum_algorithm` | "Run momentum algorithm on AAPL,TSLA" | Multi-timeframe analysis |
| `run_momentum_algorithm` | "Momentum scan on tech stocks" | Sector momentum analysis |

**Example:**
```
User: "Run momentum algorithm on AAPL,TSLA"
AI: 🚀 Momentum Algorithm Results
    
    AAPL: 🟢 STRONG BUY
    • 5min: Bullish momentum (+85%)
    • 1hour: Trend confirmation (+78%)
    • Daily: Uptrend intact (+72%)
    • Position Size: 150 shares (Kelly: 2.1%)
    
    TSLA: 🟡 NEUTRAL
    • Mixed signals across timeframes
    • Wait for clearer momentum
```

### **Mean Reversion**
| Command | Natural Language | Expected Output |
|---------|------------------|-----------------|
| `run_mean_reversion_algorithm` | "Run mean reversion on QQQ" | Statistical analysis |
| `run_pairs_trading` | "Pairs trading on AAPL,MSFT" | Pairs analysis |

### **Market Regime Detection**
| Command | Natural Language | Expected Output |
|---------|------------------|-----------------|
| `detect_market_regime` | "Detect current market regime" | Market classification |

## 💰 **PROFIT PLANNING**

### **Profit Targeting**
| Command | Natural Language | Expected Output |
|---------|------------------|-----------------|
| `make_profit_plan` | "Make me $50 today" | TTM profit plan |
| `make_profit_ttm` | "TTM profit plan for $100" | TTM-specific planning |
| `enhanced_profit_planning` | "Enhanced profit plan $200" | Advanced planning |

**Example:**
```
User: "Make me $50 today"
AI: 💰 TTM Profit Plan for $50
    
    🎯 Recommended Setup: PLTR Grade A+
    • Entry: $18.45 (150 shares)
    • Target: $18.78 (+$49.50 profit)
    • Stop: $18.12 (-$49.50 risk)
    • Probability: 78% success rate
    
    📊 Risk/Reward: 1:1 (Conservative)
    ⏰ Expected Hold: 2-4 hours
```

## 📊 **PERFORMANCE ANALYTICS**

### **Performance Tracking**
| Command | Natural Language | Expected Output |
|---------|------------------|-----------------|
| `performance_summary` | "Show my performance" | P&L and metrics |
| `show_performance_dashboard` | "Performance dashboard" | Visual analytics |
| `performance_heatmap` | "Performance heatmap" | Time-based analysis |

### **Strategy Analysis**
| Command | Natural Language | Expected Output |
|---------|------------------|-----------------|
| `strategy_comparison` | "Compare my strategies" | Strategy performance |
| `risk_analysis` | "Risk analysis" | Risk metrics |
| `confidence_analysis` | "Confidence analysis for AAPL" | Confidence breakdown |

## 🔧 **ORDER MANAGEMENT**

### **Order Placement**
| Command | Natural Language | Expected Output |
|---------|------------------|-----------------|
| `place_enhanced_order` | "Buy 100 AAPL at market" | Order confirmation |
| `place_option_order` | "Buy AAPL $150 calls" | Options order |
| `cancel_all_orders` | "Cancel all my orders" | Cancellation confirmation |

### **Order Status**
| Command | Natural Language | Expected Output |
|---------|------------------|-----------------|
| `get_all_orders` | "Show my orders" | All order status |
| `cancel_specific_order` | "Cancel my AAPL order" | Specific cancellation |

## 🔍 **MARKET INTELLIGENCE**

### **Market Data**
| Command | Natural Language | Expected Output |
|---------|------------------|-----------------|
| `get_market_status` | "Is the market open?" | Market hours status |
| `get_market_calendar` | "Market calendar" | Trading schedule |
| `get_earnings_calendar` | "Earnings calendar" | Upcoming earnings |

### **Asset Information**
| Command | Natural Language | Expected Output |
|---------|------------------|-----------------|
| `search_assets` | "Search for tech stocks" | Asset search results |
| `get_asset_info` | "Info about AAPL" | Company information |

## 🎮 **SYSTEM COMMANDS**

### **System Status**
| Command | Natural Language | Expected Output |
|---------|------------------|-----------------|
| `system_status` | "System status" | Current system state |
| `automation_status` | "Automation status" | Automation state |
| `show_help` | "Help" | Available commands |

### **Learning & Insights**
| Command | Natural Language | Expected Output |
|---------|------------------|-----------------|
| `learning_insights` | "Learning insights" | AI learning status |
| `judge_investment` | "Judge buying AAPL" | Investment verdict |
| `explain_symbol` | "Explain TSLA analysis" | Symbol explanation |

## 🚀 **QUICK REFERENCE**

### **Most Used Commands**
1. `"What's my account balance?"` - Account info
2. `"Scan for TTM opportunities"` - Find setups
3. `"Make me $50 today"` - Profit planning
4. `"Get quote for AAPL"` - Real-time prices
5. `"Create Iron Condor for AAPL"` - Options strategies
6. `"Run momentum algorithm on AAPL"` - Algo trading
7. `"Show my positions"` - Portfolio status
8. `"System status"` - System health

### **Command Patterns**
- **Questions:** "What's...", "How's...", "Is..."
- **Actions:** "Create...", "Run...", "Buy...", "Sell..."
- **Analysis:** "Analyze...", "Scan...", "Judge..."
- **Information:** "Show...", "Get...", "Explain..."

**🎯 All commands work with natural language - just ask what you want to know or do!**
