#!/usr/bin/env python3
"""
AI TTM Real-Time Screener - Phase 3
Uses trained ML model to score live TTM setups with AI predictions
"""

import pandas as pd
import numpy as np
import talib
import json
import requests
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Try to import joblib with fallback
try:
    import joblib
    JOBLIB_AVAILABLE = True
except ImportError:
    JOBLIB_AVAILABLE = False

# Use existing FMP infrastructure
try:
    from core.config import get_api_key
except ImportError:
    try:
        from config import get_api_key
    except ImportError:
        def get_api_key(key_name):
            import os
            key = os.getenv(key_name)
            if key is None:
                raise KeyError(f"Environment variable '{key_name}' is not set.")
            return key

class AITTMScreener:
    """Real-time TTM screener powered by AI predictions."""
    
    def __init__(self, model_path="ttm_ai_model.pkl", scaler_path="ttm_scaler.pkl"):
        self.model = None
        self.scaler = None
        self.load_model(model_path, scaler_path)
        
    def load_model(self, model_path, scaler_path):
        """Load trained model and scaler."""
        if not JOBLIB_AVAILABLE:
            print("❌ joblib not available! Install with: pip install joblib")
            self.model = None
            self.scaler = None
            return

        try:
            self.model = joblib.load(model_path)
            self.scaler = joblib.load(scaler_path)
            print("✅ AI Model loaded successfully")
        except FileNotFoundError:
            print("❌ Model files not found! Run training first.")
            self.model = None
            self.scaler = None
        except Exception as e:
            print(f"❌ Error loading model: {e}")
            self.model = None
            self.scaler = None
    
    def get_live_data(self, symbol, period="5d", interval="1h"):
        """Get recent data using FMP API."""
        try:
            api_key = get_api_key("FMP_API_KEY")
            if not api_key:
                return None

            # Convert interval to FMP format
            if interval == "1h":
                fmp_interval = "1hour"
            elif interval == "15m":
                fmp_interval = "15min"
            elif interval == "5m":
                fmp_interval = "5min"
            else:
                fmp_interval = "1hour"

            # FMP historical data endpoint
            url = f"https://financialmodelingprep.com/api/v3/historical-chart/{fmp_interval}/{symbol}"
            params = {"apikey": api_key}

            response = requests.get(url, params=params)
            if response.status_code != 200:
                return None

            data = response.json()
            if not data or len(data) < 50:
                return None

            # Convert to DataFrame
            df = pd.DataFrame(data)
            df['date'] = pd.to_datetime(df['date'])
            df.set_index('date', inplace=True)
            df.sort_index(inplace=True)

            # Rename columns to match expected format
            df.rename(columns={
                'open': 'Open',
                'high': 'High',
                'low': 'Low',
                'close': 'Close',
                'volume': 'Volume'
            }, inplace=True)

            # Get recent data only
            cutoff_date = datetime.now() - timedelta(days=7)
            df = df[df.index >= cutoff_date]

            if len(df) < 50:
                return None

            return df

        except Exception:
            return None
    
    def calculate_features(self, df):
        """Calculate the same features used in training."""
        # Bollinger Bands
        df['bb_upper'], df['bb_middle'], df['bb_lower'] = talib.BBANDS(
            df['Close'], timeperiod=20, nbdevup=2, nbdevdn=2
        )
        
        # Keltner Channels
        df['ema20'] = talib.EMA(df['Close'], timeperiod=20)
        df['atr'] = talib.ATR(df['High'], df['Low'], df['Close'], timeperiod=20)
        df['kc_upper'] = df['ema20'] + (df['atr'] * 1.5)
        df['kc_lower'] = df['ema20'] - (df['atr'] * 1.5)
        
        # TTM Squeeze Detection
        df['ttm_squeeze_active'] = (
            (df['bb_upper'] <= df['kc_upper']) & 
            (df['bb_lower'] >= df['kc_lower'])
        ).astype(int)
        
        # MACD and Histogram
        df['macd'], df['macd_signal'], df['macd_histogram'] = talib.MACD(
            df['Close'], fastperiod=12, slowperiod=26, signalperiod=9
        )
        
        # Custom Momentum Histogram
        df['momentum_histogram'] = df['Close'] - df['ema20']
        
        # EMAs
        df['ema5'] = talib.EMA(df['Close'], timeperiod=5)
        df['ema8'] = talib.EMA(df['Close'], timeperiod=8)
        df['ema21'] = talib.EMA(df['Close'], timeperiod=21)
        
        # EMA Crossover
        df['ema5_above_ema8'] = (df['ema5'] > df['ema8']).astype(int)
        df['ema8_above_ema21'] = (df['ema8'] > df['ema21']).astype(int)
        
        # Price above EMAs
        df['price_above_ema8'] = (df['Close'] > df['ema8']).astype(int)
        df['price_above_ema21'] = (df['Close'] > df['ema21']).astype(int)
        
        # Volume features
        df['volume_sma20'] = df['Volume'].rolling(20).mean()
        df['volume_ratio'] = df['Volume'] / df['volume_sma20']
        df['high_volume'] = (df['volume_ratio'] > 1.5).astype(int)
        
        # Candle patterns
        df['green_candle'] = (df['Close'] > df['Open']).astype(int)
        df['last_3_candles_green'] = (
            df['green_candle'].rolling(3).sum() == 3
        ).astype(int)
        
        # Volatility features
        df['true_range'] = talib.TRANGE(df['High'], df['Low'], df['Close'])
        df['volatility_ratio'] = df['true_range'] / df['Close']
        
        # Momentum features
        df['rsi'] = talib.RSI(df['Close'], timeperiod=14)
        df['momentum_rising'] = (
            df['momentum_histogram'] > df['momentum_histogram'].shift(1)
        ).astype(int)
        
        # Histogram pattern
        df['histogram_rising_3'] = (
            (df['momentum_histogram'] > df['momentum_histogram'].shift(1)) &
            (df['momentum_histogram'].shift(1) > df['momentum_histogram'].shift(2)) &
            (df['momentum_histogram'].shift(2) > df['momentum_histogram'].shift(3))
        ).astype(int)
        
        return df
    
    def predict_setup_success(self, df, symbol):
        """Use AI model to predict setup success probability."""
        if self.model is None:
            return None
        
        # Get latest row features
        latest = df.iloc[-1]
        
        # Feature columns (same as training)
        feature_cols = [
            'ttm_squeeze_active', 'momentum_histogram', 'ema5_above_ema8',
            'ema8_above_ema21', 'price_above_ema8', 'price_above_ema21',
            'last_3_candles_green', 'high_volume', 'volume_ratio',
            'volatility_ratio', 'rsi', 'momentum_rising', 'histogram_rising_3',
            'macd', 'macd_histogram', 'atr'
        ]
        
        # Extract features
        features = latest[feature_cols].values.reshape(1, -1)
        
        # Handle NaN values
        if np.isnan(features).any():
            return None
        
        # Scale features
        features_scaled = self.scaler.transform(features)
        
        # Predict probability
        probability = self.model.predict_proba(features_scaled)[0, 1]
        
        # Create setup description
        setup_parts = []
        if latest['ttm_squeeze_active']:
            setup_parts.append("TTM Squeeze")
        if latest['histogram_rising_3']:
            setup_parts.append("Histogram Rising")
        if latest['ema5_above_ema8'] and latest['ema8_above_ema21']:
            setup_parts.append("EMA Stack")
        if latest['last_3_candles_green']:
            setup_parts.append("3 Green Candles")
        if latest['high_volume']:
            setup_parts.append("High Volume")
        
        setup_description = " + ".join(setup_parts) if setup_parts else "Basic Setup"
        
        # Calculate expected move (rough estimate based on ATR)
        expected_move = latest['atr'] / latest['Close'] * 100
        
        # Determine confidence level
        if probability >= 0.8:
            confidence = "High"
        elif probability >= 0.6:
            confidence = "Moderate"
        else:
            confidence = "Low"
        
        return {
            "symbol": symbol,
            "setup": setup_description,
            "score": round(probability, 3),
            "expected_move": f"{expected_move:.1f}%",
            "confidence": confidence,
            "price": round(latest['Close'], 2),
            "volume_ratio": round(latest['volume_ratio'], 2),
            "squeeze_active": bool(latest['ttm_squeeze_active']),
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
    
    def scan_symbols(self, symbols, min_score=0.6, enable_quality_filters=True):
        """Scan multiple symbols and return AI-ranked opportunities."""
        print(f"🤖 AI TTM Screener scanning {len(symbols)} symbols...")

        opportunities = []
        filtered_count = 0

        for i, symbol in enumerate(symbols):
            print(f"📊 Scanning {symbol} ({i+1}/{len(symbols)})...")

            # Get data
            df = self.get_live_data(symbol)
            if df is None:
                continue

            # Calculate features
            df = self.calculate_features(df)

            # Get AI prediction
            prediction = self.predict_setup_success(df, symbol)
            if prediction is None:
                continue

            # Apply Phase 4 Quality Filters
            if enable_quality_filters:
                latest = df.iloc[-1]

                # Price range filter ($15-$300)
                if latest['Close'] < 15 or latest['Close'] > 300:
                    filtered_count += 1
                    continue

                # Volume requirement (120%+ average)
                if latest['volume_ratio'] < 1.2:
                    filtered_count += 1
                    continue

                # Momentum threshold (>0.5)
                momentum_strength = abs(latest['momentum_histogram'] / latest['Close'])
                if momentum_strength < 0.005:  # 0.5% momentum threshold
                    filtered_count += 1
                    continue

                # Volatility requirement (3%+ daily range)
                daily_range = (latest['High'] - latest['Low']) / latest['Close']
                if daily_range < 0.03:  # 3% minimum range
                    filtered_count += 1
                    continue

            # Filter by minimum AI score
            if prediction['score'] >= min_score:
                opportunities.append(prediction)

        # Sort by score (highest first)
        opportunities.sort(key=lambda x: x['score'], reverse=True)

        if enable_quality_filters and filtered_count > 0:
            print(f"🔍 Quality filters removed {filtered_count} low-quality setups")

        return opportunities
    
    def format_results(self, opportunities):
        """Format results for display."""
        if not opportunities:
            return "🤖 No AI-qualified TTM setups found meeting Phase 4 quality filters and minimum score threshold."

        result = f"🤖 AI TTM SCREENER RESULTS ({len(opportunities)} opportunities):\n"
        result += f"🔍 Phase 4 Quality Filters: Price $15-$300 | Volume 120%+ | Momentum 0.5%+ | Range 3%+\n\n"

        for i, opp in enumerate(opportunities, 1):
            confidence_emoji = "🔥" if opp['confidence'] == "High" else "⭐" if opp['confidence'] == "Moderate" else "📊"
            squeeze_emoji = "🎁" if opp['squeeze_active'] else ""

            result += f"{confidence_emoji} #{i} {opp['symbol']} - Score: {opp['score']:.1%} ({opp['confidence']})\n"
            result += f"   Setup: {opp['setup']} {squeeze_emoji}\n"
            result += f"   Price: ${opp['price']} | Expected Move: {opp['expected_move']} | Volume: {opp['volume_ratio']:.1f}x\n"
            result += f"   Time: {opp['timestamp']}\n\n"

        return result


def main():
    """Main screening execution."""
    print("🤖 AI TTM REAL-TIME SCREENER - PHASE 3")
    print("=" * 60)

    # Use top 100 most liquid S&P 500 stocks for real-time scanning
    # (Full S&P 500 would be too slow for real-time)
    symbols = [
        # Mega Cap Tech
        'AAPL', 'MSFT', 'GOOGL', 'GOOG', 'AMZN', 'NVDA', 'TSLA', 'META', 'ORCL', 'ADBE',
        'CRM', 'NFLX', 'INTC', 'AMD', 'CSCO', 'AVGO', 'TXN', 'QCOM', 'AMAT', 'ADI',

        # Healthcare & Pharma
        'UNH', 'JNJ', 'PFE', 'ABBV', 'LLY', 'TMO', 'ABT', 'MRK', 'DHR', 'BMY',
        'AMGN', 'GILD', 'VRTX', 'REGN', 'ISRG', 'ZTS', 'CVS', 'CI', 'HUM', 'ANTM',

        # Financial
        'JPM', 'BAC', 'WFC', 'GS', 'MS', 'C', 'AXP', 'BLK', 'SCHW', 'USB',

        # Consumer & Retail
        'WMT', 'HD', 'NKE', 'MCD', 'SBUX', 'TJX', 'LOW', 'BKNG', 'DIS', 'GM',

        # Industrial & Energy
        'BA', 'CAT', 'HON', 'UPS', 'RTX', 'LMT', 'GE', 'MMM', 'FDX', 'XOM',
        'CVX', 'COP', 'EOG', 'SLB', 'PSX', 'VLO', 'MPC', 'OXY', 'BKR', 'HAL',

        # Consumer Staples
        'PG', 'KO', 'PEP', 'COST', 'WBA', 'CL', 'KMB', 'GIS', 'K', 'HSY',

        # Utilities & REITs
        'NEE', 'DUK', 'SO', 'D', 'AEP', 'EXC', 'XEL', 'SRE', 'PEG', 'AMT'
    ]
    
    # Initialize screener
    screener = AITTMScreener()
    
    if screener.model is None:
        print("❌ No trained model found!")
        print("🔧 Run ai_ttm_model_trainer.py first")
        return
    
    # Scan for opportunities with Phase 4 Quality Filters
    opportunities = screener.scan_symbols(symbols, min_score=0.6, enable_quality_filters=True)
    
    # Display results
    results = screener.format_results(opportunities)
    print(results)
    
    # Save results to JSON
    if opportunities:
        with open(f"ai_ttm_results_{datetime.now().strftime('%Y%m%d_%H%M')}.json", 'w') as f:
            json.dump(opportunities, f, indent=2)
        print(f"💾 Results saved to JSON file")
    
    print(f"\n🎯 Scan complete! Found {len(opportunities)} AI-qualified setups")


if __name__ == "__main__":
    main()
