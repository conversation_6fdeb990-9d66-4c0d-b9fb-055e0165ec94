#!/usr/bin/env python3
"""
Simple TTM Squeeze Scanner - No TA-Lib dependency version.

This scanner identifies the TTM Squeeze pattern using basic calculations
without requiring the TA-Lib library installation.
"""

import requests
import pandas as pd
import numpy as np
from typing import List, Dict, Optional, Tuple, Any
from datetime import datetime, timedelta
import time
import math
import asyncio
import aiohttp
from dataclasses import dataclass

from config import get_api_key
from logger_util import info, warning, error

FMP_BASE = "https://financialmodelingprep.com/api/v3"


@dataclass
class SimpleTTMSetup:
    """Simple TTM Squeeze setup."""
    symbol: str
    timeframe: str
    timestamp: datetime
    close_price: float
    squeeze_status: str
    momentum_direction: str
    grade: str
    confidence: float
    entry_price: float
    stop_loss: float
    target_price: float
    pattern_details: Dict[str, Any]


class SimpleTTMSqueezeScanner:
    """
    Simple TTM Squeeze Scanner using basic calculations.
    
    Identifies squeeze patterns without requiring TA-Lib:
    - Bollinger Bands vs Keltner Channels
    - Momentum direction
    - EMA trends
    """
    
    def __init__(self):
        """Initialize the simple scanner."""
        self.api_key = get_api_key('FMP_API_KEY')
        if not self.api_key:
            raise ValueError("FMP_API_KEY not found in environment")
        
        # Large cap symbols
        self.large_cap_symbols = [
            'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'META', 'TSLA',
            'BRK.B', 'UNH', 'JNJ', 'JPM', 'V', 'PG', 'MA', 'HD', 'CVX',
            'ABBV', 'PFE', 'KO', 'AVGO', 'PEP', 'TMO', 'COST', 'WMT', 'DIS',
            'ABT', 'VZ', 'ADBE', 'NFLX', 'CRM', 'XOM', 'NKE', 'DHR', 'LIN',
            'ORCL', 'ACN', 'TXN', 'QCOM', 'NEE', 'PM', 'RTX', 'HON', 'UNP',
            'LOW', 'SPGI', 'INTU', 'IBM', 'GS', 'CAT', 'AMGN', 'ISRG', 'BKNG',
            'PLTR', 'SNOW', 'CRWD', 'ZM', 'DOCU', 'OKTA', 'DDOG', 'NET'
        ]
        
        info("🔍 Simple TTM Squeeze Scanner initialized")
    
    def get_historical_data(self, symbol: str, timeframe: str = '5min', limit: int = 100) -> pd.DataFrame:
        """Get historical data from FMP."""
        try:
            if timeframe == '1day':
                url = f"{FMP_BASE}/historical-price-full/{symbol}"
                params = {'limit': limit, 'apikey': self.api_key}
            else:
                url = f"{FMP_BASE}/historical-chart/{timeframe}/{symbol}"
                params = {'limit': limit, 'apikey': self.api_key}
            
            response = requests.get(url, params=params, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                
                if timeframe == '1day':
                    historical = data.get('historical', [])
                else:
                    historical = data
                
                if not historical:
                    return pd.DataFrame()
                
                df = pd.DataFrame(historical)
                df['datetime'] = pd.to_datetime(df['date'])
                df = df.sort_values('datetime').reset_index(drop=True)
                
                # Ensure numeric columns
                for col in ['open', 'high', 'low', 'close', 'volume']:
                    if col in df.columns:
                        df[col] = pd.to_numeric(df[col], errors='coerce')
                
                return df[['datetime', 'open', 'high', 'low', 'close', 'volume']].dropna()
            else:
                warning(f"Failed to fetch data for {symbol}: {response.status_code}")
                return pd.DataFrame()
                
        except Exception as e:
            error(f"Error fetching data for {symbol}: {e}")
            return pd.DataFrame()
    
    def calculate_sma(self, data: pd.Series, period: int) -> pd.Series:
        """Calculate Simple Moving Average."""
        return data.rolling(window=period).mean()
    
    def calculate_ema(self, data: pd.Series, period: int) -> pd.Series:
        """Calculate Exponential Moving Average."""
        return data.ewm(span=period).mean()
    
    def calculate_bollinger_bands(self, data: pd.Series, period: int = 20, std_dev: float = 2.0) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """Calculate Bollinger Bands."""
        sma = self.calculate_sma(data, period)
        std = data.rolling(window=period).std()
        
        upper = sma + (std_dev * std)
        lower = sma - (std_dev * std)
        
        return upper, sma, lower
    
    def calculate_true_range(self, high: pd.Series, low: pd.Series, close: pd.Series) -> pd.Series:
        """Calculate True Range."""
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        
        return pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    
    def calculate_atr(self, high: pd.Series, low: pd.Series, close: pd.Series, period: int = 20) -> pd.Series:
        """Calculate Average True Range."""
        tr = self.calculate_true_range(high, low, close)
        return tr.rolling(window=period).mean()
    
    def calculate_keltner_channels(self, high: pd.Series, low: pd.Series, close: pd.Series, 
                                 period: int = 20, multiplier: float = 1.5) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """Calculate Keltner Channels."""
        ema = self.calculate_ema(close, period)
        atr = self.calculate_atr(high, low, close, period)
        
        upper = ema + (multiplier * atr)
        lower = ema - (multiplier * atr)
        
        return upper, ema, lower
    
    def calculate_momentum(self, data: pd.Series, period: int = 12) -> pd.Series:
        """Calculate momentum."""
        return data - data.shift(period)
    
    def compute_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Compute all indicators."""
        if len(df) < 50:
            return df
        
        try:
            # Bollinger Bands
            bb_upper, bb_middle, bb_lower = self.calculate_bollinger_bands(df['close'])
            
            # Keltner Channels
            kc_upper, kc_middle, kc_lower = self.calculate_keltner_channels(
                df['high'], df['low'], df['close']
            )
            
            # EMAs
            df['ema5'] = self.calculate_ema(df['close'], 5)
            df['ema8'] = self.calculate_ema(df['close'], 8)
            df['ema21'] = self.calculate_ema(df['close'], 21)
            
            # Momentum
            df['momentum'] = self.calculate_momentum(df['close'], 12)
            
            # Bollinger and Keltner
            df['bb_upper'] = bb_upper
            df['bb_lower'] = bb_lower
            df['kc_upper'] = kc_upper
            df['kc_lower'] = kc_lower
            
            # Squeeze detection
            df['squeeze_on'] = (bb_lower > kc_lower) & (bb_upper < kc_upper)
            
            # Squeeze line (volatility ratio)
            bb_width = bb_upper - bb_lower
            kc_width = kc_upper - kc_lower
            df['squeeze_line'] = np.where(kc_width > 0, 100 * (1 - bb_width / kc_width), 50)
            
            # Histogram (simplified)
            df['histogram'] = np.where(df['squeeze_on'], 0, df['momentum'] / 10)
            
            return df
            
        except Exception as e:
            error(f"Error computing indicators: {e}")
            return df
    
    def identify_setups(self, df: pd.DataFrame, symbol: str, timeframe: str) -> List[SimpleTTMSetup]:
        """Identify TTM Squeeze setups."""
        setups = []
        
        if len(df) < 20:
            return setups
        
        try:
            for i in range(10, len(df)):
                # Check for squeeze release
                squeeze_released = False
                if i > 0:
                    prev_squeeze = df['squeeze_on'].iloc[i-1]
                    curr_squeeze = df['squeeze_on'].iloc[i]
                    if prev_squeeze and not curr_squeeze:
                        squeeze_released = True
                
                # Check momentum direction
                momentum_val = df['momentum'].iloc[i]
                if momentum_val > 0:
                    momentum_dir = "bullish"
                elif momentum_val < 0:
                    momentum_dir = "bearish"
                else:
                    momentum_dir = "neutral"
                
                # Check EMA trend
                ema8_rising = False
                if i >= 4:
                    ema8_curr = df['ema8'].iloc[i]
                    ema8_prev = df['ema8'].iloc[i-4]
                    ema8_rising = ema8_curr > ema8_prev
                
                # Price above 5-EMA
                price_above_5ema = df['close'].iloc[i] > df['ema5'].iloc[i]
                
                # SqueezeLine threshold
                squeeze_line_high = df['squeeze_line'].iloc[i] >= 70
                
                # Determine squeeze status
                if df['squeeze_on'].iloc[i]:
                    squeeze_status = "squeeze_on"
                elif squeeze_released:
                    squeeze_status = "squeeze_released"
                else:
                    squeeze_status = "no_squeeze"
                
                # Grade the setup
                score = 0
                if squeeze_released:
                    score += 30
                elif squeeze_status == "squeeze_on":
                    score += 20
                
                if momentum_dir != "neutral":
                    score += 20
                
                if ema8_rising:
                    score += 15
                
                if price_above_5ema:
                    score += 10
                
                if squeeze_line_high:
                    score += 5
                
                # Convert to grade
                if score >= 80:
                    grade = "A"
                elif score >= 70:
                    grade = "B"
                elif score >= 60:
                    grade = "C"
                elif score >= 50:
                    grade = "D"
                else:
                    grade = "F"
                
                confidence = min(score / 100, 1.0)
                
                # Only include good setups
                if score >= 60:  # C grade or better
                    close_price = df['close'].iloc[i]
                    entry_price = close_price * 1.002
                    stop_loss = df['ema5'].iloc[i] * 0.98
                    target_price = close_price * 1.06
                    
                    pattern_details = {
                        'squeeze_released': squeeze_released,
                        'ema8_rising': ema8_rising,
                        'price_above_5ema': price_above_5ema,
                        'squeeze_line_high': squeeze_line_high,
                        'squeeze_line_value': df['squeeze_line'].iloc[i],
                        'momentum_value': momentum_val
                    }
                    
                    setup = SimpleTTMSetup(
                        symbol=symbol,
                        timeframe=timeframe,
                        timestamp=df['datetime'].iloc[i],
                        close_price=close_price,
                        squeeze_status=squeeze_status,
                        momentum_direction=momentum_dir,
                        grade=grade,
                        confidence=confidence,
                        entry_price=entry_price,
                        stop_loss=stop_loss,
                        target_price=target_price,
                        pattern_details=pattern_details
                    )
                    
                    setups.append(setup)
        
        except Exception as e:
            error(f"Error identifying setups for {symbol}: {e}")
        
        return setups
    
    def scan_symbol(self, symbol: str, timeframe: str = '5min') -> List[SimpleTTMSetup]:
        """Scan a single symbol for setups."""
        try:
            # Get data
            df = self.get_historical_data(symbol, timeframe, 100)
            
            if df.empty or len(df) < 50:
                return []
            
            # Compute indicators
            df = self.compute_indicators(df)
            
            # Identify setups
            setups = self.identify_setups(df, symbol, timeframe)
            
            return setups
            
        except Exception as e:
            error(f"Error scanning {symbol}: {e}")
            return []
    
    def scan_multiple_symbols(self, symbols: List[str] = None, timeframes: List[str] = None, 
                            max_results: int = 20) -> Dict[str, Any]:
        """Scan multiple symbols and timeframes."""
        if symbols is None:
            symbols = self.large_cap_symbols[:20]  # Limit for demo
        
        if timeframes is None:
            timeframes = ['5min', '15min', '1hour']
        
        info(f"🔍 Scanning {len(symbols)} symbols across {len(timeframes)} timeframes")
        
        all_setups = []
        
        for symbol in symbols:
            for timeframe in timeframes:
                setups = self.scan_symbol(symbol, timeframe)
                all_setups.extend(setups)
                time.sleep(0.1)  # Rate limiting
        
        # Sort by grade and confidence
        all_setups.sort(key=lambda s: (s.grade, s.confidence), reverse=True)
        
        # Format results
        results = {
            "scan_summary": {
                "total_setups_found": len(all_setups),
                "showing_top": min(len(all_setups), max_results),
                "scan_timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "symbols_scanned": len(symbols),
                "timeframes_scanned": len(timeframes)
            },
            "top_setups": []
        }
        
        for setup in all_setups[:max_results]:
            setup_data = {
                "symbol": setup.symbol,
                "timeframe": setup.timeframe,
                "grade": setup.grade,
                "confidence": f"{setup.confidence:.1%}",
                "squeeze_status": setup.squeeze_status,
                "momentum_direction": setup.momentum_direction,
                "current_price": f"${setup.close_price:.2f}",
                "entry_price": f"${setup.entry_price:.2f}",
                "stop_loss": f"${setup.stop_loss:.2f}",
                "target_price": f"${setup.target_price:.2f}",
                "risk_reward": f"1:{(setup.target_price - setup.entry_price) / (setup.entry_price - setup.stop_loss):.1f}",
                "pattern_details": setup.pattern_details
            }
            results["top_setups"].append(setup_data)
        
        info(f"✅ Scan complete: Found {len(all_setups)} setups")
        return results


def run_simple_ttm_scan(symbols: List[str] = None, timeframes: List[str] = None, 
                       max_results: int = 20) -> Dict[str, Any]:
    """Run the simple TTM squeeze scan."""
    try:
        scanner = SimpleTTMSqueezeScanner()
        return scanner.scan_multiple_symbols(symbols, timeframes, max_results)
    except Exception as e:
        error(f"Simple TTM scan failed: {e}")
        return {
            "error": f"Scan failed: {str(e)}",
            "suggestion": "Check API key and market hours"
        }


if __name__ == "__main__":
    print("🔍 Simple TTM Squeeze Scanner (No TA-Lib Required)")
    print("=" * 60)
    
    # Test with a few symbols
    test_symbols = ['AAPL', 'MSFT', 'PLTR', 'NVDA']
    test_timeframes = ['5min', '15min']
    
    results = run_simple_ttm_scan(test_symbols, test_timeframes, 10)
    
    if "error" in results:
        print(f"❌ Error: {results['error']}")
    else:
        summary = results["scan_summary"]
        setups = results["top_setups"]
        
        print(f"📊 Found {summary['total_setups_found']} setups")
        print(f"🎯 Showing top {summary['showing_top']}")
        
        if setups:
            print("\n🏆 Top Setups:")
            for i, setup in enumerate(setups[:5], 1):
                print(f"{i}. {setup['symbol']} ({setup['timeframe']}) - {setup['grade']} ({setup['confidence']})")
                print(f"   Entry: {setup['entry_price']} | Target: {setup['target_price']} | R:R: {setup['risk_reward']}")
        else:
            print("\n📝 No setups found - try during market hours")
