"""Alpaca Trading Integration

This module connects to your Alpaca account and executes real trades
based on TTM squeeze setups found by the scanner.
"""
from __future__ import annotations

import os
import requests
import json
from datetime import datetime
from typing import Dict, List, Optional
try:
    from config import get_api_key
except ImportError:
    def get_api_key(key_name):
        return os.getenv(key_name)

try:
    from logger_util import info, warning
except ImportError:
    def info(msg): print(f"INFO: {msg}")
    def warning(msg): print(f"WARNING: {msg}")
    def error(msg): print(f"ERROR: {msg}")


class AlpacaTrader:
    """Real Alpaca trading integration."""
    
    def __init__(self, paper_trading: bool = True):
        """Initialize Alpaca trader.
        
        Args:
            paper_trading: If True, use paper trading. If False, use live trading.
        """
        self.paper_trading = paper_trading
        
        # Get Alpaca credentials from environment or config
        self.api_key = get_api_key('ALPACA_API_KEY')
        self.secret_key = get_api_key('ALPACA_API_SECRET')

        if not self.api_key or not self.secret_key:
            raise ValueError("Alpaca API credentials not found. Please set ALPACA_API_KEY and ALPACA_API_SECRET")
        
        # Set base URL based on trading mode
        if paper_trading:
            self.base_url = "https://paper-api.alpaca.markets"
            info("🧪 Alpaca Paper Trading Mode")
        else:
            self.base_url = "https://api.alpaca.markets"
            info("🚀 Alpaca Live Trading Mode")
        
        # Headers for API requests
        self.headers = {
            'APCA-API-KEY-ID': self.api_key,
            'APCA-API-SECRET-KEY': self.secret_key,
            'Content-Type': 'application/json'
        }
        
        # Test connection
        self.test_connection()

    def is_market_open(self) -> bool:
        """Check if the market is currently open."""
        try:
            response = requests.get(f"{self.base_url}/v2/clock", headers=self.headers)
            if response.status_code == 200:
                clock_data = response.json()
                return clock_data.get('is_open', False)
            return False
        except Exception as e:
            warning(f"Error checking market hours: {e}")
            return False

    def get_current_price(self, symbol: str) -> Optional[float]:
        """Get current price for a symbol."""
        try:
            # Try Alpaca first
            response = requests.get(f"{self.base_url}/v2/stocks/{symbol.upper()}/quotes/latest",
                                  headers=self.headers)
            if response.status_code == 200:
                data = response.json()
                quote = data.get('quote', {})
                # Use bid/ask midpoint for better pricing
                bid = float(quote.get('bp', 0))
                ask = float(quote.get('ap', 0))
                if bid > 0 and ask > 0:
                    return (bid + ask) / 2
                else:
                    # Fallback to last trade price
                    price = float(quote.get('p', 0))
                    if price > 0:
                        return price

            # Fallback to FMP API if Alpaca doesn't have price
            warning(f"Alpaca price not available for {symbol}, trying FMP...")
            fmp_price = self._get_fmp_price(symbol)
            if fmp_price:
                return fmp_price

            return None
        except Exception as e:
            warning(f"Error getting current price for {symbol}: {e}")
            return None

    def _get_fmp_price(self, symbol: str) -> Optional[float]:
        """Get price from FMP API as fallback."""
        try:
            from config import get_api_key
            fmp_key = get_api_key('FMP_API_KEY')

            url = f"https://financialmodelingprep.com/api/v3/quote/{symbol.upper()}?apikey={fmp_key}"
            response = requests.get(url, timeout=10)
            data = response.json()

            if data and len(data) > 0:
                price = float(data[0]['price'])
                info(f"📊 Got {symbol} price from FMP: ${price:.2f}")
                return price
            return None
        except Exception as e:
            warning(f"Error getting FMP price for {symbol}: {e}")
            return None
    
    def test_connection(self) -> bool:
        """Test connection to Alpaca API."""
        try:
            response = requests.get(f"{self.base_url}/v2/account", headers=self.headers)
            if response.status_code == 200:
                account_data = response.json()
                equity = float(account_data['equity'])
                buying_power = float(account_data['buying_power'])
                info(f"✅ Alpaca connected - Equity: ${equity:,.2f}, Buying Power: ${buying_power:,.2f}")
                return True
            else:
                warning(f"❌ Alpaca connection failed: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            warning(f"❌ Alpaca connection error: {e}")
            return False
    
    def get_account_info(self) -> Dict:
        """Get account information."""
        try:
            response = requests.get(f"{self.base_url}/v2/account", headers=self.headers)
            if response.status_code == 200:
                return response.json()
            else:
                warning(f"Failed to get account info: {response.status_code}")
                return {}
        except Exception as e:
            warning(f"Error getting account info: {e}")
            return {}
    
    def place_order(self, symbol: str, quantity: int, side: str = 'buy',
                   order_type: str = 'market', limit_price: Optional[float] = None,
                   stop_loss: Optional[float] = None, take_profit: Optional[float] = None) -> Dict:
        """Place an order with Alpaca.
        
        Args:
            symbol: Stock symbol (e.g., 'AAPL')
            quantity: Number of shares
            side: 'buy' or 'sell'
            order_type: 'market', 'limit', 'stop', etc.
            stop_loss: Stop loss price (optional)
            take_profit: Take profit price (optional)
        
        Returns:
            Order response from Alpaca
        """
        try:
            # Check if market is open
            market_open = self.is_market_open()

            # Adjust order type based on market hours
            if not market_open and order_type == 'market':
                # Use limit order when market is closed
                current_price = self.get_current_price(symbol)
                if current_price:
                    # Add small buffer for limit price
                    if side == 'buy':
                        limit_price = current_price * 1.001  # 0.1% above current price
                    else:
                        limit_price = current_price * 0.999  # 0.1% below current price

                    order_data = {
                        'symbol': symbol.upper(),
                        'qty': str(quantity),
                        'side': side,
                        'type': 'limit',
                        'limit_price': str(round(limit_price, 2)),
                        'time_in_force': 'day',  # Must be DAY for extended hours
                        'extended_hours': True  # Allow extended hours trading
                    }
                    info(f"📅 Market closed - using limit order at ${limit_price:.2f}")
                else:
                    return {
                        'success': False,
                        'error': 'Cannot get current price for limit order'
                    }
            else:
                # Basic order data for market hours
                order_data = {
                    'symbol': symbol.upper(),
                    'qty': str(quantity),
                    'side': side,
                    'type': order_type,
                    'time_in_force': 'day'
                }

                # Add limit price if specified
                if order_type == 'limit' and limit_price:
                    order_data['limit_price'] = str(limit_price)
            
            # Place main order
            response = requests.post(f"{self.base_url}/v2/orders",
                                   headers=self.headers,
                                   json=order_data)

            if response.status_code in [200, 201]:  # Both 200 and 201 are success
                order = response.json()
                order_id = order['id']
                order_status = order.get('status', 'unknown')

                info(f"✅ Order placed: {side.upper()} {quantity} {symbol} - Order ID: {order_id} - Status: {order_status}")

                # Place bracket orders if stop loss or take profit specified
                if stop_loss or take_profit:
                    self._place_bracket_orders(symbol, quantity, order_id, stop_loss, take_profit)

                return {
                    'success': True,
                    'order_id': order_id,
                    'order': order,
                    'status': order_status,
                    'message': f"Order placed successfully: {side.upper()} {quantity} {symbol}"
                }
            else:
                error_msg = f"Order failed: {response.status_code} - {response.text}"
                warning(error_msg)
                return {
                    'success': False,
                    'error': error_msg
                }
                
        except Exception as e:
            error_msg = f"Error placing order: {e}"
            warning(error_msg)
            return {
                'success': False,
                'error': error_msg
            }
    
    def _place_bracket_orders(self, symbol: str, quantity: int, parent_order_id: str,
                             stop_loss: Optional[float], take_profit: Optional[float]):
        """Place stop loss and take profit orders."""
        try:
            # Stop loss order
            if stop_loss:
                stop_order = {
                    'symbol': symbol.upper(),
                    'qty': str(quantity),
                    'side': 'sell',
                    'type': 'stop',
                    'time_in_force': 'gtc',  # Good till canceled
                    'stop_price': str(stop_loss)
                }
                
                stop_response = requests.post(f"{self.base_url}/v2/orders", 
                                            headers=self.headers, 
                                            json=stop_order)
                
                if stop_response.status_code == 201:
                    info(f"✅ Stop loss placed: ${stop_loss:.2f}")
                else:
                    warning(f"Stop loss failed: {stop_response.text}")
            
            # Take profit order
            if take_profit:
                profit_order = {
                    'symbol': symbol.upper(),
                    'qty': str(quantity),
                    'side': 'sell',
                    'type': 'limit',
                    'time_in_force': 'gtc',
                    'limit_price': str(take_profit)
                }
                
                profit_response = requests.post(f"{self.base_url}/v2/orders", 
                                              headers=self.headers, 
                                              json=profit_order)
                
                if profit_response.status_code == 201:
                    info(f"✅ Take profit placed: ${take_profit:.2f}")
                else:
                    warning(f"Take profit failed: {profit_response.text}")
                    
        except Exception as e:
            warning(f"Error placing bracket orders: {e}")
    
    def get_positions(self) -> List[Dict]:
        """Get current positions."""
        try:
            response = requests.get(f"{self.base_url}/v2/positions", headers=self.headers)
            if response.status_code == 200:
                return response.json()
            else:
                warning(f"Failed to get positions: {response.status_code}")
                return []
        except Exception as e:
            warning(f"Error getting positions: {e}")
            return []
    
    def get_orders(self, status: str = 'all') -> List[Dict]:
        """Get orders.
        
        Args:
            status: 'open', 'closed', 'all'
        """
        try:
            params = {'status': status, 'limit': 50}
            response = requests.get(f"{self.base_url}/v2/orders", 
                                  headers=self.headers, params=params)
            if response.status_code == 200:
                return response.json()
            else:
                warning(f"Failed to get orders: {response.status_code}")
                return []
        except Exception as e:
            warning(f"Error getting orders: {e}")
            return []
    
    def cancel_order(self, order_id: str) -> bool:
        """Cancel an order."""
        try:
            response = requests.delete(f"{self.base_url}/v2/orders/{order_id}", 
                                     headers=self.headers)
            if response.status_code == 204:
                info(f"✅ Order canceled: {order_id}")
                return True
            else:
                warning(f"Failed to cancel order: {response.status_code}")
                return False
        except Exception as e:
            warning(f"Error canceling order: {e}")
            return False
    
    def close_position(self, symbol: str) -> bool:
        """Close a position."""
        try:
            response = requests.delete(f"{self.base_url}/v2/positions/{symbol.upper()}", 
                                     headers=self.headers)
            if response.status_code == 207:
                info(f"✅ Position closed: {symbol}")
                return True
            else:
                warning(f"Failed to close position: {response.status_code}")
                return False
        except Exception as e:
            warning(f"Error closing position: {e}")
            return False


# Global trader instances
_paper_trader = None
_live_trader = None

def get_paper_trader() -> AlpacaTrader:
    """Get paper trading instance"""
    global _paper_trader
    if _paper_trader is None:
        _paper_trader = AlpacaTrader(paper_trading=True)
    return _paper_trader

def get_live_trader() -> AlpacaTrader:
    """Get live trading instance"""
    global _live_trader
    if _live_trader is None:
        _live_trader = AlpacaTrader(paper_trading=False)
    return _live_trader

def execute_ttm_trade_alpaca(trade_data: Dict, quantity: int,
                           stop_loss_pct: float = 0.03, paper_trading: bool = True) -> Dict:
    """Execute a TTM squeeze trade through Alpaca.
    
    Args:
        trade_data: Trade data from TTM scanner
        quantity: Number of shares to trade
        stop_loss_pct: Stop loss percentage (default 3%)
        paper_trading: Use paper trading if True
    
    Returns:
        Trade execution result
    """
    try:
        # Initialize Alpaca trader
        trader = AlpacaTrader(paper_trading=paper_trading)
        
        # Extract trade parameters
        symbol = trade_data['symbol']
        entry_price = trade_data['entry']
        target_price = trade_data['target']
        
        # Calculate stop loss
        stop_loss = entry_price * (1 - stop_loss_pct)
        
        # Place the order
        result = trader.place_order(
            symbol=symbol,
            quantity=quantity,
            side='buy',
            order_type='market',
            stop_loss=stop_loss,
            take_profit=target_price
        )
        
        if result['success']:
            return {
                'success': True,
                'message': f"TTM trade executed via Alpaca: {symbol}",
                'order_id': result['order_id'],
                'symbol': symbol,
                'quantity': quantity,
                'entry_price': entry_price,
                'stop_loss': stop_loss,
                'take_profit': target_price,
                'paper_trading': paper_trading
            }
        else:
            return {
                'success': False,
                'error': result['error']
            }
            
    except Exception as e:
        return {
            'success': False,
            'error': f"Alpaca trading error: {e}"
        }


if __name__ == "__main__":
    # Test Alpaca connection
    print("🧪 Testing Alpaca Connection...")
    
    try:
        trader = AlpacaTrader(paper_trading=True)
        account = trader.get_account_info()
        
        if account:
            print(f"✅ Connected to Alpaca")
            print(f"Account: {account.get('account_number', 'N/A')}")
            print(f"Equity: ${float(account.get('equity', 0)):,.2f}")
            print(f"Buying Power: ${float(account.get('buying_power', 0)):,.2f}")
            print(f"Paper Trading: {trader.paper_trading}")
        else:
            print("❌ Failed to connect to Alpaca")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        print("\n💡 To fix this:")
        print("1. Get your Alpaca API keys from https://app.alpaca.markets/")
        print("2. Set environment variables:")
        print("   ALPACA_API_KEY=your_api_key")
        print("   ALPACA_SECRET_KEY=your_secret_key")
        print("3. Or add them to your config.py file")
