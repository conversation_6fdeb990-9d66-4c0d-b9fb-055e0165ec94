#!/usr/bin/env python3
"""Performance Analytics Dashboard

Visual dashboard for trading performance analytics:
- Performance charts and graphs
- Detailed metrics display
- Export capabilities
- Interactive analysis
"""
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

try:
    import pandas as pd
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False
from datetime import datetime, timedelta
import json
from typing import Dict, List


class PerformanceDashboard:
    """Interactive performance analytics dashboard."""
    
    def __init__(self, tracker):
        self.tracker = tracker
        self.root = None
        self.canvas = None
        self.figure = None
        
    def create_dashboard(self):
        """Create the performance dashboard window."""
        self.root = tk.Toplevel()
        self.root.title("TTM Performance Analytics Dashboard")
        self.root.geometry("1400x900")
        self.root.configure(bg='#1e1e1e')
        
        self._create_header()
        self._create_main_content()
        self._create_controls()
        
        # Load initial data
        self.refresh_dashboard()
        
        return self.root
    
    def _create_header(self):
        """Create dashboard header."""
        header_frame = tk.Frame(self.root, bg='#2d2d2d', height=80)
        header_frame.pack(fill='x', padx=10, pady=5)
        header_frame.pack_propagate(False)
        
        # Title
        title_label = tk.Label(
            header_frame,
            text="📊 TTM PERFORMANCE ANALYTICS DASHBOARD",
            font=('Arial', 18, 'bold'),
            fg='#00ff88',
            bg='#2d2d2d'
        )
        title_label.pack(side='left', padx=20, pady=20)
        
        # Period selector
        period_frame = tk.Frame(header_frame, bg='#2d2d2d')
        period_frame.pack(side='right', padx=20, pady=20)
        
        tk.Label(period_frame, text="Period:", fg='white', bg='#2d2d2d', font=('Arial', 12)).pack(side='left', padx=5)
        
        self.period_var = tk.StringVar(value="30")
        period_combo = ttk.Combobox(
            period_frame,
            textvariable=self.period_var,
            values=['7', '30', '90', '180', '365'],
            width=8
        )
        period_combo.pack(side='left', padx=5)
        period_combo.bind('<<ComboboxSelected>>', lambda e: self.refresh_dashboard())
    
    def _create_main_content(self):
        """Create main content area."""
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Overview tab
        self._create_overview_tab()
        
        # Charts tab
        self._create_charts_tab()
        
        # Trade log tab
        self._create_trade_log_tab()
        
        # Strategy analysis tab
        self._create_strategy_tab()
    
    def _create_overview_tab(self):
        """Create overview metrics tab."""
        overview_frame = ttk.Frame(self.notebook)
        self.notebook.add(overview_frame, text='📊 Overview')
        
        # Metrics grid
        metrics_frame = tk.Frame(overview_frame, bg='white')
        metrics_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Create metric cards
        self.metric_cards = {}
        
        # Row 1
        row1 = tk.Frame(metrics_frame, bg='white')
        row1.pack(fill='x', pady=10)
        
        self.metric_cards['total_pnl'] = self._create_metric_card(row1, "Total P&L", "$0.00", "#00ff88")
        self.metric_cards['win_rate'] = self._create_metric_card(row1, "Win Rate", "0%", "#4a9eff")
        self.metric_cards['total_trades'] = self._create_metric_card(row1, "Total Trades", "0", "#ff6b6b")
        self.metric_cards['profit_factor'] = self._create_metric_card(row1, "Profit Factor", "0.0", "#ffa726")
        
        # Row 2
        row2 = tk.Frame(metrics_frame, bg='white')
        row2.pack(fill='x', pady=10)
        
        self.metric_cards['avg_win'] = self._create_metric_card(row2, "Avg Win", "$0.00", "#66bb6a")
        self.metric_cards['avg_loss'] = self._create_metric_card(row2, "Avg Loss", "$0.00", "#ef5350")
        self.metric_cards['largest_win'] = self._create_metric_card(row2, "Largest Win", "$0.00", "#26a69a")
        self.metric_cards['largest_loss'] = self._create_metric_card(row2, "Largest Loss", "$0.00", "#ff7043")
        
        # TTM Grade Performance
        ttm_frame = tk.LabelFrame(overview_frame, text="TTM Grade Performance", font=('Arial', 12, 'bold'))
        ttm_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        self.ttm_tree = ttk.Treeview(ttm_frame, columns=('Grade', 'Trades', 'Win Rate', 'Avg P&L'), show='headings', height=6)
        
        for col in ['Grade', 'Trades', 'Win Rate', 'Avg P&L']:
            self.ttm_tree.heading(col, text=col)
            self.ttm_tree.column(col, width=100, anchor='center')
        
        self.ttm_tree.pack(fill='both', expand=True, padx=10, pady=10)
    
    def _create_metric_card(self, parent, title, value, color):
        """Create a metric display card."""
        card = tk.Frame(parent, bg=color, relief='raised', bd=2)
        card.pack(side='left', fill='both', expand=True, padx=10, pady=5)
        
        title_label = tk.Label(card, text=title, font=('Arial', 10, 'bold'), bg=color, fg='white')
        title_label.pack(pady=(10, 5))
        
        value_label = tk.Label(card, text=value, font=('Arial', 16, 'bold'), bg=color, fg='white')
        value_label.pack(pady=(0, 10))
        
        return value_label
    
    def _create_charts_tab(self):
        """Create charts and graphs tab."""
        charts_frame = ttk.Frame(self.notebook)
        self.notebook.add(charts_frame, text='📈 Charts')

        if MATPLOTLIB_AVAILABLE:
            # Create matplotlib figure
            self.figure, ((self.ax1, self.ax2), (self.ax3, self.ax4)) = plt.subplots(2, 2, figsize=(12, 8))
            self.figure.patch.set_facecolor('#1e1e1e')

            # Create canvas
            self.canvas = FigureCanvasTkAgg(self.figure, charts_frame)
            self.canvas.get_tk_widget().pack(fill='both', expand=True, padx=10, pady=10)
        else:
            # Show message about missing matplotlib
            no_charts_label = tk.Label(
                charts_frame,
                text="📊 Charts require matplotlib\n\nInstall with: pip install matplotlib",
                font=('Arial', 14),
                fg='#ff6b6b',
                bg='white',
                justify='center'
            )
            no_charts_label.pack(expand=True, fill='both', padx=20, pady=20)
    
    def _create_trade_log_tab(self):
        """Create detailed trade log tab."""
        log_frame = ttk.Frame(self.notebook)
        self.notebook.add(log_frame, text='📋 Trade Log')
        
        # Controls
        controls_frame = tk.Frame(log_frame, bg='white')
        controls_frame.pack(fill='x', padx=10, pady=5)
        
        export_btn = tk.Button(controls_frame, text="📤 Export CSV", command=self.export_trades,
                              bg='#4a9eff', fg='white', font=('Arial', 10, 'bold'))
        export_btn.pack(side='left', padx=5)
        
        refresh_btn = tk.Button(controls_frame, text="🔄 Refresh", command=self.refresh_trade_log,
                               bg='#00ff88', fg='white', font=('Arial', 10, 'bold'))
        refresh_btn.pack(side='left', padx=5)
        
        # Trade log table
        columns = ('Date', 'Symbol', 'Strategy', 'Side', 'Entry', 'Exit', 'P&L', 'P&L%', 'Grade')
        self.trade_tree = ttk.Treeview(log_frame, columns=columns, show='headings', height=20)
        
        for col in columns:
            self.trade_tree.heading(col, text=col)
            if col in ['Entry', 'Exit', 'P&L']:
                self.trade_tree.column(col, width=80, anchor='e')
            elif col in ['P&L%']:
                self.trade_tree.column(col, width=60, anchor='e')
            else:
                self.trade_tree.column(col, width=80, anchor='center')
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(log_frame, orient='vertical', command=self.trade_tree.yview)
        self.trade_tree.configure(yscrollcommand=scrollbar.set)
        
        self.trade_tree.pack(side='left', fill='both', expand=True, padx=(10, 0), pady=10)
        scrollbar.pack(side='right', fill='y', padx=(0, 10), pady=10)
    
    def _create_strategy_tab(self):
        """Create strategy analysis tab."""
        strategy_frame = ttk.Frame(self.notebook)
        self.notebook.add(strategy_frame, text='🎯 Strategy Analysis')
        
        # Strategy comparison table
        strategy_label = tk.Label(strategy_frame, text="Strategy Performance Comparison", 
                                 font=('Arial', 14, 'bold'))
        strategy_label.pack(pady=10)
        
        columns = ('Strategy', 'Trades', 'Win Rate', 'Total P&L', 'Avg P&L', 'Sharpe Ratio')
        self.strategy_tree = ttk.Treeview(strategy_frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            self.strategy_tree.heading(col, text=col)
            self.strategy_tree.column(col, width=120, anchor='center')
        
        self.strategy_tree.pack(fill='both', expand=True, padx=10, pady=10)
    
    def _create_controls(self):
        """Create control buttons."""
        controls_frame = tk.Frame(self.root, bg='#1e1e1e', height=60)
        controls_frame.pack(fill='x', padx=10, pady=5)
        controls_frame.pack_propagate(False)
        
        refresh_btn = tk.Button(controls_frame, text="🔄 Refresh All", command=self.refresh_dashboard,
                               bg='#00ff88', fg='black', font=('Arial', 12, 'bold'))
        refresh_btn.pack(side='left', padx=10, pady=15)
        
        export_btn = tk.Button(controls_frame, text="📊 Export Report", command=self.export_report,
                              bg='#4a9eff', fg='white', font=('Arial', 12, 'bold'))
        export_btn.pack(side='left', padx=10, pady=15)
        
        close_btn = tk.Button(controls_frame, text="❌ Close", command=self.root.destroy,
                             bg='#ff6b6b', fg='white', font=('Arial', 12, 'bold'))
        close_btn.pack(side='right', padx=10, pady=15)
    
    def refresh_dashboard(self):
        """Refresh all dashboard data."""
        try:
            days = int(self.period_var.get())
            
            # Get performance summary
            summary = self.tracker.get_performance_summary(days)
            
            if "error" in summary:
                messagebox.showwarning("No Data", summary["error"])
                return
            
            # Update metric cards
            self._update_metric_cards(summary)
            
            # Update TTM grade performance
            self._update_ttm_performance()
            
            # Update charts
            self._update_charts(summary, days)
            
            # Update trade log
            self.refresh_trade_log()
            
            # Update strategy analysis
            self._update_strategy_analysis()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to refresh dashboard: {str(e)}")
    
    def _update_metric_cards(self, summary):
        """Update metric display cards."""
        self.metric_cards['total_pnl'].config(text=f"${summary['total_pnl']:.2f}")
        self.metric_cards['win_rate'].config(text=f"{summary['win_rate']:.1f}%")
        self.metric_cards['total_trades'].config(text=str(summary['total_trades']))
        self.metric_cards['profit_factor'].config(text=f"{summary['profit_factor']:.2f}")
        self.metric_cards['avg_win'].config(text=f"${summary['avg_win']:.2f}")
        self.metric_cards['avg_loss'].config(text=f"${summary['avg_loss']:.2f}")
        self.metric_cards['largest_win'].config(text=f"${summary['largest_win']:.2f}")
        self.metric_cards['largest_loss'].config(text=f"${summary['largest_loss']:.2f}")
    
    def _update_ttm_performance(self):
        """Update TTM grade performance table."""
        # Clear existing items
        for item in self.ttm_tree.get_children():
            self.ttm_tree.delete(item)
        
        # Get TTM grade performance
        grade_perf = self.tracker.get_ttm_grade_performance()
        
        for grade, data in grade_perf.items():
            self.ttm_tree.insert('', 'end', values=(
                grade,
                data['total_trades'],
                f"{data['win_rate']:.1f}%",
                f"${data['avg_pnl']:.2f}"
            ))
    
    def _update_charts(self, summary, days):
        """Update performance charts."""
        if not MATPLOTLIB_AVAILABLE or not hasattr(self, 'canvas') or not self.canvas:
            return

        # Clear previous plots
        for ax in [self.ax1, self.ax2, self.ax3, self.ax4]:
            ax.clear()
            ax.set_facecolor('#2d2d2d')

        # Chart 1: P&L over time (mock data for now)
        if NUMPY_AVAILABLE:
            import random
            dates = [f"Day {i}" for i in range(1, days+1)]
            cumulative_pnl = []
            total = 0
            for _ in range(days):
                total += random.uniform(-20, 25)
                cumulative_pnl.append(total)

            self.ax1.plot(range(len(dates)), cumulative_pnl, color='#00ff88', linewidth=2)
            self.ax1.set_title('Cumulative P&L', color='white', fontsize=12)
            self.ax1.tick_params(colors='white')

        # Chart 2: Win/Loss distribution
        wins = summary['winning_trades']
        losses = summary['losing_trades']

        if wins + losses > 0:
            self.ax2.pie([wins, losses], labels=['Wins', 'Losses'], colors=['#00ff88', '#ff6b6b'],
                        autopct='%1.1f%%', textprops={'color': 'white'})
            self.ax2.set_title('Win/Loss Distribution', color='white', fontsize=12)

        # Chart 3: Strategy breakdown
        if summary['strategy_breakdown']:
            strategies = [s['strategy'] for s in summary['strategy_breakdown']]
            pnls = [s['total_pnl'] for s in summary['strategy_breakdown']]

            colors = ['#4a9eff', '#ffa726', '#66bb6a', '#ef5350']
            self.ax3.bar(strategies, pnls, color=colors[:len(strategies)])
            self.ax3.set_title('Strategy Performance', color='white', fontsize=12)
            self.ax3.tick_params(colors='white')

        # Chart 4: Monthly performance (mock data)
        months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun']
        import random
        monthly_pnl = [random.uniform(-50, 150) for _ in range(6)]

        colors = ['#00ff88' if x > 0 else '#ff6b6b' for x in monthly_pnl]
        self.ax4.bar(months, monthly_pnl, color=colors)
        self.ax4.set_title('Monthly Performance', color='white', fontsize=12)
        self.ax4.tick_params(colors='white')

        self.figure.tight_layout()
        self.canvas.draw()
    
    def refresh_trade_log(self):
        """Refresh the trade log table."""
        # Clear existing items
        for item in self.trade_tree.get_children():
            self.trade_tree.delete(item)
        
        # This would get actual trades from the database
        # For now, showing mock data
        mock_trades = [
            ('2024-01-15', 'AAPL', 'TTM', 'LONG', '$150.00', '$153.00', '$300.00', '2.0%', 'A'),
            ('2024-01-14', 'NVDA', 'TTM', 'LONG', '$280.00', '$275.00', '-$500.00', '-1.8%', 'B'),
            ('2024-01-13', 'TSLA', 'TTM', 'LONG', '$200.00', '$205.00', '$250.00', '2.5%', 'A+'),
        ]
        
        for trade in mock_trades:
            self.trade_tree.insert('', 'end', values=trade)
    
    def _update_strategy_analysis(self):
        """Update strategy analysis table."""
        # Clear existing items
        for item in self.strategy_tree.get_children():
            self.strategy_tree.delete(item)
        
        # Mock strategy data
        strategies = [
            ('TTM Squeeze', 25, '68%', '$1,250.00', '$50.00', '1.45'),
            ('Breakout', 15, '60%', '$750.00', '$50.00', '1.20'),
            ('Pullback', 10, '70%', '$500.00', '$50.00', '1.60'),
        ]
        
        for strategy in strategies:
            self.strategy_tree.insert('', 'end', values=strategy)
    
    def export_trades(self):
        """Export trades to CSV."""
        filename = filedialog.asksaveasfilename(
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        
        if filename:
            # This would export actual trade data
            messagebox.showinfo("Export", f"Trades exported to {filename}")
    
    def export_report(self):
        """Export performance report."""
        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if filename:
            # This would export a comprehensive report
            messagebox.showinfo("Export", f"Performance report exported to {filename}")


def show_performance_dashboard(tracker):
    """Show the performance dashboard."""
    dashboard = PerformanceDashboard(tracker)
    return dashboard.create_dashboard()


if __name__ == "__main__":
    # Test the dashboard
    from performance_tracker import PerformanceTracker
    
    tracker = PerformanceTracker()
    dashboard = PerformanceDashboard(tracker)
    
    root = tk.Tk()
    root.withdraw()  # Hide main window
    
    dashboard_window = dashboard.create_dashboard()
    root.mainloop()
