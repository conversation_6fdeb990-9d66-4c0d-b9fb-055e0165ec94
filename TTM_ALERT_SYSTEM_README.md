# 🚨 TotalRecall TTM Alert System

## Real-Time TTM Squeeze Monitoring with Comprehensive Notifications

The TotalRecall TTM Alert System provides **continuous, automated monitoring** for TTM squeeze setups with **instant notifications** when high-probability trading opportunities are detected.

---

## 🎯 Key Features

### ✅ **Real-Time Monitoring**
- **Continuous background scanning** every 1-5 minutes during market hours
- **Automatic market hours detection** (pre-market, regular hours, after-hours)
- **Dynamic scan intervals** - faster during market open/close, slower when market closed
- **Auto-restart functionality** with error recovery

### 🔔 **Multi-Channel Notifications**
- **🖥️ Desktop notifications** - Windows toast notifications
- **🔊 Sound alerts** - System sounds based on alert priority
- **📱 GUI popups** - Detailed popup windows with trade information
- **🗣️ Voice alerts** - Text-to-speech announcements (optional)

### 📊 **Advanced Filtering System**
- **Grade filtering** - A+, A, B, C grades based on 5-point criteria system
- **Price range filtering** - Set minimum and maximum price limits
- **Volume filtering** - Minimum volume requirements
- **Confidence filtering** - Minimum confidence percentage
- **Risk/Reward filtering** - Minimum risk-to-reward ratios
- **Rate limiting** - Maximum alerts per hour to prevent spam

### 🎛️ **Professional Control Panel**
- **Easy-to-use GUI** for managing all settings
- **Real-time status monitoring** with live statistics
- **Alert history tracking** with detailed logs
- **Configuration management** - Save and load custom settings
- **Performance analytics** - Success rates, scan statistics

---

## 🚀 Quick Start

### 1. **Launch the System**
```bash
# Option 1: Interactive launcher (Recommended)
python launch_ttm_alert_system.py

# Option 2: Direct GUI launch
python launch_ttm_alert_system.py --gui

# Option 3: Quick start with defaults
python launch_ttm_alert_system.py --quick
```

### 2. **Configure Your Preferences**
- **Minimum Grade**: A+ (perfect setups) to C (decent setups)
- **Scan Interval**: 30 seconds to 60 minutes
- **Price Range**: $1 - $1000 (or your preferred range)
- **Volume**: Minimum 100,000 shares
- **Notifications**: Enable desktop, sound, and popup alerts

### 3. **Start Monitoring**
Click "🚀 Start Monitoring" and the system will:
- Begin scanning for TTM squeeze opportunities
- Send instant alerts when setups are found
- Continue running in the background
- Automatically handle market hours

---

## 📋 TTM Squeeze Criteria (5-Point System)

The system uses a **strict 5-point grading system** to evaluate TTM squeeze quality:

### ✅ **Required Criteria**
1. **Bollinger Bands inside Keltner Channels** - The squeeze condition
2. **8-EMA Rising** - 3+ consecutive periods of upward trend
3. **ATR Flip** - Increasing volatility pattern indicating momentum
4. **Histogram Pattern** - 2+ rising momentum bars
5. **Momentum Rising** - 3+ consecutive periods of increasing momentum

### 🏆 **Grading System**
- **A+ Grade**: 5/5 criteria met (Perfect setup - highest priority)
- **A Grade**: 4/5 criteria met (Strong setup - high priority)
- **B Grade**: 3/5 criteria met (Decent setup - medium priority)
- **C Grade**: 2/5 criteria met (Weak setup - low priority)

---

## 🎛️ Control Panel Features

### **Main Control Tab**
- **System Status**: Real-time monitoring status
- **Quick Settings**: Grade, interval, market hours
- **Live Alert Feed**: Real-time alert notifications

### **Filters Tab**
- **Price Filters**: Min/max price range
- **Volume Filters**: Minimum volume requirements
- **Quality Filters**: Confidence and risk/reward ratios
- **Rate Limiting**: Maximum alerts per hour

### **Notifications Tab**
- **Notification Types**: Desktop, sound, GUI, voice
- **Settings**: Timeouts, durations, volumes
- **Test Notifications**: Preview alert formats

### **Statistics Tab**
- **Performance Metrics**: Scan success rates
- **Alert Statistics**: Total alerts, filtering efficiency
- **System Health**: Uptime, error rates

### **History Tab**
- **Recent Alerts**: Last 50 alerts with details
- **Alert Details**: Symbol, grade, price, time
- **Export Options**: Save alert history

---

## ⚙️ Configuration Options

### **Monitoring Settings**
```json
{
  "min_grade": "A",           // Minimum grade to alert on
  "scan_interval": 120,       // Seconds between scans
  "market_hours_only": true   // Only scan during market hours
}
```

### **Filter Settings**
```json
{
  "min_price": 1.0,           // Minimum stock price
  "max_price": 1000.0,        // Maximum stock price
  "min_volume": 100000,       // Minimum daily volume
  "min_confidence": 0.6,      // Minimum confidence (60%)
  "min_risk_reward": 1.5,     // Minimum risk/reward ratio
  "max_alerts_per_hour": 10   // Rate limiting
}
```

### **Notification Settings**
```json
{
  "desktop": {
    "enabled": true,
    "timeout": 10             // Seconds to display
  },
  "sound": {
    "enabled": true,
    "critical_sound": "SystemExclamation"
  },
  "gui": {
    "enabled": true,
    "popup_duration": 5000    // Milliseconds
  }
}
```

---

## 🔧 Background Service

### **Automatic Operation**
The background service ensures **continuous monitoring** with:
- **Auto-restart** on errors or failures
- **Health monitoring** with automatic recovery
- **Market hours detection** for intelligent scheduling
- **Performance tracking** and error logging

### **Service Management**
```bash
# Start background service
python launch_ttm_alert_system.py --service

# Check service status
python launch_ttm_alert_system.py --status
```

---

## 📊 Market Hours Support

### **Automatic Detection**
- **Pre-market**: 4:00 AM - 9:30 AM ET
- **Regular Hours**: 9:30 AM - 4:00 PM ET
- **After Hours**: 4:00 PM - 8:00 PM ET
- **Weekend**: Monitoring paused

### **Dynamic Scanning**
- **Market Open/Close**: Every 30 seconds (high activity)
- **Regular Hours**: Every 2 minutes (normal activity)
- **Pre/After Hours**: Every 4 minutes (lower activity)
- **Market Closed**: Every 5 minutes (minimal activity)

---

## 🚨 Alert Examples

### **A+ Grade Alert**
```
🚨 TTM SQUEEZE ALERT: AAPL - Grade A+

📊 Setup Quality:
• Criteria Met: ✅ 5/5
• Confidence: 95%
• Breakout Probability: 87%

💰 Current Price: $150.25
📈 Volume: 2,500,000
⏰ Timeframe: 15min

🎯 Trade Setup:
• Entry: $150.50
• Stop Loss: $147.25 (-2.2%)
• Target: $156.75 (+4.2%)
• Risk/Reward: 1.9:1

⏰ Alert Time: 2024-01-15 14:30:15
```

---

## 🛠️ Installation & Dependencies

### **Required Dependencies**
```bash
pip install requests pandas numpy tkinter
```

### **Optional Dependencies** (Enhanced Features)
```bash
pip install plyer pyttsx3 pytz
```

### **File Structure**
```
TotalRecall/
├── launch_ttm_alert_system.py     # Main launcher
├── core/
│   ├── ttm_alert_system.py        # Core alert system
│   ├── ttm_alert_control_panel.py # GUI control panel
│   └── ttm_background_service.py  # Background service
├── scanners/
│   └── proper_ttm_squeeze_scanner.py # TTM scanner
└── data/
    ├── alert_config.json          # Alert configuration
    └── service_config.json        # Service configuration
```

---

## 🎯 Usage Examples

### **Basic Monitoring**
```python
from core.ttm_alert_system import start_ttm_alerts

# Start monitoring A+ grade setups every 2 minutes
start_ttm_alerts(min_grade="A+", scan_interval=120)
```

### **Custom Filtering**
```python
filters = {
    "min_price": 10.0,
    "max_price": 500.0,
    "min_volume": 500000,
    "min_confidence": 0.8
}

start_ttm_alerts(min_grade="A", scan_interval=60, custom_filters=filters)
```

---

## 📞 Support & Troubleshooting

### **Common Issues**
1. **No alerts received**: Check grade filter and market hours
2. **Too many alerts**: Increase grade requirement or add filters
3. **Missing notifications**: Install optional dependencies
4. **Scanner errors**: Check API configuration and internet connection

### **Logs & Debugging**
- **Alert logs**: `logs/ttm_service.log`
- **Error tracking**: Built-in error recovery
- **Status monitoring**: Real-time health checks

---

## 🎉 Ready to Trade!

Your TotalRecall TTM Alert System is now configured for **professional-grade trading alerts**. The system will continuously monitor the market and notify you instantly when high-probability TTM squeeze setups appear.

**Happy Trading! 📈🚀**
