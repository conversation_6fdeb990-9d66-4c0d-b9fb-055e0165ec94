"""Ultimate Trading Expert GUI Interface

A comprehensive trading interface with:
- Chat with the AI trading expert
- TTM Squeeze scanner with A-F grading
- Quantity adjustments and position sizing
- Real-time alerts for A/B grade opportunities
- Dynamic stop loss management
"""
from __future__ import annotations

import threading
import time
from datetime import datetime
from typing import Dict, List, Optional
import json
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox

from chat_core import chat_gpt
from ttm_squeeze_scanner import scan_ttm_squeeze_opportunities
from advanced_ttm_squeeze_scanner import run_ttm_squeeze_scan
from profit_target import ProfitTargetPlanner
from logger_util import info, warning

class TradingInterface:
    """Main trading interface with chat and scanning capabilities."""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Ultimate Trading Expert Interface")
        self.root.geometry("1200x800")
        self.root.configure(bg='#2b2b2b')

        self.scanning = False
        self.alert_thread = None
        self.last_scan_results = []
        self.planner = ProfitTargetPlanner()

        # Create the interface
        self.create_interface()

    def create_interface(self):
        """Create the main GUI interface."""
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=10)

        # Create tabs
        self.create_chat_tab()
        self.create_scanner_tab()
        self.create_position_tab()

    def create_chat_tab(self):
        """Create the chat interface tab."""
        chat_frame = ttk.Frame(self.notebook)
        self.notebook.add(chat_frame, text='💬 Chat & Analysis')

        # Title
        title_label = tk.Label(chat_frame, text='💬 Chat with Ultimate Trading Expert',
                              font=('Arial', 16, 'bold'), bg='#2b2b2b', fg='white')
        title_label.pack(pady=10)

        # Chat output area
        self.chat_output = scrolledtext.ScrolledText(chat_frame, height=25, width=80,
                                                    bg='#1e1e1e', fg='white',
                                                    font=('Consolas', 10))
        self.chat_output.pack(padx=10, pady=5, fill='both', expand=True)

        # Input frame
        input_frame = tk.Frame(chat_frame, bg='#2b2b2b')
        input_frame.pack(fill='x', padx=10, pady=5)

        self.chat_input = tk.Entry(input_frame, font=('Arial', 12), width=60)
        self.chat_input.pack(side='left', fill='x', expand=True, padx=(0, 5))
        self.chat_input.bind('<Return>', self.send_message)

        send_button = tk.Button(input_frame, text='Send', command=self.send_message,
                               bg='#4CAF50', fg='white', font=('Arial', 12, 'bold'))
        send_button.pack(side='right')

        # Clear button
        clear_button = tk.Button(chat_frame, text='Clear Chat', command=self.clear_chat,
                                bg='#f44336', fg='white', font=('Arial', 10))
        clear_button.pack(pady=5)
        
        # TTM Squeeze Scanner section
        scanner_column = [
            [sg.Text('🎯 TTM Squeeze Scanner', font=('Arial', 14, 'bold'))],
            [sg.Text('Min Grade:'), 
             sg.Combo(['A', 'B', 'C', 'D', 'F'], default_value='B', key='-MIN_GRADE-', size=(5, 1)),
             sg.Text('Max Results:'),
             sg.Input('10', key='-MAX_RESULTS-', size=(5, 1))],
            [sg.Button('🔍 Scan Now', key='-SCAN_TTM-'),
             sg.Button('🔄 Auto Scan', key='-AUTO_SCAN-'),
             sg.Button('⏹️ Stop Scan', key='-STOP_SCAN-')],
            [sg.Text('Scan Status:'), sg.Text('Ready', key='-SCAN_STATUS-', text_color='green')],
            [sg.Table(values=[], headings=['Symbol', 'Grade', 'Confidence', 'Entry', 'Stop', 'Target', 'R:R'],
                     max_col_width=15, auto_size_columns=True, justification='center',
                     key='-SCAN_RESULTS-', enable_events=True, size=(60, 10))],
            [sg.Text('🔔 Alerts: A/B Grade Opportunities', font=('Arial', 12, 'bold'))],
            [sg.Multiline(size=(60, 5), key='-ALERTS-', disabled=True,
                         background_color='#2d2d2d', text_color='yellow')]
        ]
        
        # Position Management section
        position_column = [
            [sg.Text('📊 Position Management', font=('Arial', 14, 'bold'))],
            [sg.Text('Symbol:'), sg.Input(key='-SYMBOL-', size=(10, 1)),
             sg.Text('Quantity:'), sg.Input(key='-QUANTITY-', size=(10, 1))],
            [sg.Text('Entry Price:'), sg.Input(key='-ENTRY_PRICE-', size=(10, 1)),
             sg.Text('Current Stop:'), sg.Input(key='-CURRENT_STOP-', size=(10, 1))],
            [sg.Button('💰 Calculate Position', key='-CALC_POSITION-'),
             sg.Button('🛡️ Update Stop Loss', key='-UPDATE_STOP-')],
            [sg.Text('Target Profit ($):'), sg.Input('50', key='-TARGET_PROFIT-', size=(10, 1)),
             sg.Button('🎯 Generate Plan', key='-GENERATE_PLAN-')],
            [sg.Multiline(size=(60, 8), key='-POSITION_OUTPUT-', disabled=True,
                         background_color='#1e1e1e', text_color='lightgreen')]
        ]
        
        # Main layout with tabs
        layout = [
            [sg.TabGroup([
                [sg.Tab('💬 Chat & Analysis', [chat_column])],
                [sg.Tab('🎯 TTM Scanner', [scanner_column])],
                [sg.Tab('📊 Position Manager', [position_column])]
            ])],
            [sg.Button('Exit', key='-EXIT-')]
        ]
        
        return layout
    
    def run_ttm_scan(self, min_grade: str = 'B', max_results: int = 10) -> List[Dict]:
        """Run TTM squeeze scan and return formatted results."""
        try:
            self.window['-SCAN_STATUS-'].update('Scanning...', text_color='orange')
            self.window.refresh()
            
            # Use the advanced scanner
            scan_result = run_ttm_squeeze_scan()
            
            # Parse results if they exist
            results = []
            if "🎯 TOP TTM SQUEEZE OPPORTUNITIES" in scan_result:
                lines = scan_result.split('\n')
                for line in lines:
                    if '📈' in line and '$' in line:
                        # Parse line like "📈 NVDA: $141.935 ..."
                        try:
                            parts = line.split()
                            symbol = parts[1].rstrip(':')
                            price = float(parts[2].replace('$', ''))
                            
                            # Create a mock result for display
                            results.append({
                                'symbol': symbol,
                                'grade': 'B',  # Default grade
                                'confidence': 85.0,
                                'entry': price,
                                'stop': round(price * 0.95, 2),
                                'target': round(price * 1.08, 2),
                                'rr': '1:2.4'
                            })
                        except (IndexError, ValueError):
                            continue
            
            # If no results from advanced scanner, use simple scanner
            if not results:
                simple_results = scan_ttm_squeeze_opportunities(min_grade=min_grade, max_results=max_results)
                if 'top_opportunities' in simple_results:
                    for opp in simple_results['top_opportunities']:
                        results.append({
                            'symbol': opp.get('symbol', 'N/A'),
                            'grade': opp.get('grade', 'C'),
                            'confidence': opp.get('confidence', 0.0) * 100,
                            'entry': opp.get('entry_price', 0.0),
                            'stop': opp.get('stop_loss', 0.0),
                            'target': opp.get('target_price', 0.0),
                            'rr': f"1:{opp.get('risk_reward', 0.0):.1f}"
                        })
            
            self.window['-SCAN_STATUS-'].update('Scan Complete', text_color='green')
            return results
            
        except Exception as e:
            self.window['-SCAN_STATUS-'].update(f'Error: {str(e)}', text_color='red')
            warning(f"TTM scan failed: {e}")
            return []
    
    def update_scan_results(self, results: List[Dict]):
        """Update the scan results table."""
        table_data = []
        for result in results:
            table_data.append([
                result['symbol'],
                result['grade'],
                f"{result['confidence']:.1f}%",
                f"${result['entry']:.2f}",
                f"${result['stop']:.2f}",
                f"${result['target']:.2f}",
                result['rr']
            ])
        
        self.window['-SCAN_RESULTS-'].update(values=table_data)
        self.last_scan_results = results
    
    def check_for_alerts(self):
        """Check for new A/B grade opportunities and alert."""
        while self.scanning:
            try:
                results = self.run_ttm_scan(min_grade='B', max_results=20)
                
                # Check for A/B grade opportunities
                high_grade_opportunities = [r for r in results if r['grade'] in ['A', 'B', 'A+']]
                
                if high_grade_opportunities:
                    timestamp = datetime.now().strftime("%H:%M:%S")
                    alert_text = f"[{timestamp}] 🚨 HIGH GRADE ALERT!\n"
                    
                    for opp in high_grade_opportunities:
                        alert_text += f"  {opp['symbol']} - Grade {opp['grade']} ({opp['confidence']:.1f}%)\n"
                        alert_text += f"  Entry: ${opp['entry']:.2f} | Stop: ${opp['stop']:.2f} | Target: ${opp['target']:.2f}\n"
                    
                    alert_text += "\n"
                    
                    # Update alerts window
                    current_alerts = self.window['-ALERTS-'].get()
                    self.window['-ALERTS-'].update(current_alerts + alert_text)
                    
                    # Update main results
                    self.update_scan_results(results)
                
                # Wait 5 minutes before next scan
                time.sleep(300)
                
            except Exception as e:
                warning(f"Alert scan failed: {e}")
                time.sleep(60)  # Wait 1 minute on error
    
    def run(self):
        """Run the main GUI loop."""
        layout = self.create_layout()
        self.window = sg.Window('Ultimate Trading Expert Interface', layout, finalize=True)
        
        # Add welcome message
        welcome_msg = """🤖 Welcome to Ultimate Trading Expert!

Features:
• Chat with AI for trading advice
• TTM Squeeze scanner with A-F grading
• Dynamic stop loss management
• Real-time alerts for high-grade opportunities

Try asking: "make me $50 profit today" or "scan for TTM squeeze setups"
"""
        self.window['-CHAT_OUTPUT-'].update(welcome_msg)
        
        while True:
            event, values = self.window.read(timeout=1000)
            
            if event in (sg.WIN_CLOSED, '-EXIT-'):
                break
            
            elif event == '-SEND-' or (event == '-CHAT_INPUT-' and values['-CHAT_INPUT-']):
                user_input = values['-CHAT_INPUT-'].strip()
                if user_input:
                    # Add user message to chat
                    current_chat = self.window['-CHAT_OUTPUT-'].get()
                    self.window['-CHAT_OUTPUT-'].update(current_chat + f"\n💬 You: {user_input}\n")
                    
                    # Get AI response
                    try:
                        response = chat_gpt(user_input)
                        self.window['-CHAT_OUTPUT-'].update(current_chat + f"\n💬 You: {user_input}\n🤖 AI: {response}\n\n")
                    except Exception as e:
                        self.window['-CHAT_OUTPUT-'].update(current_chat + f"\n💬 You: {user_input}\n❌ Error: {str(e)}\n\n")
                    
                    # Clear input
                    self.window['-CHAT_INPUT-'].update('')
            
            elif event == '-CLEAR_CHAT-':
                self.window['-CHAT_OUTPUT-'].update('')
            
            elif event == '-SCAN_TTM-':
                min_grade = values['-MIN_GRADE-']
                max_results = int(values['-MAX_RESULTS-']) if values['-MAX_RESULTS-'].isdigit() else 10
                results = self.run_ttm_scan(min_grade, max_results)
                self.update_scan_results(results)
            
            elif event == '-AUTO_SCAN-':
                if not self.scanning:
                    self.scanning = True
                    self.alert_thread = threading.Thread(target=self.check_for_alerts, daemon=True)
                    self.alert_thread.start()
                    self.window['-SCAN_STATUS-'].update('Auto-scanning...', text_color='blue')
            
            elif event == '-STOP_SCAN-':
                self.scanning = False
                self.window['-SCAN_STATUS-'].update('Stopped', text_color='red')
            
            elif event == '-GENERATE_PLAN-':
                try:
                    target_profit = float(values['-TARGET_PROFIT-']) if values['-TARGET_PROFIT-'] else 50
                    plan = self.planner.plan(target_profit_dollars=target_profit)
                    
                    plan_text = f"""🎯 TRADING PLAN FOR ${target_profit} PROFIT:

Symbol: {plan['symbol']}
Entry Price: ${plan['entry_price']:.2f}
Quantity: {plan['quantity']} shares
Stop Loss: ${plan['stop_loss']:.2f} ({plan.get('stop_type', 'fixed')})
Take Profit: ${plan['take_profit']:.2f}
Required Cash: ${plan['required_cash']:.2f}

Stop Analysis: {plan.get('stop_analysis', 'Standard stop loss')}

TTM Analysis: {plan.get('ttm_analysis', 'No TTM analysis')}
"""
                    self.window['-POSITION_OUTPUT-'].update(plan_text)
                    
                except Exception as e:
                    self.window['-POSITION_OUTPUT-'].update(f"❌ Error generating plan: {str(e)}")
            
            elif event == '-UPDATE_STOP-':
                try:
                    symbol = values['-SYMBOL-'].upper()
                    entry_price = float(values['-ENTRY_PRICE-'])
                    current_stop = float(values['-CURRENT_STOP-'])
                    
                    if symbol and entry_price and current_stop:
                        stop_update = self.planner.update_stop_loss(symbol, entry_price, current_stop)
                        
                        update_text = f"""🛡️ STOP LOSS UPDATE FOR {symbol}:

Current Stop: ${stop_update['current_stop']:.2f}
Recommended Stop: ${stop_update['recommended_stop']:.2f}
Stop Type: {stop_update['stop_type']}
Should Update: {'✅ YES' if stop_update['should_update'] else '❌ NO'}

Analysis: {stop_update['analysis']}
Trailing Update: {stop_update['trailing_update']}
"""
                        self.window['-POSITION_OUTPUT-'].update(update_text)
                    else:
                        self.window['-POSITION_OUTPUT-'].update("❌ Please fill in Symbol, Entry Price, and Current Stop")
                        
                except ValueError:
                    self.window['-POSITION_OUTPUT-'].update("❌ Please enter valid numbers for prices")
                except Exception as e:
                    self.window['-POSITION_OUTPUT-'].update(f"❌ Error updating stop: {str(e)}")
        
        # Cleanup
        self.scanning = False
        self.window.close()


def main():
    """Launch the trading interface."""
    interface = TradingInterface()
    interface.run()


if __name__ == "__main__":
    main()
