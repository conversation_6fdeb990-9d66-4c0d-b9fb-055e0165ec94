"""Simple JSON logger utility for Ultimate Trading Expert.

All logs are printed to stdout in JSON format for easy ingestion by log
aggregators as well as human readability.
"""
from __future__ import annotations

import json
import sys
from datetime import datetime, timezone
from typing import Any, Dict

LOCAL_TZ = datetime.now().astimezone().tzinfo  # User local timezone


def _now_iso() -> str:
    """Return the current timestamp in ISO8601 format with milliseconds."""
    return datetime.now(tz=LOCAL_TZ).isoformat(timespec="milliseconds")


def log(level: str, msg: str, **extra: Any) -> None:
    """Emit a structured JSON log line.

    Parameters
    ----------
    level : str
        Log severity: DEBUG, INFO, WARNING, ERROR, CRITICAL
    msg : str
        Main log message (human-friendly).
    extra : Any
        Arbitrary serialisable key/value pairs for context.
    """
    record: Dict[str, Any] = {
        "ts": _now_iso(),
        "level": level.upper(),
        "msg": msg,
        **extra,
    }
    print(json.dumps(record, default=str), file=sys.stdout, flush=True)


# Convenience wrappers ------------------------------------------------------

def debug(msg: str, **extra: Any) -> None:  # noqa: D401
    log("DEBUG", msg, **extra)


def info(msg: str, **extra: Any) -> None:  # noqa: D401
    log("INFO", msg, **extra)


def warning(msg: str, **extra: Any) -> None:  # noqa: D401
    log("WARNING", msg, **extra)


def error(msg: str, **extra: Any) -> None:  # noqa: D401
    log("ERROR", msg, **extra)


def critical(msg: str, **extra: Any) -> None:  # noqa: D401
    log("CRITICAL", msg, **extra) 