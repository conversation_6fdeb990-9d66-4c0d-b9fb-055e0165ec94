#!/usr/bin/env python3
"""
Alpaca MCP Server Integration for TTM Trading System

This module integrates the Alpaca MCP Server to give our AI chat
direct trading capabilities through natural language.

Features:
- Direct trade execution through chat
- Real-time portfolio analysis
- Natural language risk/reward calculations
- Live market data access
- Advanced options trading capabilities
"""

import os
import subprocess
import json
import asyncio
from pathlib import Path
from typing import Dict, Any, Optional

class AlpacaMCPIntegration:
    """Integration with Alpaca MCP Server for enhanced AI trading capabilities."""
    
    def __init__(self):
        self.mcp_server_path = None
        self.is_running = False
        self.process = None
        
    def setup_mcp_server(self):
        """Download and setup the Alpaca MCP Server."""
        try:
            # Create integrations directory
            integrations_dir = Path("integrations")
            integrations_dir.mkdir(exist_ok=True)
            
            mcp_dir = integrations_dir / "alpaca-mcp-server"
            
            if not mcp_dir.exists():
                print("📥 Downloading Alpaca MCP Server...")
                subprocess.run([
                    "git", "clone", 
                    "https://github.com/alpacahq/alpaca-mcp-server.git",
                    str(mcp_dir)
                ], check=True)
                
                # Install requirements
                print("📦 Installing MCP Server requirements...")
                subprocess.run([
                    "pip", "install", "-r", str(mcp_dir / "requirements.txt")
                ], check=True)
                
                print("✅ Alpaca MCP Server setup complete!")
            
            self.mcp_server_path = mcp_dir / "alpaca_mcp_server.py"
            return True
            
        except Exception as e:
            print(f"❌ MCP Server setup failed: {e}")
            return False
    
    def configure_credentials(self, api_key: str, secret_key: str, paper_trading: bool = True):
        """Configure Alpaca API credentials for MCP server."""
        try:
            if not self.mcp_server_path:
                raise ValueError("MCP Server not setup. Call setup_mcp_server() first.")
            
            # Create .env file in MCP server directory
            env_file = self.mcp_server_path.parent / ".env"
            
            env_content = f"""ALPACA_API_KEY = "{api_key}"
ALPACA_SECRET_KEY = "{secret_key}"
PAPER = {str(paper_trading).lower()}
"""
            
            with open(env_file, 'w') as f:
                f.write(env_content)
            
            print(f"✅ Credentials configured for {'paper' if paper_trading else 'live'} trading")
            return True
            
        except Exception as e:
            print(f"❌ Credential configuration failed: {e}")
            return False
    
    def start_mcp_server(self):
        """Start the Alpaca MCP Server process."""
        try:
            if not self.mcp_server_path or not self.mcp_server_path.exists():
                raise ValueError("MCP Server not found. Run setup_mcp_server() first.")
            
            if self.is_running:
                print("⚠️ MCP Server already running")
                return True
            
            print("🚀 Starting Alpaca MCP Server...")
            
            # Start MCP server as subprocess
            self.process = subprocess.Popen([
                "python", str(self.mcp_server_path)
            ], cwd=str(self.mcp_server_path.parent))
            
            self.is_running = True
            print("✅ Alpaca MCP Server started successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Failed to start MCP Server: {e}")
            return False
    
    def stop_mcp_server(self):
        """Stop the Alpaca MCP Server process."""
        try:
            if self.process:
                self.process.terminate()
                self.process.wait()
                self.process = None
            
            self.is_running = False
            print("⏹️ Alpaca MCP Server stopped")
            return True
            
        except Exception as e:
            print(f"❌ Failed to stop MCP Server: {e}")
            return False
    
    def create_claude_config(self, api_key: str, secret_key: str, paper_trading: bool = True):
        """Create Claude Desktop configuration for MCP integration."""
        try:
            if not self.mcp_server_path:
                raise ValueError("MCP Server not setup")
            
            config = {
                "mcpServers": {
                    "alpaca": {
                        "command": "python",
                        "args": [str(self.mcp_server_path)],
                        "env": {
                            "ALPACA_API_KEY": api_key,
                            "ALPACA_SECRET_KEY": secret_key,
                            "PAPER": str(paper_trading).lower()
                        }
                    }
                }
            }
            
            # Save config for user reference
            config_file = Path("integrations/claude_desktop_config.json")
            with open(config_file, 'w') as f:
                json.dump(config, f, indent=2)
            
            print(f"✅ Claude Desktop config created: {config_file}")
            print("\n📋 SETUP INSTRUCTIONS:")
            print("1. Open Claude Desktop")
            print("2. Go to Settings → Developer → Edit Config")
            print("3. Copy the contents from claude_desktop_config.json")
            print("4. Restart Claude Desktop")
            print("5. You can now trade directly through Claude chat!")
            
            return config
            
        except Exception as e:
            print(f"❌ Claude config creation failed: {e}")
            return None

def integrate_with_ttm_system():
    """Integrate MCP server with our existing TTM trading system."""
    
    print("🔗 INTEGRATING ALPACA MCP WITH TTM SYSTEM")
    print("=" * 50)
    
    # Initialize integration
    mcp = AlpacaMCPIntegration()
    
    # Setup MCP server
    if not mcp.setup_mcp_server():
        return False
    
    # Get credentials from environment or user input
    api_key = os.getenv('ALPACA_API_KEY')
    secret_key = os.getenv('ALPACA_SECRET_KEY')
    
    if not api_key or not secret_key:
        print("\n🔑 ALPACA API CREDENTIALS NEEDED")
        print("Please set your Alpaca API credentials:")
        print("Option 1: Set environment variables ALPACA_API_KEY and ALPACA_SECRET_KEY")
        print("Option 2: Enter them now (for paper trading setup)")
        
        choice = input("\nDo you want to enter credentials now? (y/n): ").lower()
        if choice == 'y':
            api_key = input("Enter Alpaca API Key: ").strip()
            secret_key = input("Enter Alpaca Secret Key: ").strip()
        else:
            print("⚠️ Credentials needed for MCP integration")
            return False
    
    # Configure for paper trading by default
    paper_trading = True
    
    # Configure credentials
    if not mcp.configure_credentials(api_key, secret_key, paper_trading):
        return False
    
    # Create Claude config
    config = mcp.create_claude_config(api_key, secret_key, paper_trading)
    if not config:
        return False
    
    # Start MCP server
    if not mcp.start_mcp_server():
        return False
    
    print("\n🎉 INTEGRATION COMPLETE!")
    print("\n💡 WHAT YOU CAN NOW DO:")
    print("• Ask Claude: 'What's my current PLTR position risk/reward?'")
    print("• Say: 'Buy 10 shares of AAPL at market price'")
    print("• Request: 'Show me my portfolio performance today'")
    print("• Command: 'Place a bull call spread on TSLA'")
    print("• Query: 'What are the Greeks for my SPY options?'")
    
    print("\n🚀 ENHANCED TTM CAPABILITIES:")
    print("• Live Scanner → AI Analysis → Direct Execution")
    print("• Natural language portfolio management")
    print("• Real-time risk/reward calculations")
    print("• Advanced options strategies through chat")
    print("• Complete account oversight")
    
    return mcp

def main():
    """Main integration function."""
    try:
        mcp = integrate_with_ttm_system()
        
        if mcp:
            print(f"\n⚡ MCP Server running at: {mcp.mcp_server_path}")
            print("🔄 Integration with TTM system active")
            print("\n💡 Next Steps:")
            print("1. Configure Claude Desktop with the provided config")
            print("2. Restart Claude Desktop")
            print("3. Start trading through natural language!")
            
            # Keep server running
            try:
                print("\n🔄 MCP Server running... Press Ctrl+C to stop")
                while True:
                    import time
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n⏹️ Stopping MCP Server...")
                mcp.stop_mcp_server()
                print("✅ Integration stopped")
        
    except Exception as e:
        print(f"❌ Integration failed: {e}")

if __name__ == "__main__":
    main()
