#!/usr/bin/env python3
"""Beginner-friendly options formatter with simple tables and plain English."""

from typing import Dict

def format_beginner_options_response(result: Dict) -> str:
    """Format options analysis in super simple tables with plain English."""

    if "error" in result:
        return f"""
❌ **Oops! Something went wrong:**
{result['error']}

💡 **Try asking about popular stocks like:**
• Apple (AAPL)
• Tesla (TSLA)
• S&P 500 ETF (SPY)
"""

    # Extract data
    symbol = result.get('symbol', 'STOCK')
    strategy = result.get('strategy', 'Unknown')
    max_profit = result.get('max_profit', 0)
    max_loss = result.get('max_loss', 0)
    probability = result.get('probability_of_profit', 0) * 100
    net_premium = abs(result.get('net_premium', 0))
    current_price = result.get('current_price', 0)
    breakeven = result.get('breakeven_points', [0])[0] if result.get('breakeven_points') else 0

    # Extract real market data

    # Create simple response
    response = f"""
🎯 **{symbol} Options Strategy: {strategy}**

## 📊 **Simple Breakdown:**

| What You Need to Know | Amount |
|----------------------|--------|
| 💰 **Maximum Profit** | ${max_profit:.2f} |
| 📉 **Maximum Loss** | ${max_loss:.2f} |
| 🎲 **Chance of Profit** | {probability:.0f}% |
| 💵 **Cost to Enter** | ${net_premium:.2f} |
| 📈 **Current Stock Price** | ${current_price:.2f} |
| 🎯 **Breakeven Price** | ${breakeven:.2f} |

## 💡 **What This Means in Plain English:**

{_get_plain_english_explanation(strategy, symbol, max_profit, max_loss, probability)}

## 📝 **Trade Example:**

| Action | What Happens | Money In/Out | Result |
|--------|--------------|--------------|--------|
| Enter Trade | Pay premium to open position | -${net_premium:.2f} | Position opened |
| If Profitable | Close position at target | +${net_premium + max_profit:.2f} | Profit: +${max_profit:.2f} |
| If Unprofitable | Close position at loss | +${net_premium - max_loss:.2f} | Loss: -${max_loss:.2f} |

**How to read it:**
• **Money In:** What you pay (negative number)
• **Money Out:** What you receive when closing
• **Profit/Loss:** Money Out minus Money In

{_get_strategy_explanation(strategy)}
"""

    return response



def _get_plain_english_explanation(strategy: str, symbol: str, max_profit: float, max_loss: float, probability: float) -> str:
    """Get plain English explanation of the strategy."""

    explanations = {
        "Bull Call Spread": f"You think {symbol} will go UP. You buy a cheaper call option and sell a more expensive one. If {symbol} rises above your target, you make up to ${max_profit:.2f}. If it doesn't, you lose up to ${max_loss:.2f}. You have a {probability:.0f}% chance of making money.",

        "Bear Put Spread": f"You think {symbol} will go DOWN. You buy a put option and sell another put. If {symbol} falls below your target, you make up to ${max_profit:.2f}. If it doesn't, you lose up to ${max_loss:.2f}. You have a {probability:.0f}% chance of making money.",

        "Iron Condor": f"You think {symbol} will stay FLAT (not move much). You sell options above and below the current price. If {symbol} stays in the middle, you keep the premium and make up to ${max_profit:.2f}. If it moves too much either way, you lose up to ${max_loss:.2f}. You have a {probability:.0f}% chance of making money.",

        "Long Call": f"You think {symbol} will go UP significantly. You buy a call option. If {symbol} rises above your strike price, you can make unlimited profit. If it doesn't, you lose your premium of ${max_loss:.2f}. You have a {probability:.0f}% chance of making money.",

        "Long Put": f"You think {symbol} will go DOWN significantly. You buy a put option. If {symbol} falls below your strike price, you make up to ${max_profit:.2f}. If it doesn't, you lose your premium of ${max_loss:.2f}. You have a {probability:.0f}% chance of making money."
    }

    return explanations.get(strategy, f"This strategy gives you a {probability:.0f}% chance to make up to ${max_profit:.2f}, but you could lose up to ${max_loss:.2f}.")

def _get_strategy_explanation(strategy: str) -> str:
    """Get additional strategy explanation."""

    explanations = {
        "Bull Call Spread": "**Bull Call Spread:** You're betting the stock goes up, but not by too much. It's cheaper than buying a single call option.",

        "Bear Put Spread": "**Bear Put Spread:** You're betting the stock goes down, but you limit both your risk and reward.",

        "Iron Condor": "**Iron Condor:** You're betting the stock stays roughly where it is. You collect money upfront but give it back if the stock moves too much.",

        "Long Call": "**Long Call:** Simple bet that the stock goes up. High risk, high reward.",

        "Long Put": "**Long Put:** Simple bet that the stock goes down. Limited risk, good reward if you're right."
    }

    return explanations.get(strategy, "**Strategy:** This is an options trading strategy with defined risk and reward.")

# Test function
if __name__ == "__main__":
    # Test with mock data
    test_result = {
        "symbol": "AAPL",
        "strategy": "Bull Call Spread",
        "max_profit": 250.00,
        "max_loss": 150.00,
        "probability_of_profit": 0.65,
        "net_premium": 150.00
    }

    print(format_beginner_options_response(test_result))
