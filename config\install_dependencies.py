#!/usr/bin/env python3
"""
Installation script for TTM Squeeze Scanner dependencies.

This script helps install the required dependencies, especially TA-Lib which can be tricky.
"""

import subprocess
import sys
import platform
import os


def run_command(cmd, description=""):
    """Run a command and return success status."""
    print(f"🔧 {description}")
    print(f"   Running: {cmd}")
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"   ✅ Success")
            return True
        else:
            print(f"   ❌ Failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"   ❌ Exception: {e}")
        return False


def install_talib():
    """Install TA-Lib with platform-specific instructions."""
    system = platform.system().lower()
    
    print("📊 Installing TA-Lib (Technical Analysis Library)")
    print("=" * 50)
    
    if system == "windows":
        print("🪟 Windows detected")
        print("💡 TA-Lib on Windows requires pre-compiled binaries")
        print("   Option 1: Try pip install first")
        
        if run_command("pip install TA-Lib", "Installing TA-Lib via pip"):
            return True
        
        print("\n   Option 2: Install from wheel")
        print("   1. Download TA-Lib wheel from: https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib")
        print("   2. Install with: pip install TA_Lib-0.4.25-cp311-cp311-win_amd64.whl")
        print("   3. Or use conda: conda install -c conda-forge ta-lib")
        
        return False
        
    elif system == "darwin":  # macOS
        print("🍎 macOS detected")
        print("   Installing TA-Lib via Homebrew...")
        
        if run_command("brew install ta-lib", "Installing TA-Lib C library"):
            if run_command("pip install TA-Lib", "Installing Python TA-Lib wrapper"):
                return True
        
        print("\n💡 If Homebrew failed, try:")
        print("   1. Install Homebrew: /bin/bash -c \"$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\"")
        print("   2. Then run: brew install ta-lib && pip install TA-Lib")
        
        return False
        
    else:  # Linux
        print("🐧 Linux detected")
        print("   Installing TA-Lib...")
        
        # Try different package managers
        commands = [
            ("sudo apt-get update && sudo apt-get install -y libta-lib-dev", "Installing via apt (Ubuntu/Debian)"),
            ("sudo yum install -y ta-lib-devel", "Installing via yum (CentOS/RHEL)"),
            ("sudo dnf install -y ta-lib-devel", "Installing via dnf (Fedora)"),
        ]
        
        for cmd, desc in commands:
            if run_command(cmd, desc):
                if run_command("pip install TA-Lib", "Installing Python TA-Lib wrapper"):
                    return True
                break
        
        print("\n💡 If package manager failed, compile from source:")
        print("   wget http://prdownloads.sourceforge.net/ta-lib/ta-lib-0.4.0-src.tar.gz")
        print("   tar -xzf ta-lib-0.4.0-src.tar.gz")
        print("   cd ta-lib/")
        print("   ./configure --prefix=/usr")
        print("   make")
        print("   sudo make install")
        print("   pip install TA-Lib")
        
        return False


def install_other_dependencies():
    """Install other required dependencies."""
    print("\n📦 Installing other dependencies")
    print("=" * 50)
    
    dependencies = [
        "aiohttp>=3.8.0",
        "scipy>=1.11.0", 
        "pandas>=2.2.2",
        "numpy>=1.26.4",
    ]
    
    success = True
    for dep in dependencies:
        if not run_command(f"pip install {dep}", f"Installing {dep}"):
            success = False
    
    return success


def check_installation():
    """Check if all dependencies are properly installed."""
    print("\n🔍 Checking installation")
    print("=" * 50)
    
    imports = [
        ("pandas", "import pandas as pd"),
        ("numpy", "import numpy as np"),
        ("talib", "import talib"),
        ("aiohttp", "import aiohttp"),
        ("scipy", "import scipy"),
    ]
    
    all_good = True
    for name, import_stmt in imports:
        try:
            exec(import_stmt)
            print(f"✅ {name}: OK")
        except ImportError as e:
            print(f"❌ {name}: FAILED - {e}")
            all_good = False
    
    return all_good


def main():
    """Main installation process."""
    print("🚀 TTM Squeeze Scanner Dependency Installer")
    print("=" * 60)
    print("This script will install the required dependencies for the")
    print("Advanced TTM Squeeze Scanner, including the tricky TA-Lib library.")
    print("=" * 60)
    
    # Check Python version
    python_version = sys.version_info
    print(f"🐍 Python version: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 8):
        print("❌ Python 3.8+ is required")
        return False
    
    # Install standard dependencies first
    print("\n📦 Installing standard dependencies...")
    if not install_other_dependencies():
        print("⚠️  Some standard dependencies failed to install")
    
    # Install TA-Lib (the tricky one)
    print("\n📊 Installing TA-Lib...")
    talib_success = install_talib()
    
    # Check everything
    print("\n🔍 Final check...")
    all_good = check_installation()
    
    print("\n" + "=" * 60)
    print("📋 INSTALLATION SUMMARY")
    print("=" * 60)
    
    if all_good:
        print("🎉 All dependencies installed successfully!")
        print("\n✅ You can now run the TTM Squeeze Scanner:")
        print("   python test_ttm_squeeze_scanner.py")
        print("   python advanced_ttm_squeeze_scanner.py")
    else:
        print("⚠️  Some dependencies failed to install")
        print("\n💡 Manual installation steps:")
        
        if not talib_success:
            print("\n📊 For TA-Lib:")
            system = platform.system().lower()
            if system == "windows":
                print("   • Download from: https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib")
                print("   • Or use conda: conda install -c conda-forge ta-lib")
            elif system == "darwin":
                print("   • Install Homebrew, then: brew install ta-lib && pip install TA-Lib")
            else:
                print("   • Install system package: sudo apt-get install libta-lib-dev")
                print("   • Then: pip install TA-Lib")
        
        print("\n📞 Need help?")
        print("   • Check the README.md for detailed instructions")
        print("   • Visit TA-Lib documentation: https://ta-lib.org/")
        print("   • Search for platform-specific installation guides")
    
    return all_good


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
