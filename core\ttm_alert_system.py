#!/usr/bin/env python3
"""
TTM Alert System - Real-time monitoring for perfect TTM setups
Integrates with the STRICT TTM scanner to provide instant alerts
"""

import threading
import time
import json
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

try:
    from core.logger_util import info, warning, error
except ImportError:
    def info(msg): print(f"ℹ️ {msg}")
    def warning(msg): print(f"⚠️ {msg}")
    def error(msg): print(f"❌ {msg}")

try:
    from scanners.proper_ttm_squeeze_scanner import ProperTTMSqueezeScanner
    SCANNER_AVAILABLE = True
except ImportError:
    SCANNER_AVAILABLE = False
    warning("TTM Scanner not available for alerts")


@dataclass
class TTMAlert:
    """TTM Alert data structure."""
    symbol: str
    grade: str
    criteria_count: int
    confidence: float
    price: float
    timeframe: str
    timestamp: datetime
    entry_price: float
    stop_loss: float
    target_price: float
    missing_criteria: List[str]


class TTMAlertSystem:
    """Real-time TTM alert monitoring system."""
    
    def __init__(self):
        self.is_running = False
        self.alert_thread = None
        self.min_grade = "A"
        self.scan_interval = 120  # 2 minutes
        self.last_alerts = []
        self.alert_history = []
        
    def start_monitoring(self, min_grade: str = "A", scan_interval: int = 120) -> str:
        """Start real-time TTM monitoring."""
        if self.is_running:
            return f"🔔 TTM Alert monitoring already running (Grade {self.min_grade}+)"
        
        if not SCANNER_AVAILABLE:
            return "❌ TTM Scanner not available. Cannot start monitoring."
        
        self.min_grade = min_grade
        self.scan_interval = scan_interval
        self.is_running = True
        
        self.alert_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.alert_thread.start()
        
        info(f"🚨 TTM Alert monitoring started - Grade {min_grade}+ every {scan_interval}s")
        
        return f"""🚨 TTM ALERT MONITORING STARTED!

**Alert Settings:**
• Minimum Grade: {min_grade}+
• Scan Frequency: Every {scan_interval//60} minutes
• Criteria: STRICT 5-point system
• Status: 🟢 ACTIVE

**Monitoring Criteria:**
✅ BB inside KC (Bollinger Bands inside Keltner Channels)
✅ 8EMA Rising (3+ consecutive periods)
✅ ATR Flip (ATR increasing pattern)
✅ Histogram Pattern (2+ rising momentum bars)
✅ Momentum Rising (3+ consecutive periods)

**Grade System:**
• 5/5 criteria = A+ (Perfect setup)
• 4/5 criteria = A (Strong setup)
• 3/5 criteria = B (Decent setup)

🔔 You'll get instant alerts when quality TTM setups appear!"""
    
    def stop_monitoring(self) -> str:
        """Stop TTM monitoring."""
        if not self.is_running:
            return "🔕 TTM Alert monitoring is not running"
        
        self.is_running = False
        info("🔕 TTM Alert monitoring stopped")
        return "🔕 TTM Alert monitoring stopped"
    
    def get_status(self) -> Dict[str, Any]:
        """Get current monitoring status."""
        return {
            "is_running": self.is_running,
            "min_grade": self.min_grade,
            "scan_interval": self.scan_interval,
            "last_scan": datetime.now().strftime("%H:%M:%S"),
            "active_alerts": len(self.last_alerts),
            "total_alerts_today": len([a for a in self.alert_history 
                                     if a.timestamp.date() == datetime.now().date()])
        }
    
    def get_recent_alerts(self, limit: int = 10) -> List[TTMAlert]:
        """Get recent alerts."""
        return self.alert_history[-limit:] if self.alert_history else []
    
    def _monitoring_loop(self):
        """Main monitoring loop."""
        while self.is_running:
            try:
                self._scan_for_alerts()
                time.sleep(self.scan_interval)
            except Exception as e:
                error(f"TTM Alert monitoring error: {e}")
                time.sleep(60)  # Wait 1 minute on error
    
    def _scan_for_alerts(self):
        """Scan for new TTM alerts."""
        try:
            scanner = ProperTTMSqueezeScanner()
            opportunities = scanner.scan_all_symbols()
            
            # Filter by grade
            grade_values = {"A+": 5, "A": 4, "B": 3, "C": 2, "D": 1}
            min_grade_value = grade_values.get(self.min_grade, 4)
            
            new_alerts = []
            for opp in opportunities:
                if grade_values.get(opp['grade'], 0) >= min_grade_value:
                    # Check if this is a new alert (not seen in last scan)
                    if not self._is_duplicate_alert(opp):
                        alert = self._create_alert(opp)
                        new_alerts.append(alert)
                        self.alert_history.append(alert)
            
            if new_alerts:
                self._send_alerts(new_alerts)
            
            self.last_alerts = [self._create_alert(opp) for opp in opportunities 
                              if grade_values.get(opp['grade'], 0) >= min_grade_value]
            
        except Exception as e:
            error(f"Error scanning for TTM alerts: {e}")
    
    def _is_duplicate_alert(self, opportunity: Dict) -> bool:
        """Check if this opportunity was already alerted recently."""
        symbol = opportunity['symbol']
        grade = opportunity['grade']
        
        # Check if same symbol/grade was alerted in last scan
        for alert in self.last_alerts:
            if alert.symbol == symbol and alert.grade == grade:
                return True
        return False
    
    def _create_alert(self, opportunity: Dict) -> TTMAlert:
        """Create TTM alert from opportunity."""
        missing_criteria = [name for name, passed in opportunity.get('criteria_details', {}).items() 
                          if not passed]
        
        return TTMAlert(
            symbol=opportunity['symbol'],
            grade=opportunity['grade'],
            criteria_count=opportunity.get('criteria_count', 0),
            confidence=opportunity.get('confidence', 0),
            price=opportunity.get('price', 0),
            timeframe=opportunity.get('timeframe', '15min'),
            timestamp=datetime.now(),
            entry_price=opportunity.get('entry_price', 0),
            stop_loss=opportunity.get('stop_loss', 0),
            target_price=opportunity.get('target_price', 0),
            missing_criteria=missing_criteria
        )
    
    def _send_alerts(self, alerts: List[TTMAlert]):
        """Send alerts to user."""
        for alert in alerts:
            alert_msg = self._format_alert(alert)
            print(alert_msg)  # In real implementation, this would be a notification
            info(f"TTM Alert: {alert.symbol} Grade {alert.grade}")
    
    def _format_alert(self, alert: TTMAlert) -> str:
        """Format alert message."""
        criteria_status = "✅" if alert.criteria_count == 5 else f"{alert.criteria_count}/5"
        
        msg = f"""🚨 TTM ALERT: {alert.symbol} - Grade {alert.grade}

📊 **Setup Details:**
• Criteria Met: {criteria_status}
• Confidence: {alert.confidence*100:.0f}%
• Price: ${alert.price:.2f}
• Timeframe: {alert.timeframe}

💰 **Trade Setup:**
• Entry: ${alert.entry_price:.2f}
• Stop Loss: ${alert.stop_loss:.2f}
• Target: ${alert.target_price:.2f}
• Risk/Reward: {((alert.target_price - alert.entry_price) / (alert.entry_price - alert.stop_loss)):.1f}:1"""

        if alert.missing_criteria:
            msg += f"\n\n⚠️ **Missing:** {', '.join(alert.missing_criteria)}"
        
        msg += f"\n\n⏰ **Time:** {alert.timestamp.strftime('%H:%M:%S')}"
        
        return msg


# Global alert system instance
_alert_system = TTMAlertSystem()


def get_alert_system() -> TTMAlertSystem:
    """Get the global alert system instance."""
    return _alert_system


def start_ttm_alerts(min_grade: str = "A") -> str:
    """Start TTM alert monitoring."""
    return _alert_system.start_monitoring(min_grade)


def stop_ttm_alerts() -> str:
    """Stop TTM alert monitoring."""
    return _alert_system.stop_monitoring()


def get_alert_status() -> Dict[str, Any]:
    """Get alert system status."""
    return _alert_system.get_status()


def get_recent_ttm_alerts(limit: int = 10) -> List[TTMAlert]:
    """Get recent TTM alerts."""
    return _alert_system.get_recent_alerts(limit)
