#!/usr/bin/env python3
"""
TTM Alert System - Real-time monitoring for perfect TTM setups
Enhanced with desktop notifications, sound alerts, and comprehensive filtering
"""

import threading
import time
import json
import os
import sys
import winsound
from datetime import datetime, time as dt_time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path

# Windows notification support
try:
    from plyer import notification
    NOTIFICATIONS_AVAILABLE = True
except ImportError:
    NOTIFICATIONS_AVAILABLE = False
    print("⚠️ Install plyer for desktop notifications: pip install plyer")

# GUI popup support
try:
    import tkinter as tk
    from tkinter import messagebox, Toplevel
    GUI_AVAILABLE = True
except ImportError:
    GUI_AVAILABLE = False

try:
    from core.logger_util import info, warning, error
except ImportError:
    def info(msg): print(f"ℹ️ {msg}")
    def warning(msg): print(f"⚠️ {msg}")
    def error(msg): print(f"❌ {msg}")

try:
    from scanners.proper_ttm_squeeze_scanner import ProperTTMSqueezeScanner
    SCANNER_AVAILABLE = True
except ImportError:
    SCANNER_AVAILABLE = False
    warning("TTM Scanner not available for alerts")

try:
    from core.ttm_setup_manager import store_ttm_setup
    SETUP_MANAGER_AVAILABLE = True
except ImportError:
    SETUP_MANAGER_AVAILABLE = False
    warning("TTM Setup Manager not available")


@dataclass
class TTMAlert:
    """Enhanced TTM Alert data structure with notification support."""
    symbol: str
    grade: str
    criteria_count: int
    confidence: float
    price: float
    timeframe: str
    timestamp: datetime
    entry_price: float
    stop_loss: float
    target_price: float
    missing_criteria: List[str]
    alert_id: str = ""
    volume: float = 0.0
    market_cap: float = 0.0
    sector: str = ""
    risk_reward_ratio: float = 0.0
    breakout_probability: float = 0.0

    def __post_init__(self):
        """Calculate derived fields after initialization."""
        if not self.alert_id:
            self.alert_id = f"{self.symbol}_{self.grade}_{int(self.timestamp.timestamp())}"

        if self.entry_price > 0 and self.stop_loss > 0 and self.target_price > 0:
            risk = abs(self.entry_price - self.stop_loss)
            reward = abs(self.target_price - self.entry_price)
            self.risk_reward_ratio = reward / risk if risk > 0 else 0.0


class NotificationManager:
    """Manages different types of notifications for TTM alerts."""

    def __init__(self, config_path: str = "data/alert_config.json"):
        self.config_path = config_path
        self.config = self._load_config()
        self.active_popups = []

    def _load_config(self) -> Dict[str, Any]:
        """Load notification configuration."""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r') as f:
                    return json.load(f)
        except Exception as e:
            warning(f"Failed to load alert config: {e}")

        # Default configuration
        return {
            "voice": {"enabled": True, "rate": 150, "volume": 0.8},
            "sound": {"enabled": True, "critical_sound": "SystemExclamation"},
            "gui": {"enabled": True, "popup_duration": 5000},
            "desktop": {"enabled": True, "timeout": 10},
            "alert_levels": {
                "A+": "CRITICAL",
                "A": "HIGH",
                "B": "MEDIUM",
                "C": "LOW"
            }
        }

    def send_notification(self, alert: TTMAlert, alert_level: str = None):
        """Send notification using configured methods."""
        if not alert_level:
            alert_level = self.config.get("alert_levels", {}).get(alert.grade, "MEDIUM")

        # Desktop notification
        if self.config.get("desktop", {}).get("enabled", True) and NOTIFICATIONS_AVAILABLE:
            self._send_desktop_notification(alert)

        # Sound alert
        if self.config.get("sound", {}).get("enabled", True):
            self._play_sound_alert(alert_level)

        # GUI popup
        if self.config.get("gui", {}).get("enabled", True) and GUI_AVAILABLE:
            self._show_gui_popup(alert)

        # Voice alert
        if self.config.get("voice", {}).get("enabled", False):
            self._speak_alert(alert)

    def _send_desktop_notification(self, alert: TTMAlert):
        """Send Windows desktop notification."""
        try:
            title = f"🚨 TTM Alert: {alert.symbol} - Grade {alert.grade}"
            message = f"Price: ${alert.price:.2f}\nConfidence: {alert.confidence*100:.0f}%\nR/R: {alert.risk_reward_ratio:.1f}:1"

            notification.notify(
                title=title,
                message=message,
                app_name="TotalRecall Trading",
                timeout=self.config.get("desktop", {}).get("timeout", 10)
            )
        except Exception as e:
            warning(f"Desktop notification failed: {e}")

    def _play_sound_alert(self, alert_level: str):
        """Play system sound based on alert level."""
        try:
            sound_map = {
                "CRITICAL": winsound.MB_ICONEXCLAMATION,
                "HIGH": winsound.MB_ICONASTERISK,
                "MEDIUM": winsound.MB_ICONQUESTION,
                "LOW": winsound.MB_OK
            }
            sound_type = sound_map.get(alert_level, winsound.MB_OK)
            winsound.MessageBeep(sound_type)
        except Exception as e:
            warning(f"Sound alert failed: {e}")

    def _show_gui_popup(self, alert: TTMAlert):
        """Show GUI popup window."""
        try:
            def show_popup():
                popup = tk.Toplevel()
                popup.title(f"TTM Alert: {alert.symbol}")
                popup.geometry("400x300")
                popup.attributes('-topmost', True)

                # Alert content
                content = f"""
🚨 TTM SQUEEZE ALERT 🚨

Symbol: {alert.symbol}
Grade: {alert.grade} ({alert.criteria_count}/5 criteria)
Confidence: {alert.confidence*100:.0f}%
Price: ${alert.price:.2f}
Timeframe: {alert.timeframe}

💰 Trade Setup:
Entry: ${alert.entry_price:.2f}
Stop Loss: ${alert.stop_loss:.2f}
Target: ${alert.target_price:.2f}
Risk/Reward: {alert.risk_reward_ratio:.1f}:1

⏰ Time: {alert.timestamp.strftime('%H:%M:%S')}
                """

                label = tk.Label(popup, text=content, justify=tk.LEFT, font=("Consolas", 10))
                label.pack(padx=20, pady=20)

                # Auto-close after duration
                duration = self.config.get("gui", {}).get("popup_duration", 5000)
                popup.after(duration, popup.destroy)

                self.active_popups.append(popup)

            # Run in main thread if possible
            if hasattr(tk, '_default_root') and tk._default_root:
                tk._default_root.after(0, show_popup)
            else:
                show_popup()

        except Exception as e:
            warning(f"GUI popup failed: {e}")

    def _speak_alert(self, alert: TTMAlert):
        """Speak alert using text-to-speech."""
        try:
            import pyttsx3
            engine = pyttsx3.init()

            rate = self.config.get("voice", {}).get("rate", 150)
            volume = self.config.get("voice", {}).get("volume", 0.8)

            engine.setProperty('rate', rate)
            engine.setProperty('volume', volume)

            message = f"TTM Alert: {alert.symbol} grade {alert.grade} at {alert.price:.2f} dollars"
            engine.say(message)
            engine.runAndWait()

        except ImportError:
            info("Install pyttsx3 for voice alerts: pip install pyttsx3")
        except Exception as e:
            warning(f"Voice alert failed: {e}")


class TTMAlertSystem:
    """Enhanced real-time TTM alert monitoring system with comprehensive filtering."""

    def __init__(self):
        self.is_running = False
        self.alert_thread = None
        self.min_grade = "A"
        self.scan_interval = 120  # 2 minutes
        self.last_alerts = []
        self.alert_history = []
        self.notification_manager = NotificationManager()

        # Enhanced filtering options
        self.filters = {
            "min_price": 1.0,
            "max_price": 1000.0,
            "min_volume": 100000,
            "min_confidence": 0.6,
            "min_risk_reward": 1.5,
            "excluded_symbols": [],
            "included_sectors": [],
            "market_hours_only": True,
            "max_alerts_per_hour": 10
        }

        # Market hours (Eastern Time)
        self.market_hours = {
            "premarket_start": dt_time(4, 0),   # 4:00 AM ET
            "market_open": dt_time(9, 30),      # 9:30 AM ET
            "market_close": dt_time(16, 0),     # 4:00 PM ET
            "afterhours_end": dt_time(20, 0)    # 8:00 PM ET
        }

        # Alert rate limiting
        self.alert_timestamps = []
        self.duplicate_cache = {}

        # Statistics tracking
        self.stats = {
            "total_scans": 0,
            "alerts_sent": 0,
            "alerts_filtered": 0,
            "last_scan_time": None,
            "scan_errors": 0
        }
        
    def start_monitoring(self, min_grade: str = "A", scan_interval: int = 120,
                        custom_filters: Dict[str, Any] = None) -> str:
        """Start enhanced real-time TTM monitoring with comprehensive filtering."""
        if self.is_running:
            return f"🔔 TTM Alert monitoring already running (Grade {self.min_grade}+)"

        if not SCANNER_AVAILABLE:
            return "❌ TTM Scanner not available. Cannot start monitoring."

        # Update configuration
        self.min_grade = min_grade
        self.scan_interval = max(30, scan_interval)  # Minimum 30 seconds

        # Apply custom filters if provided
        if custom_filters:
            self.filters.update(custom_filters)

        # Validate market hours if enabled
        if self.filters.get("market_hours_only", True):
            if not self._is_market_time():
                return f"""⏰ MARKET CLOSED - Monitoring will start when market opens

**Next Market Session:**
• Pre-market: 4:00 AM ET
• Regular Hours: 9:30 AM - 4:00 PM ET
• After Hours: Until 8:00 PM ET

**Current Settings:**
• Grade Filter: {min_grade}+
• Scan Interval: {scan_interval//60} minutes
• Price Range: ${self.filters['min_price']:.2f} - ${self.filters['max_price']:.2f}
• Min Volume: {self.filters['min_volume']:,}
• Min Confidence: {self.filters['min_confidence']*100:.0f}%

Monitoring will automatically start during market hours."""

        self.is_running = True
        self.alert_thread = threading.Thread(target=self._enhanced_monitoring_loop, daemon=True)
        self.alert_thread.start()

        info(f"🚨 Enhanced TTM Alert monitoring started - Grade {min_grade}+ every {scan_interval}s")

        return f"""🚨 ENHANCED TTM ALERT MONITORING STARTED!

**Alert Settings:**
• Minimum Grade: {min_grade}+
• Scan Frequency: Every {scan_interval//60} minutes
• Market Hours Only: {self.filters['market_hours_only']}
• Status: 🟢 ACTIVE

**Filtering Criteria:**
💰 Price Range: ${self.filters['min_price']:.2f} - ${self.filters['max_price']:.2f}
📊 Min Volume: {self.filters['min_volume']:,}
🎯 Min Confidence: {self.filters['min_confidence']*100:.0f}%
⚖️ Min Risk/Reward: {self.filters['min_risk_reward']:.1f}:1
🚫 Max Alerts/Hour: {self.filters['max_alerts_per_hour']}

**Notification Methods:**
🔔 Desktop Notifications: {NOTIFICATIONS_AVAILABLE}
🔊 Sound Alerts: Enabled
🖥️ GUI Popups: {GUI_AVAILABLE}
📢 Voice Alerts: Available

**TTM Criteria (5-Point System):**
✅ Bollinger Bands inside Keltner Channels
✅ 8-EMA Rising (3+ consecutive periods)
✅ ATR Flip (increasing volatility pattern)
✅ Histogram Pattern (2+ rising momentum bars)
✅ Momentum Rising (3+ consecutive periods)

🎯 Ready to catch high-probability TTM squeeze breakouts!"""
    
    def stop_monitoring(self) -> str:
        """Stop TTM monitoring."""
        if not self.is_running:
            return "🔕 TTM Alert monitoring is not running"
        
        self.is_running = False
        info("🔕 TTM Alert monitoring stopped")
        return "🔕 TTM Alert monitoring stopped"
    
    def get_status(self) -> Dict[str, Any]:
        """Get current monitoring status."""
        return {
            "is_running": self.is_running,
            "min_grade": self.min_grade,
            "scan_interval": self.scan_interval,
            "last_scan": datetime.now().strftime("%H:%M:%S"),
            "active_alerts": len(self.last_alerts),
            "total_alerts_today": len([a for a in self.alert_history 
                                     if a.timestamp.date() == datetime.now().date()])
        }
    
    def get_recent_alerts(self, limit: int = 10) -> List[TTMAlert]:
        """Get recent alerts."""
        return self.alert_history[-limit:] if self.alert_history else []
    
    def _enhanced_monitoring_loop(self):
        """Enhanced monitoring loop with market hours and intelligent scanning."""
        info("🚀 Enhanced TTM monitoring loop started")

        while self.is_running:
            try:
                # Check market hours if enabled
                if self.filters.get("market_hours_only", True):
                    if not self._is_market_time():
                        info("⏰ Market closed - waiting for next session")
                        time.sleep(300)  # Check every 5 minutes when market closed
                        continue

                # Perform scan
                self._enhanced_scan_for_alerts()
                self.stats["total_scans"] += 1
                self.stats["last_scan_time"] = datetime.now()

                # Dynamic sleep interval based on market activity
                sleep_time = self._calculate_dynamic_interval()
                time.sleep(sleep_time)

            except Exception as e:
                error(f"TTM Alert monitoring error: {e}")
                self.stats["scan_errors"] += 1
                time.sleep(60)  # Wait 1 minute on error

    def _is_market_time(self) -> bool:
        """Check if current time is within market hours (ET)."""
        try:
            from datetime import datetime
            import pytz

            # Get current Eastern Time
            et_tz = pytz.timezone('US/Eastern')
            current_et = datetime.now(et_tz).time()
            current_weekday = datetime.now(et_tz).weekday()

            # Skip weekends (Saturday=5, Sunday=6)
            if current_weekday >= 5:
                return False

            # Check if within trading hours (including pre/after market)
            return (self.market_hours["premarket_start"] <= current_et <=
                   self.market_hours["afterhours_end"])

        except ImportError:
            # Fallback without timezone support
            current_time = datetime.now().time()
            current_weekday = datetime.now().weekday()

            if current_weekday >= 5:
                return False

            return (dt_time(4, 0) <= current_time <= dt_time(20, 0))
        except Exception as e:
            warning(f"Market time check failed: {e}")
            return True  # Default to allowing scans

    def _calculate_dynamic_interval(self) -> int:
        """Calculate dynamic scan interval based on market conditions."""
        base_interval = self.scan_interval

        # Faster scanning during market hours
        if self._is_market_time():
            current_time = datetime.now().time()

            # More frequent during market open/close
            if (dt_time(9, 20) <= current_time <= dt_time(10, 0) or  # Market open
                dt_time(15, 30) <= current_time <= dt_time(16, 30)):  # Market close
                return max(30, base_interval // 2)

            # Regular frequency during market hours
            elif dt_time(9, 30) <= current_time <= dt_time(16, 0):
                return base_interval

            # Slower during pre/after hours
            else:
                return base_interval * 2

        # Much slower when market closed
        return max(300, base_interval * 4)  # Minimum 5 minutes when closed
    
    def _enhanced_scan_for_alerts(self):
        """Enhanced scan with comprehensive filtering and rate limiting."""
        try:
            # Check rate limiting
            if not self._check_rate_limit():
                return

            scanner = ProperTTMSqueezeScanner()
            opportunities = scanner.scan_all_symbols()

            info(f"🔍 Scanned {len(opportunities)} opportunities")

            # Store ALL setups found (before filtering for alerts)
            if SETUP_MANAGER_AVAILABLE:
                stored_count = 0
                for opp in opportunities:
                    try:
                        setup_id = store_ttm_setup(opp)
                        if setup_id:
                            stored_count += 1
                    except Exception as e:
                        warning(f"Failed to store setup {opp.get('symbol', 'unknown')}: {e}")

                if stored_count > 0:
                    info(f"💾 Stored {stored_count} TTM setups to database")

            # Apply all filters for alerting
            filtered_opportunities = []
            for opp in opportunities:
                if self._passes_all_filters(opp):
                    filtered_opportunities.append(opp)
                else:
                    self.stats["alerts_filtered"] += 1

            info(f"✅ {len(filtered_opportunities)} opportunities passed alert filters")

            # Check for new alerts
            new_alerts = []
            for opp in filtered_opportunities:
                if not self._is_duplicate_alert(opp):
                    alert = self._create_enhanced_alert(opp)
                    new_alerts.append(alert)
                    self.alert_history.append(alert)

            # Send notifications for new alerts
            if new_alerts:
                self._send_enhanced_alerts(new_alerts)
                self.stats["alerts_sent"] += len(new_alerts)
                info(f"🚨 Sent {len(new_alerts)} new TTM alerts")

            # Update last alerts for duplicate detection
            self.last_alerts = [self._create_enhanced_alert(opp) for opp in filtered_opportunities]

        except Exception as e:
            error(f"Error in enhanced TTM scan: {e}")
            self.stats["scan_errors"] += 1

    def _passes_all_filters(self, opportunity: Dict[str, Any]) -> bool:
        """Check if opportunity passes all configured filters."""
        try:
            # Grade filter
            grade_values = {"A+": 5, "A": 4, "B": 3, "C": 2, "D": 1}
            min_grade_value = grade_values.get(self.min_grade, 4)
            if grade_values.get(opportunity.get('grade', 'F'), 0) < min_grade_value:
                return False

            # Price filter
            price = opportunity.get('price', 0)
            if not (self.filters['min_price'] <= price <= self.filters['max_price']):
                return False

            # Volume filter
            volume = opportunity.get('volume', 0)
            if volume < self.filters['min_volume']:
                return False

            # Confidence filter
            confidence = opportunity.get('confidence', 0)
            if confidence < self.filters['min_confidence']:
                return False

            # Symbol exclusion filter
            symbol = opportunity.get('symbol', '')
            if symbol in self.filters['excluded_symbols']:
                return False

            # Risk/reward filter (if available)
            entry = opportunity.get('entry_price', 0)
            stop = opportunity.get('stop_loss', 0)
            target = opportunity.get('target_price', 0)

            if entry > 0 and stop > 0 and target > 0:
                risk = abs(entry - stop)
                reward = abs(target - entry)
                if risk > 0:
                    rr_ratio = reward / risk
                    if rr_ratio < self.filters['min_risk_reward']:
                        return False

            return True

        except Exception as e:
            warning(f"Filter check failed for {opportunity.get('symbol', 'unknown')}: {e}")
            return False

    def _check_rate_limit(self) -> bool:
        """Check if we're within the alert rate limit."""
        current_time = datetime.now()
        max_alerts = self.filters.get('max_alerts_per_hour', 10)

        # Clean old timestamps (older than 1 hour)
        self.alert_timestamps = [ts for ts in self.alert_timestamps
                               if (current_time - ts).total_seconds() < 3600]

        # Check if we can send more alerts
        return len(self.alert_timestamps) < max_alerts
    
    def _is_duplicate_alert(self, opportunity: Dict) -> bool:
        """Check if this opportunity was already alerted recently."""
        symbol = opportunity['symbol']
        grade = opportunity['grade']
        
        # Check if same symbol/grade was alerted in last scan
        for alert in self.last_alerts:
            if alert.symbol == symbol and alert.grade == grade:
                return True
        return False
    
    def _create_enhanced_alert(self, opportunity: Dict) -> TTMAlert:
        """Create enhanced TTM alert with additional data."""
        missing_criteria = [name for name, passed in opportunity.get('criteria_details', {}).items()
                          if not passed]

        # Calculate additional metrics
        entry = opportunity.get('entry_price', 0)
        stop = opportunity.get('stop_loss', 0)
        target = opportunity.get('target_price', 0)

        return TTMAlert(
            symbol=opportunity['symbol'],
            grade=opportunity['grade'],
            criteria_count=opportunity.get('criteria_count', 0),
            confidence=opportunity.get('confidence', 0),
            price=opportunity.get('price', 0),
            timeframe=opportunity.get('timeframe', '15min'),
            timestamp=datetime.now(),
            entry_price=entry,
            stop_loss=stop,
            target_price=target,
            missing_criteria=missing_criteria,
            volume=opportunity.get('volume', 0),
            market_cap=opportunity.get('market_cap', 0),
            sector=opportunity.get('sector', ''),
            breakout_probability=opportunity.get('breakout_probability', 0)
        )

    def _send_enhanced_alerts(self, alerts: List[TTMAlert]):
        """Send enhanced alerts using notification manager."""
        for alert in alerts:
            # Add to rate limiting tracker
            self.alert_timestamps.append(alert.timestamp)

            # Send via notification manager
            self.notification_manager.send_notification(alert)

            # Console output for logging
            alert_msg = self._format_enhanced_alert(alert)
            print(alert_msg)
            info(f"🚨 TTM Alert sent: {alert.symbol} Grade {alert.grade}")

    def _format_enhanced_alert(self, alert: TTMAlert) -> str:
        """Format enhanced alert message with comprehensive details."""
        criteria_status = "✅" if alert.criteria_count == 5 else f"{alert.criteria_count}/5"

        msg = f"""
🚨 TTM SQUEEZE ALERT: {alert.symbol} - Grade {alert.grade}

📊 **Setup Quality:**
• Criteria Met: {criteria_status}
• Confidence: {alert.confidence*100:.0f}%
• Breakout Probability: {alert.breakout_probability*100:.0f}%

💰 **Current Price:** ${alert.price:.2f}
📈 **Volume:** {alert.volume:,.0f}
⏰ **Timeframe:** {alert.timeframe}

🎯 **Trade Setup:**
• Entry: ${alert.entry_price:.2f}
• Stop Loss: ${alert.stop_loss:.2f} ({((alert.stop_loss - alert.entry_price) / alert.entry_price * 100):+.1f}%)
• Target: ${alert.target_price:.2f} ({((alert.target_price - alert.entry_price) / alert.entry_price * 100):+.1f}%)
• Risk/Reward: {alert.risk_reward_ratio:.1f}:1"""

        if alert.missing_criteria:
            msg += f"\n\n⚠️ **Missing Criteria:** {', '.join(alert.missing_criteria)}"

        if alert.sector:
            msg += f"\n🏢 **Sector:** {alert.sector}"

        msg += f"\n\n⏰ **Alert Time:** {alert.timestamp.strftime('%Y-%m-%d %H:%M:%S')}"
        msg += f"\n🆔 **Alert ID:** {alert.alert_id}"

        return msg
    
    def _format_alert(self, alert: TTMAlert) -> str:
        """Format alert message."""
        criteria_status = "✅" if alert.criteria_count == 5 else f"{alert.criteria_count}/5"
        
        msg = f"""🚨 TTM ALERT: {alert.symbol} - Grade {alert.grade}

📊 **Setup Details:**
• Criteria Met: {criteria_status}
• Confidence: {alert.confidence*100:.0f}%
• Price: ${alert.price:.2f}
• Timeframe: {alert.timeframe}

💰 **Trade Setup:**
• Entry: ${alert.entry_price:.2f}
• Stop Loss: ${alert.stop_loss:.2f}
• Target: ${alert.target_price:.2f}
• Risk/Reward: {((alert.target_price - alert.entry_price) / (alert.entry_price - alert.stop_loss)):.1f}:1"""

        if alert.missing_criteria:
            msg += f"\n\n⚠️ **Missing:** {', '.join(alert.missing_criteria)}"
        
        msg += f"\n\n⏰ **Time:** {alert.timestamp.strftime('%H:%M:%S')}"
        
        return msg

    def update_filters(self, new_filters: Dict[str, Any]) -> str:
        """Update filtering criteria."""
        old_filters = self.filters.copy()
        self.filters.update(new_filters)

        changes = []
        for key, value in new_filters.items():
            if key in old_filters and old_filters[key] != value:
                changes.append(f"• {key}: {old_filters[key]} → {value}")
            elif key not in old_filters:
                changes.append(f"• {key}: {value} (new)")

        if changes:
            info(f"Updated TTM alert filters: {len(changes)} changes")
            return f"✅ Updated TTM Alert Filters:\n" + "\n".join(changes)
        else:
            return "ℹ️ No filter changes applied"

    def get_statistics(self) -> Dict[str, Any]:
        """Get comprehensive monitoring statistics."""
        uptime = (datetime.now() - self.stats["last_scan_time"]).total_seconds() if self.stats["last_scan_time"] else 0

        return {
            **self.stats,
            "uptime_seconds": uptime,
            "alerts_per_hour": len([ts for ts in self.alert_timestamps
                                  if (datetime.now() - ts).total_seconds() < 3600]),
            "success_rate": ((self.stats["total_scans"] - self.stats["scan_errors"]) /
                           max(1, self.stats["total_scans"])) * 100,
            "filter_efficiency": (self.stats["alerts_filtered"] /
                                max(1, self.stats["alerts_filtered"] + self.stats["alerts_sent"])) * 100,
            "is_market_time": self._is_market_time(),
            "current_filters": self.filters.copy()
        }


# Global alert system instance
_alert_system = TTMAlertSystem()


def get_alert_system() -> TTMAlertSystem:
    """Get the global alert system instance."""
    return _alert_system


def start_ttm_alerts(min_grade: str = "A", scan_interval: int = 120,
                    custom_filters: Dict[str, Any] = None) -> str:
    """Start enhanced TTM alert monitoring."""
    return _alert_system.start_monitoring(min_grade, scan_interval, custom_filters)


def stop_ttm_alerts() -> str:
    """Stop TTM alert monitoring."""
    return _alert_system.stop_monitoring()


def get_alert_status() -> Dict[str, Any]:
    """Get enhanced alert system status."""
    return _alert_system.get_status()


def get_recent_ttm_alerts(limit: int = 10) -> List[TTMAlert]:
    """Get recent TTM alerts."""
    return _alert_system.get_recent_alerts(limit)


def update_alert_filters(filters: Dict[str, Any]) -> str:
    """Update TTM alert filtering criteria."""
    return _alert_system.update_filters(filters)


def get_alert_statistics() -> Dict[str, Any]:
    """Get comprehensive alert system statistics."""
    return _alert_system.get_statistics()


def configure_notifications(config: Dict[str, Any]) -> str:
    """Configure notification settings."""
    try:
        _alert_system.notification_manager.config.update(config)
        return "✅ Notification settings updated successfully"
    except Exception as e:
        return f"❌ Failed to update notification settings: {e}"
