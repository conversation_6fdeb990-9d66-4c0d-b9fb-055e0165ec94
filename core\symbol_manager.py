#!/usr/bin/env python3
"""
Symbol Manager - Automated management of trading symbols
Handles S&P 500 constituents, $100B+ market cap stocks, and priority symbols
"""

import requests
import pandas as pd
import json
import os
import time
from datetime import datetime, timedelta
from typing import List, Dict, Set, Optional, Any
import sqlite3

try:
    from core.logger_util import info, warning, error
except ImportError:
    def info(msg): print(f"ℹ️ {msg}")
    def warning(msg): print(f"⚠️ {msg}")
    def error(msg): print(f"❌ {msg}")

try:
    from config import get_api_key
    FMP_API_KEY = get_api_key('FMP_API_KEY')
except ImportError:
    FMP_API_KEY = os.getenv('FMP_API_KEY', 'demo')


class SymbolManager:
    """Comprehensive symbol management system."""
    
    def __init__(self, db_path: str = "data/symbols.db"):
        self.db_path = db_path
        self.api_key = FMP_API_KEY
        self._init_database()
        
        # Priority symbols (always included)
        self.priority_symbols = ['PLTR', 'AAPL']
        
        # Market cap threshold for inclusion ($100B)
        self.min_market_cap = 100_000_000_000
        
        # Cache for API responses
        self._cache = {}
        self._cache_expiry = {}
        
    def _init_database(self):
        """Initialize the symbols database."""
        try:
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Symbols table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS symbols (
                        symbol TEXT PRIMARY KEY,
                        name TEXT,
                        sector TEXT,
                        industry TEXT,
                        market_cap REAL,
                        price REAL,
                        is_sp500 BOOLEAN DEFAULT 0,
                        is_large_cap BOOLEAN DEFAULT 0,
                        is_priority BOOLEAN DEFAULT 0,
                        is_active BOOLEAN DEFAULT 1,
                        last_updated TEXT,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Symbol lists table for tracking different lists
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS symbol_lists (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        list_name TEXT NOT NULL,
                        symbol TEXT NOT NULL,
                        added_date TEXT DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(list_name, symbol)
                    )
                ''')
                
                # Create indexes
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_market_cap ON symbols(market_cap)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_sp500 ON symbols(is_sp500)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_active ON symbols(is_active)')
                
                conn.commit()
                info("✅ Symbol Manager database initialized")
                
        except Exception as e:
            error(f"Failed to initialize Symbol Manager database: {e}")
            raise
    
    def fetch_sp500_symbols(self) -> List[str]:
        """Fetch current S&P 500 constituents."""
        try:
            info("📊 Fetching S&P 500 constituents...")
            
            # Try multiple sources for S&P 500 data
            symbols = []
            
            # Method 1: Wikipedia (most reliable)
            try:
                url = 'https://en.wikipedia.org/wiki/List_of_S%26P_500_companies'
                tables = pd.read_html(url)
                sp500_table = tables[0]
                symbols = sp500_table['Symbol'].tolist()
                
                # Clean symbols (handle special characters)
                cleaned_symbols = []
                for symbol in symbols:
                    # Replace dots with dashes for API compatibility
                    cleaned_symbol = symbol.replace('.', '-')
                    cleaned_symbols.append(cleaned_symbol)
                
                info(f"✅ Retrieved {len(cleaned_symbols)} S&P 500 symbols from Wikipedia")
                return cleaned_symbols
                
            except Exception as e:
                warning(f"Wikipedia method failed: {e}")
            
            # Method 2: FMP API
            try:
                url = f"https://financialmodelingprep.com/api/v3/sp500_constituent"
                params = {"apikey": self.api_key}
                
                response = requests.get(url, params=params, timeout=30)
                if response.status_code == 200:
                    data = response.json()
                    symbols = [item['symbol'] for item in data if 'symbol' in item]
                    info(f"✅ Retrieved {len(symbols)} S&P 500 symbols from FMP API")
                    return symbols
                    
            except Exception as e:
                warning(f"FMP API method failed: {e}")
            
            # Method 3: Fallback to hardcoded list
            warning("Using fallback S&P 500 list")
            return self._get_fallback_sp500_symbols()
            
        except Exception as e:
            error(f"Failed to fetch S&P 500 symbols: {e}")
            return self._get_fallback_sp500_symbols()
    
    def fetch_large_cap_symbols(self, min_market_cap: float = None) -> List[str]:
        """Fetch symbols with market cap above threshold."""
        try:
            if min_market_cap is None:
                min_market_cap = self.min_market_cap
            
            info(f"💰 Fetching stocks with market cap > ${min_market_cap/1e9:.0f}B...")
            
            # Use FMP API to get market cap data
            url = f"https://financialmodelingprep.com/api/v3/market-capitalization"
            params = {"apikey": self.api_key, "limit": 1000}
            
            response = requests.get(url, params=params, timeout=60)
            
            if response.status_code == 200:
                data = response.json()
                large_cap_symbols = []
                
                for item in data:
                    market_cap = item.get('marketCap', 0)
                    symbol = item.get('symbol', '')
                    
                    if market_cap >= min_market_cap and symbol:
                        large_cap_symbols.append(symbol)
                
                info(f"✅ Found {len(large_cap_symbols)} stocks with market cap > ${min_market_cap/1e9:.0f}B")
                return large_cap_symbols
            else:
                warning(f"Failed to fetch market cap data: {response.status_code}")
                return []
                
        except Exception as e:
            error(f"Failed to fetch large cap symbols: {e}")
            return []
    
    def get_symbol_details(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get detailed information for a symbol."""
        try:
            # Check cache first
            cache_key = f"details_{symbol}"
            if cache_key in self._cache and cache_key in self._cache_expiry:
                if datetime.now() < self._cache_expiry[cache_key]:
                    return self._cache[cache_key]
            
            # Fetch from API
            url = f"https://financialmodelingprep.com/api/v3/profile/{symbol}"
            params = {"apikey": self.api_key}
            
            response = requests.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data and len(data) > 0:
                    details = data[0]
                    
                    # Cache the result for 1 hour
                    self._cache[cache_key] = details
                    self._cache_expiry[cache_key] = datetime.now() + timedelta(hours=1)
                    
                    return details
            
            return None
            
        except Exception as e:
            warning(f"Failed to get details for {symbol}: {e}")
            return None
    
    def update_symbol_database(self) -> Dict[str, int]:
        """Update the symbol database with latest data."""
        try:
            info("🔄 Updating symbol database...")
            
            # Fetch all symbol lists
            sp500_symbols = self.fetch_sp500_symbols()
            large_cap_symbols = self.fetch_large_cap_symbols()
            
            # Combine all symbols
            all_symbols = set(sp500_symbols + large_cap_symbols + self.priority_symbols)
            
            stats = {
                "total_symbols": len(all_symbols),
                "sp500_symbols": len(sp500_symbols),
                "large_cap_symbols": len(large_cap_symbols),
                "priority_symbols": len(self.priority_symbols),
                "updated_symbols": 0,
                "new_symbols": 0,
                "errors": 0
            }
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                for symbol in all_symbols:
                    try:
                        # Get symbol details
                        details = self.get_symbol_details(symbol)
                        
                        if details:
                            # Extract relevant information
                            name = details.get('companyName', '')
                            sector = details.get('sector', '')
                            industry = details.get('industry', '')
                            market_cap = details.get('mktCap', 0)
                            price = details.get('price', 0)
                            
                            # Determine flags
                            is_sp500 = symbol in sp500_symbols
                            is_large_cap = symbol in large_cap_symbols
                            is_priority = symbol in self.priority_symbols
                            
                            # Insert or update symbol
                            cursor.execute('''
                                INSERT OR REPLACE INTO symbols (
                                    symbol, name, sector, industry, market_cap, price,
                                    is_sp500, is_large_cap, is_priority, is_active, last_updated
                                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ''', (
                                symbol, name, sector, industry, market_cap, price,
                                is_sp500, is_large_cap, is_priority, True,
                                datetime.now().isoformat()
                            ))
                            
                            # Check if this was a new symbol
                            if cursor.rowcount == 1:
                                stats["new_symbols"] += 1
                            else:
                                stats["updated_symbols"] += 1
                        
                        else:
                            # Symbol without details, add basic entry
                            is_sp500 = symbol in sp500_symbols
                            is_large_cap = symbol in large_cap_symbols
                            is_priority = symbol in self.priority_symbols
                            
                            cursor.execute('''
                                INSERT OR IGNORE INTO symbols (
                                    symbol, is_sp500, is_large_cap, is_priority, 
                                    is_active, last_updated
                                ) VALUES (?, ?, ?, ?, ?, ?)
                            ''', (
                                symbol, is_sp500, is_large_cap, is_priority,
                                True, datetime.now().isoformat()
                            ))
                            
                            if cursor.rowcount == 1:
                                stats["new_symbols"] += 1
                        
                        # Small delay to avoid rate limiting
                        time.sleep(0.1)
                        
                    except Exception as e:
                        warning(f"Error updating {symbol}: {e}")
                        stats["errors"] += 1
                        continue
                
                conn.commit()
            
            info(f"✅ Symbol database updated: {stats}")
            return stats
            
        except Exception as e:
            error(f"Failed to update symbol database: {e}")
            return {"error": str(e)}
    
    def get_active_symbols(self, include_sp500: bool = True, include_large_cap: bool = True,
                          include_priority: bool = True, min_market_cap: float = None) -> List[str]:
        """Get list of active symbols based on criteria."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                conditions = ["is_active = 1"]
                params = []
                
                if include_sp500 and include_large_cap and include_priority:
                    conditions.append("(is_sp500 = 1 OR is_large_cap = 1 OR is_priority = 1)")
                else:
                    sub_conditions = []
                    if include_sp500:
                        sub_conditions.append("is_sp500 = 1")
                    if include_large_cap:
                        sub_conditions.append("is_large_cap = 1")
                    if include_priority:
                        sub_conditions.append("is_priority = 1")
                    
                    if sub_conditions:
                        conditions.append(f"({' OR '.join(sub_conditions)})")
                
                if min_market_cap:
                    conditions.append("market_cap >= ?")
                    params.append(min_market_cap)
                
                query = f"SELECT symbol FROM symbols WHERE {' AND '.join(conditions)} ORDER BY symbol"
                
                cursor.execute(query, params)
                symbols = [row[0] for row in cursor.fetchall()]
                
                info(f"📊 Retrieved {len(symbols)} active symbols")
                return symbols
                
        except Exception as e:
            error(f"Failed to get active symbols: {e}")
            return []
    
    def get_symbol_statistics(self) -> Dict[str, Any]:
        """Get comprehensive symbol statistics."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Total counts
                cursor.execute("SELECT COUNT(*) FROM symbols WHERE is_active = 1")
                total_active = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM symbols WHERE is_sp500 = 1 AND is_active = 1")
                sp500_count = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM symbols WHERE is_large_cap = 1 AND is_active = 1")
                large_cap_count = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM symbols WHERE is_priority = 1 AND is_active = 1")
                priority_count = cursor.fetchone()[0]
                
                # Sector distribution
                cursor.execute("""
                    SELECT sector, COUNT(*) FROM symbols 
                    WHERE is_active = 1 AND sector != '' 
                    GROUP BY sector ORDER BY COUNT(*) DESC LIMIT 10
                """)
                sector_dist = dict(cursor.fetchall())
                
                # Market cap ranges
                cursor.execute("""
                    SELECT 
                        SUM(CASE WHEN market_cap >= 1000000000000 THEN 1 ELSE 0 END) as trillion_plus,
                        SUM(CASE WHEN market_cap >= 100000000000 AND market_cap < 1000000000000 THEN 1 ELSE 0 END) as hundred_bil_plus,
                        SUM(CASE WHEN market_cap >= 10000000000 AND market_cap < 100000000000 THEN 1 ELSE 0 END) as ten_bil_plus,
                        SUM(CASE WHEN market_cap > 0 AND market_cap < 10000000000 THEN 1 ELSE 0 END) as under_ten_bil
                    FROM symbols WHERE is_active = 1
                """)
                market_cap_ranges = cursor.fetchone()
                
                return {
                    "total_active_symbols": total_active,
                    "sp500_symbols": sp500_count,
                    "large_cap_symbols": large_cap_count,
                    "priority_symbols": priority_count,
                    "sector_distribution": sector_dist,
                    "market_cap_ranges": {
                        "trillion_plus": market_cap_ranges[0],
                        "hundred_billion_plus": market_cap_ranges[1],
                        "ten_billion_plus": market_cap_ranges[2],
                        "under_ten_billion": market_cap_ranges[3]
                    },
                    "last_updated": datetime.now().isoformat()
                }
                
        except Exception as e:
            error(f"Failed to get symbol statistics: {e}")
            return {}
    
    def _get_fallback_sp500_symbols(self) -> List[str]:
        """Fallback S&P 500 symbol list."""
        return [
            # Technology
            'AAPL', 'MSFT', 'GOOGL', 'GOOG', 'AMZN', 'NVDA', 'META', 'TSLA', 'AVGO', 'ORCL',
            'CRM', 'ADBE', 'NFLX', 'AMD', 'INTC', 'CSCO', 'ACN', 'TXN', 'QCOM', 'IBM',
            'INTU', 'AMAT', 'MU', 'ADI', 'LRCX', 'KLAC', 'MCHP', 'CDNS', 'SNPS', 'FTNT',
            
            # Healthcare
            'UNH', 'JNJ', 'PFE', 'ABBV', 'TMO', 'ABT', 'DHR', 'MRK', 'BMY', 'AMGN',
            'GILD', 'VRTX', 'REGN', 'ISRG', 'ZTS', 'CVS', 'CI', 'HUM', 'ANTM', 'MOH',
            
            # Financials
            'JPM', 'BAC', 'WFC', 'GS', 'MS', 'C', 'BLK', 'SPGI', 'AXP', 'USB',
            'TFC', 'PNC', 'COF', 'SCHW', 'CB', 'MMC', 'ICE', 'CME', 'AON', 'AJG',
            
            # Consumer Discretionary
            'AMZN', 'TSLA', 'HD', 'MCD', 'NKE', 'LOW', 'SBUX', 'TJX', 'BKNG', 'ORLY',
            'MAR', 'GM', 'F', 'CCL', 'RCL', 'NCLH', 'MGM', 'WYNN', 'LVS', 'CZR',
            
            # Consumer Staples
            'PG', 'KO', 'PEP', 'WMT', 'COST', 'MDLZ', 'CL', 'KMB', 'GIS', 'K',
            'HSY', 'MKC', 'SJM', 'CAG', 'CPB', 'HRL', 'TSN', 'TAP', 'KHC', 'CHD',
            
            # Energy
            'XOM', 'CVX', 'COP', 'EOG', 'SLB', 'PSX', 'VLO', 'MPC', 'OXY', 'BKR',
            'HAL', 'DVN', 'FANG', 'EQT', 'CTRA', 'MRO', 'APA', 'OVV', 'MTDR', 'SM',
            
            # Industrials
            'BA', 'CAT', 'HON', 'UNP', 'RTX', 'LMT', 'DE', 'UPS', 'GE', 'MMM',
            'NOC', 'GD', 'FDX', 'NSC', 'CSX', 'WM', 'EMR', 'ETN', 'ITW', 'PH',
            
            # Materials
            'LIN', 'APD', 'SHW', 'FCX', 'NEM', 'DOW', 'DD', 'PPG', 'ECL', 'IFF',
            'ALB', 'CE', 'FMC', 'VMC', 'MLM', 'PKG', 'AMCR', 'IP', 'AVY', 'SEE',
            
            # Real Estate
            'AMT', 'PLD', 'CCI', 'EQIX', 'PSA', 'WELL', 'DLR', 'O', 'SBAC', 'EXR',
            'AVB', 'EQR', 'VTR', 'ESS', 'MAA', 'KIM', 'REG', 'BXP', 'ARE', 'UDR',
            
            # Utilities
            'NEE', 'SO', 'DUK', 'AEP', 'SRE', 'D', 'PEG', 'EXC', 'XEL', 'ED',
            'WEC', 'AWK', 'DTE', 'ES', 'FE', 'EIX', 'ETR', 'CMS', 'CNP', 'NI',
            
            # Communication Services
            'GOOGL', 'META', 'NFLX', 'DIS', 'VZ', 'T', 'CMCSA', 'CHTR', 'TMUS', 'ATVI',
            
            # Priority additions
            'PLTR'
        ]


# Global symbol manager instance
_symbol_manager = SymbolManager()


def get_symbol_manager() -> SymbolManager:
    """Get the global symbol manager instance."""
    return _symbol_manager


def get_trading_symbols() -> List[str]:
    """Get comprehensive list of trading symbols."""
    return _symbol_manager.get_active_symbols()


def update_symbol_database() -> Dict[str, int]:
    """Update the symbol database."""
    return _symbol_manager.update_symbol_database()


def get_symbol_statistics() -> Dict[str, Any]:
    """Get symbol statistics."""
    return _symbol_manager.get_symbol_statistics()
