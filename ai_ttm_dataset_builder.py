#!/usr/bin/env python3
"""
AI TTM Dataset Builder - Phase 1
Creates training dataset from historical stock data with TTM features and win/loss labels
"""

import pandas as pd
import numpy as np
import talib
from datetime import datetime, timedelta
import os
import requests
import warnings
warnings.filterwarnings('ignore')

# Use existing FMP infrastructure
try:
    from core.config import get_api_key
except ImportError:
    try:
        from config import get_api_key
    except ImportError:
        def get_api_key(key_name):
            import os
            key = os.getenv(key_name)
            if key is None:
                raise KeyError(f"Environment variable '{key_name}' is not set.")
            return key

class TTMDatasetBuilder:
    """Builds ML training dataset for TTM squeeze setups."""
    
    def __init__(self, lookforward_candles=6, win_threshold=0.005):
        self.lookforward_candles = lookforward_candles  # How many candles to look ahead (6 hours)
        self.win_threshold = win_threshold  # 0.5% minimum gain to be a "WIN" (professional day trading standard)
        
    def get_stock_data(self, symbol, period="2y", interval="1h"):
        """Download historical data using FMP API."""
        try:
            api_key = get_api_key("FMP_API_KEY")
            if not api_key:
                print(f"❌ FMP API key not found")
                return None

            # Convert interval to FMP format
            if interval == "1h":
                fmp_interval = "1hour"
            elif interval == "15m":
                fmp_interval = "15min"
            elif interval == "5m":
                fmp_interval = "5min"
            else:
                fmp_interval = "1hour"  # Default

            # FMP historical data endpoint
            url = f"https://financialmodelingprep.com/api/v3/historical-chart/{fmp_interval}/{symbol}"
            params = {"apikey": api_key}

            response = requests.get(url, params=params)
            if response.status_code != 200:
                print(f"❌ FMP API error for {symbol}: {response.status_code}")
                return None

            data = response.json()
            if not data or len(data) < 100:
                print(f"❌ Insufficient data for {symbol}: {len(data) if data else 0} bars")
                return None

            # Convert to DataFrame with proper column names
            df = pd.DataFrame(data)
            df['date'] = pd.to_datetime(df['date'])
            df.set_index('date', inplace=True)
            df.sort_index(inplace=True)

            # Rename columns to match yfinance format
            df.rename(columns={
                'open': 'Open',
                'high': 'High',
                'low': 'Low',
                'close': 'Close',
                'volume': 'Volume'
            }, inplace=True)

            # Get last 2 years of data
            cutoff_date = datetime.now() - timedelta(days=730)
            df = df[df.index >= cutoff_date]

            if len(df) < 100:
                print(f"❌ Insufficient recent data for {symbol}: {len(df)} bars")
                return None

            return df

        except Exception as e:
            print(f"❌ Error downloading {symbol}: {e}")
            return None
    
    def calculate_ttm_features(self, df):
        """Calculate all TTM-related features."""
        # Bollinger Bands
        df['bb_upper'], df['bb_middle'], df['bb_lower'] = talib.BBANDS(
            df['Close'], timeperiod=20, nbdevup=2, nbdevdn=2
        )
        
        # Keltner Channels
        df['ema20'] = talib.EMA(df['Close'], timeperiod=20)
        df['atr'] = talib.ATR(df['High'], df['Low'], df['Close'], timeperiod=20)
        df['kc_upper'] = df['ema20'] + (df['atr'] * 1.5)
        df['kc_lower'] = df['ema20'] - (df['atr'] * 1.5)
        
        # TTM Squeeze Detection
        df['ttm_squeeze_active'] = (
            (df['bb_upper'] <= df['kc_upper']) & 
            (df['bb_lower'] >= df['kc_lower'])
        ).astype(int)
        
        # MACD and Histogram
        df['macd'], df['macd_signal'], df['macd_histogram'] = talib.MACD(
            df['Close'], fastperiod=12, slowperiod=26, signalperiod=9
        )
        
        # Custom Momentum Histogram (Linear Regression of Close vs EMA20)
        df['momentum_histogram'] = df['Close'] - df['ema20']
        
        # EMAs
        df['ema5'] = talib.EMA(df['Close'], timeperiod=5)
        df['ema8'] = talib.EMA(df['Close'], timeperiod=8)
        df['ema21'] = talib.EMA(df['Close'], timeperiod=21)
        
        # EMA Crossover
        df['ema5_above_ema8'] = (df['ema5'] > df['ema8']).astype(int)
        df['ema8_above_ema21'] = (df['ema8'] > df['ema21']).astype(int)
        
        # Price above EMAs
        df['price_above_ema8'] = (df['Close'] > df['ema8']).astype(int)
        df['price_above_ema21'] = (df['Close'] > df['ema21']).astype(int)
        
        # Volume features
        df['volume_sma20'] = df['Volume'].rolling(20).mean()
        df['volume_ratio'] = df['Volume'] / df['volume_sma20']
        df['high_volume'] = (df['volume_ratio'] > 1.5).astype(int)
        
        # Candle patterns
        df['green_candle'] = (df['Close'] > df['Open']).astype(int)
        df['last_3_candles_green'] = (
            df['green_candle'].rolling(3).sum() == 3
        ).astype(int)
        
        # Volatility features
        df['true_range'] = talib.TRANGE(df['High'], df['Low'], df['Close'])
        df['volatility_ratio'] = df['true_range'] / df['Close']
        
        # Momentum features
        df['rsi'] = talib.RSI(df['Close'], timeperiod=14)
        df['momentum_rising'] = (
            df['momentum_histogram'] > df['momentum_histogram'].shift(1)
        ).astype(int)
        
        # Histogram pattern (3 rising bars)
        df['histogram_rising_3'] = (
            (df['momentum_histogram'] > df['momentum_histogram'].shift(1)) &
            (df['momentum_histogram'].shift(1) > df['momentum_histogram'].shift(2)) &
            (df['momentum_histogram'].shift(2) > df['momentum_histogram'].shift(3))
        ).astype(int)
        
        return df
    
    def calculate_future_returns(self, df):
        """Calculate future returns for labeling."""
        # Calculate max return in next N candles
        future_highs = df['High'].shift(-1).rolling(self.lookforward_candles).max()
        future_returns = (future_highs - df['Close']) / df['Close']
        
        # Label as WIN if return >= threshold
        df['future_return'] = future_returns
        df['label'] = (future_returns >= self.win_threshold).astype(int)
        
        return df
    
    def extract_features_and_labels(self, df, symbol):
        """Extract feature rows for ML training."""
        # Define feature columns
        feature_cols = [
            'ttm_squeeze_active',
            'momentum_histogram',
            'ema5_above_ema8',
            'ema8_above_ema21',
            'price_above_ema8',
            'price_above_ema21',
            'last_3_candles_green',
            'high_volume',
            'volume_ratio',
            'volatility_ratio',
            'rsi',
            'momentum_rising',
            'histogram_rising_3',
            'macd',
            'macd_histogram',
            'atr'
        ]
        
        # Create dataset
        dataset = df[feature_cols + ['label', 'future_return']].copy()
        dataset['symbol'] = symbol
        dataset['timestamp'] = df.index
        
        # Remove rows with NaN values
        dataset = dataset.dropna()
        
        return dataset
    
    def build_dataset(self, symbols, save_path="ttm_training_dataset.csv"):
        """Build complete dataset from multiple symbols."""
        print(f"🚀 Building TTM ML Dataset from {len(symbols)} symbols...")
        
        all_data = []
        successful_symbols = 0
        
        for i, symbol in enumerate(symbols):
            print(f"📊 Processing {symbol} ({i+1}/{len(symbols)})...")
            
            # Download data
            df = self.get_stock_data(symbol, period="2y", interval="1h")
            if df is None:
                continue
            
            # Calculate features
            df = self.calculate_ttm_features(df)
            
            # Calculate labels
            df = self.calculate_future_returns(df)
            
            # Extract training data
            symbol_data = self.extract_features_and_labels(df, symbol)
            
            if len(symbol_data) > 0:
                all_data.append(symbol_data)
                successful_symbols += 1
                print(f"✅ {symbol}: {len(symbol_data)} samples")
            else:
                print(f"❌ {symbol}: No valid samples")
        
        # Combine all data
        if all_data:
            final_dataset = pd.concat(all_data, ignore_index=True)
            
            # Save to CSV
            final_dataset.to_csv(save_path, index=False)
            
            # Print statistics
            total_samples = len(final_dataset)
            win_samples = final_dataset['label'].sum()
            win_rate = win_samples / total_samples * 100
            
            print(f"\n🎯 DATASET COMPLETE!")
            print(f"📊 Total Samples: {total_samples:,}")
            print(f"🏆 WIN Samples: {win_samples:,} ({win_rate:.1f}%)")
            print(f"📉 LOSS Samples: {total_samples - win_samples:,} ({100-win_rate:.1f}%)")
            print(f"💾 Saved to: {save_path}")
            print(f"✅ Successful symbols: {successful_symbols}/{len(symbols)}")
            
            return final_dataset
        else:
            print("❌ No data collected!")
            return None


def get_sp500_symbols():
    """Get S&P 500 symbols from Wikipedia."""
    try:
        import pandas as pd
        # Get S&P 500 list from Wikipedia
        url = 'https://en.wikipedia.org/wiki/List_of_S%26P_500_companies'
        tables = pd.read_html(url)
        sp500_table = tables[0]
        symbols = sp500_table['Symbol'].tolist()

        # Clean symbols (remove dots, etc.)
        cleaned_symbols = []
        for symbol in symbols:
            # Replace dots with dashes for Yahoo/FMP compatibility
            cleaned_symbol = symbol.replace('.', '-')
            cleaned_symbols.append(cleaned_symbol)

        print(f"📊 Retrieved {len(cleaned_symbols)} S&P 500 symbols")
        return cleaned_symbols

    except Exception as e:
        print(f"❌ Error fetching S&P 500 list: {e}")
        print("🔄 Falling back to manual list...")

        # Fallback to comprehensive manual list
        return [
            # Technology
            'AAPL', 'MSFT', 'GOOGL', 'GOOG', 'AMZN', 'NVDA', 'TSLA', 'META', 'ORCL', 'ADBE',
            'CRM', 'NFLX', 'INTC', 'AMD', 'CSCO', 'AVGO', 'TXN', 'QCOM', 'AMAT', 'ADI',
            'LRCX', 'KLAC', 'MCHP', 'SNPS', 'CDNS', 'FTNT', 'PANW', 'CRWD', 'ZS', 'OKTA',

            # Healthcare
            'UNH', 'JNJ', 'PFE', 'ABBV', 'LLY', 'TMO', 'ABT', 'MRK', 'DHR', 'BMY',
            'AMGN', 'GILD', 'VRTX', 'REGN', 'ISRG', 'ZTS', 'CVS', 'CI', 'HUM', 'ANTM',

            # Financial
            'JPM', 'BAC', 'WFC', 'GS', 'MS', 'C', 'AXP', 'BLK', 'SCHW', 'USB',
            'PNC', 'TFC', 'COF', 'BK', 'STT', 'NTRS', 'RF', 'CFG', 'KEY', 'FITB',

            # Consumer Discretionary
            'AMZN', 'TSLA', 'HD', 'NKE', 'MCD', 'SBUX', 'TJX', 'LOW', 'BKNG', 'DIS',
            'GM', 'F', 'APTV', 'YUM', 'CMG', 'ORLY', 'AZO', 'ULTA', 'RCL', 'CCL',

            # Consumer Staples
            'WMT', 'PG', 'KO', 'PEP', 'COST', 'WBA', 'CL', 'KMB', 'GIS', 'K',
            'HSY', 'MKC', 'SJM', 'CAG', 'CPB', 'CHD', 'CLX', 'TSN', 'HRL', 'LW',

            # Energy
            'XOM', 'CVX', 'COP', 'EOG', 'SLB', 'PSX', 'VLO', 'MPC', 'OXY', 'BKR',
            'HAL', 'DVN', 'FANG', 'EQT', 'CTRA', 'MRO', 'APA', 'HES', 'KMI', 'OKE',

            # Industrials
            'BA', 'CAT', 'HON', 'UPS', 'RTX', 'LMT', 'GE', 'MMM', 'FDX', 'NOC',
            'GD', 'DE', 'EMR', 'ETN', 'ITW', 'PH', 'CMI', 'ROK', 'DOV', 'XYL',

            # Materials
            'LIN', 'APD', 'SHW', 'FCX', 'NEM', 'ECL', 'DD', 'DOW', 'PPG', 'IFF',
            'LYB', 'ALB', 'CE', 'VMC', 'MLM', 'PKG', 'AMCR', 'IP', 'CF', 'FMC',

            # Utilities
            'NEE', 'DUK', 'SO', 'D', 'AEP', 'EXC', 'XEL', 'SRE', 'PEG', 'ED',
            'EIX', 'WEC', 'AWK', 'DTE', 'ES', 'FE', 'AEE', 'CMS', 'CNP', 'NI',

            # Real Estate
            'AMT', 'PLD', 'CCI', 'EQIX', 'PSA', 'WELL', 'DLR', 'O', 'SBAC', 'EXR',
            'AVB', 'EQR', 'VTR', 'ESS', 'MAA', 'UDR', 'CPT', 'FRT', 'BXP', 'REG',

            # Communication Services
            'GOOGL', 'META', 'NFLX', 'DIS', 'VZ', 'T', 'CMCSA', 'CHTR', 'TMUS', 'ATVI'
        ]


def main():
    """Main execution function."""
    print("🚀 S&P 500 AI TTM DATASET BUILDER")
    print("=" * 60)

    # Get S&P 500 symbols
    symbols = get_sp500_symbols()
    
    # Build dataset with professional day trading standards
    builder = TTMDatasetBuilder(lookforward_candles=6, win_threshold=0.005)
    dataset = builder.build_dataset(symbols)
    
    if dataset is not None:
        print(f"\n🚀 Ready for Phase 2 - Model Training!")
        print(f"📁 Dataset file: ttm_training_dataset.csv")
        print(f"🎯 Next: Run the model training script")


if __name__ == "__main__":
    main()
