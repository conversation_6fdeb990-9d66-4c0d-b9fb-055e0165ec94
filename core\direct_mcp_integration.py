#!/usr/bin/env python3
"""
Direct MCP Integration for TotalRecall Chat System

This module integrates alpaca-mcp-server capabilities directly into the existing
TotalRecall chat system, adding the MCP functions as native tools in the TOOLS registry.
"""

import os
import sys
import asyncio
import json
from pathlib import Path
from typing import Dict, Any, List, Optional

# Import existing system components
try:
    from core.chat_core import TOOLS
    from core.logger_util import info, warning, error
    CORE_AVAILABLE = True
except ImportError:
    CORE_AVAILABLE = False
    TOOLS = {}
    def info(msg): print(f"ℹ️ {msg}")
    def warning(msg): print(f"⚠️ {msg}")
    def error(msg): print(f"❌ {msg}")

class DirectMCPIntegration:
    """Direct integration of MCP server capabilities into TotalRecall chat."""
    
    def __init__(self):
        self.mcp_server_path = None
        self.mcp_functions = {}
        self.is_integrated = False
        
        # Locate and load MCP server
        self._locate_mcp_server()
        self._setup_environment()
        self._load_mcp_functions()
    
    def _locate_mcp_server(self):
        """Locate the alpaca-mcp-server."""
        # Check integrations directory first
        integrations_path = Path("integrations") / "alpaca-mcp-server"
        if integrations_path.exists() and (integrations_path / "alpaca_mcp_server.py").exists():
            self.mcp_server_path = integrations_path
            info(f"📍 Found MCP server at: {self.mcp_server_path}")
            return
        
        # Check Downloads directory
        downloads_path = Path.home() / "Downloads" / "alpaca-mcp-server-main"
        if downloads_path.exists() and (downloads_path / "alpaca_mcp_server.py").exists():
            self.mcp_server_path = downloads_path
            info(f"📍 Found MCP server at: {self.mcp_server_path}")
            return
        
        warning("⚠️ MCP server not found - enhanced features will not be available")

    def _setup_environment(self):
        """Setup environment variables for MCP server."""
        if not self.mcp_server_path:
            return

        try:
            import os

            # Load environment from .env file
            env_file = self.mcp_server_path / ".env"
            if env_file.exists():
                with open(env_file, 'r') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#') and '=' in line:
                            key, value = line.split('=', 1)
                            key = key.strip()
                            value = value.strip().strip('"').strip("'")
                            os.environ[key] = value

                info("✅ Environment variables loaded from .env file")
            else:
                warning("⚠️ No .env file found - API credentials may not be available")

        except Exception as e:
            error(f"Failed to setup environment: {e}")

    def _load_mcp_functions(self):
        """Load MCP server functions directly."""
        if not self.mcp_server_path:
            return
        
        try:
            # Add MCP server to Python path
            sys.path.insert(0, str(self.mcp_server_path))
            
            # Import MCP server functions
            import alpaca_mcp_server
            
            # Get available MCP functions (only use functions that actually exist)
            available_functions = {}

            # Check each function before adding it - ALL 26 MCP tools
            function_map = {
                # Account & Portfolio Management
                'get_account_info': 'get_account_info',
                'get_positions': 'get_positions',
                'get_open_position': 'get_open_position',

                # Stock Data & Analysis
                'get_stock_quote': 'get_stock_quote',
                'get_stock_bars': 'get_stock_bars',
                'get_stock_trades': 'get_stock_trades',
                'get_stock_latest_trade': 'get_stock_latest_trade',
                'get_stock_latest_bar': 'get_stock_latest_bar',

                # Order Management
                'get_orders': 'get_orders',
                'place_stock_order': 'place_stock_order',
                'cancel_all_orders': 'cancel_all_orders',
                'cancel_order_by_id': 'cancel_order_by_id',

                # Position Management
                'close_position': 'close_position',
                'close_all_positions': 'close_all_positions',

                # Asset Information
                'get_asset_info': 'get_asset_info',
                'get_all_assets': 'get_all_assets',

                # Watchlist Management
                'get_watchlists': 'get_watchlists',
                'create_watchlist': 'create_watchlist',
                'update_watchlist': 'update_watchlist',

                # Market Information
                'get_market_clock': 'get_market_clock',
                'get_market_calendar': 'get_market_calendar',
                'get_corporate_announcements': 'get_corporate_announcements',

                # Options Trading
                'get_option_contracts': 'get_option_contracts',
                'get_option_latest_quote': 'get_option_latest_quote',
                'get_option_snapshot': 'get_option_snapshot',
                'place_option_market_order': 'place_option_market_order',
            }

            for func_name, attr_name in function_map.items():
                if hasattr(alpaca_mcp_server, attr_name):
                    available_functions[func_name] = getattr(alpaca_mcp_server, attr_name)

            self.mcp_functions = available_functions
            
            info(f"✅ Loaded {len(self.mcp_functions)} MCP functions")
            
        except Exception as e:
            error(f"Failed to load MCP functions: {e}")
            self.mcp_functions = {}
    
    def integrate_with_chat_tools(self):
        """Integrate MCP functions directly into the chat TOOLS registry."""
        if not self.mcp_functions:
            warning("No MCP functions available to integrate")
            return False
        
        try:
            # Add ALL MCP-powered tools to existing TOOLS (26 total)
            mcp_tools = {
                # ACCOUNT & PORTFOLIO MANAGEMENT
                "get_account_balance": {
                    "function": self.get_account_balance,
                    "schema": {
                        "name": "get_account_balance",
                        "description": "Get current account balance, buying power, and account status",
                        "parameters": {"type": "object", "properties": {}}
                    }
                },

                "get_current_positions": {
                    "function": self.get_current_positions,
                    "schema": {
                        "name": "get_current_positions",
                        "description": "Get all current stock and options positions with P&L",
                        "parameters": {"type": "object", "properties": {}}
                    }
                },

                "get_specific_position": {
                    "function": self.get_specific_position,
                    "schema": {
                        "name": "get_specific_position",
                        "description": "Get detailed information for a specific position",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "symbol": {"type": "string", "description": "Stock symbol"}
                            },
                            "required": ["symbol"]
                        }
                    }
                },

                # STOCK DATA & ANALYSIS
                "get_enhanced_quote": {
                    "function": self.get_enhanced_quote,
                    "schema": {
                        "name": "get_enhanced_quote",
                        "description": "Get enhanced stock quote with real-time data",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "symbol": {"type": "string", "description": "Stock symbol"}
                            },
                            "required": ["symbol"]
                        }
                    }
                },

                "get_stock_history": {
                    "function": self.get_stock_history,
                    "schema": {
                        "name": "get_stock_history",
                        "description": "Get historical price bars for technical analysis",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "symbol": {"type": "string", "description": "Stock symbol"},
                                "days": {"type": "integer", "default": 5, "description": "Number of days of history"}
                            },
                            "required": ["symbol"]
                        }
                    }
                },

                "get_stock_trades": {
                    "function": self.get_stock_trades,
                    "schema": {
                        "name": "get_stock_trades",
                        "description": "Get recent trade data for volume analysis",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "symbol": {"type": "string", "description": "Stock symbol"},
                                "days": {"type": "integer", "default": 1, "description": "Number of days"}
                            },
                            "required": ["symbol"]
                        }
                    }
                },

                "get_latest_trade": {
                    "function": self.get_latest_trade,
                    "schema": {
                        "name": "get_latest_trade",
                        "description": "Get the most recent trade for a stock",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "symbol": {"type": "string", "description": "Stock symbol"}
                            },
                            "required": ["symbol"]
                        }
                    }
                },

                "get_latest_bar": {
                    "function": self.get_latest_bar,
                    "schema": {
                        "name": "get_latest_bar",
                        "description": "Get the latest price bar (OHLCV) for a stock",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "symbol": {"type": "string", "description": "Stock symbol"}
                            },
                            "required": ["symbol"]
                        }
                    }
                },

                # ORDER MANAGEMENT
                "get_all_orders": {
                    "function": self.get_all_orders,
                    "schema": {
                        "name": "get_all_orders",
                        "description": "Get all orders with status filtering",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "status": {"type": "string", "default": "all", "description": "Order status filter"},
                                "limit": {"type": "integer", "default": 10, "description": "Number of orders to return"}
                            }
                        }
                    }
                },

                "place_enhanced_order": {
                    "function": self.place_enhanced_order,
                    "schema": {
                        "name": "place_enhanced_order",
                        "description": "Place stock order with advanced order types",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "symbol": {"type": "string", "description": "Stock symbol"},
                                "side": {"type": "string", "enum": ["buy", "sell"]},
                                "quantity": {"type": "number", "description": "Number of shares"},
                                "order_type": {"type": "string", "enum": ["market", "limit", "stop", "stop_limit"], "default": "market"},
                                "limit_price": {"type": "number", "description": "Limit price for limit orders"},
                                "stop_price": {"type": "number", "description": "Stop price for stop orders"}
                            },
                            "required": ["symbol", "side", "quantity"]
                        }
                    }
                },

                "cancel_all_orders": {
                    "function": self.cancel_all_orders,
                    "schema": {
                        "name": "cancel_all_orders",
                        "description": "Cancel all open orders",
                        "parameters": {"type": "object", "properties": {}}
                    }
                },

                "cancel_specific_order": {
                    "function": self.cancel_specific_order,
                    "schema": {
                        "name": "cancel_specific_order",
                        "description": "Cancel a specific order by ID",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "order_id": {"type": "string", "description": "Order ID to cancel"}
                            },
                            "required": ["order_id"]
                        }
                    }
                },

                # POSITION MANAGEMENT
                "close_position": {
                    "function": self.close_position,
                    "schema": {
                        "name": "close_position",
                        "description": "Close a specific position",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "symbol": {"type": "string", "description": "Stock symbol"},
                                "quantity": {"type": "string", "description": "Quantity to close (optional)"},
                                "percentage": {"type": "string", "description": "Percentage to close (optional)"}
                            },
                            "required": ["symbol"]
                        }
                    }
                },

                "close_all_positions": {
                    "function": self.close_all_positions,
                    "schema": {
                        "name": "close_all_positions",
                        "description": "Close all open positions",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "cancel_orders": {"type": "boolean", "default": False, "description": "Cancel open orders too"}
                            }
                        }
                    }
                },

                # ASSET INFORMATION
                "get_asset_info": {
                    "function": self.get_asset_info,
                    "schema": {
                        "name": "get_asset_info",
                        "description": "Get detailed asset information",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "symbol": {"type": "string", "description": "Asset symbol"}
                            },
                            "required": ["symbol"]
                        }
                    }
                },

                "search_assets": {
                    "function": self.search_assets,
                    "schema": {
                        "name": "search_assets",
                        "description": "Search and filter available assets",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "status": {"type": "string", "description": "Asset status filter"},
                                "asset_class": {"type": "string", "description": "Asset class filter"},
                                "exchange": {"type": "string", "description": "Exchange filter"}
                            }
                        }
                    }
                },

                # WATCHLIST MANAGEMENT
                "get_watchlists": {
                    "function": self.get_watchlists,
                    "schema": {
                        "name": "get_watchlists",
                        "description": "Get all watchlists",
                        "parameters": {"type": "object", "properties": {}}
                    }
                },

                "create_watchlist": {
                    "function": self.create_watchlist,
                    "schema": {
                        "name": "create_watchlist",
                        "description": "Create a new watchlist",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "name": {"type": "string", "description": "Watchlist name"},
                                "symbols": {"type": "array", "items": {"type": "string"}, "description": "Stock symbols"}
                            },
                            "required": ["name", "symbols"]
                        }
                    }
                },

                "update_watchlist": {
                    "function": self.update_watchlist,
                    "schema": {
                        "name": "update_watchlist",
                        "description": "Update an existing watchlist",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "watchlist_id": {"type": "string", "description": "Watchlist ID"},
                                "name": {"type": "string", "description": "New name (optional)"},
                                "symbols": {"type": "array", "items": {"type": "string"}, "description": "New symbols (optional)"}
                            },
                            "required": ["watchlist_id"]
                        }
                    }
                },

                # MARKET INFORMATION
                "get_market_status": {
                    "function": self.get_market_status,
                    "schema": {
                        "name": "get_market_status",
                        "description": "Get current market status and hours",
                        "parameters": {"type": "object", "properties": {}}
                    }
                },

                "get_market_calendar": {
                    "function": self.get_market_calendar_info,
                    "schema": {
                        "name": "get_market_calendar",
                        "description": "Get market calendar and trading hours",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "days_ahead": {"type": "integer", "default": 7}
                            }
                        }
                    }
                },

                "get_earnings_calendar": {
                    "function": self.get_earnings_calendar,
                    "schema": {
                        "name": "get_earnings_calendar",
                        "description": "Get upcoming earnings announcements",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "symbol": {"type": "string", "description": "Optional symbol filter"},
                                "days_ahead": {"type": "integer", "default": 30}
                            }
                        }
                    }
                },

                # OPTIONS TRADING
                "get_option_contracts": {
                    "function": self.get_option_contracts,
                    "schema": {
                        "name": "get_option_contracts",
                        "description": "Search for option contracts",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "symbol": {"type": "string", "description": "Underlying stock symbol"},
                                "expiration": {"type": "string", "description": "Expiration date (optional)"},
                                "strike_min": {"type": "string", "description": "Minimum strike price (optional)"},
                                "strike_max": {"type": "string", "description": "Maximum strike price (optional)"}
                            },
                            "required": ["symbol"]
                        }
                    }
                },

                "get_option_quote": {
                    "function": self.get_option_quote,
                    "schema": {
                        "name": "get_option_quote",
                        "description": "Get option quote data",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "symbol": {"type": "string", "description": "Option symbol"}
                            },
                            "required": ["symbol"]
                        }
                    }
                },

                "get_options_greeks": {
                    "function": self.get_options_greeks,
                    "schema": {
                        "name": "get_options_greeks",
                        "description": "Get options Greeks (Delta, Gamma, Theta, Vega) for a symbol",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "symbol": {"type": "string", "description": "Option symbol or underlying stock symbol"}
                            },
                            "required": ["symbol"]
                        }
                    }
                },

                "place_option_order": {
                    "function": self.place_option_order,
                    "schema": {
                        "name": "place_option_order",
                        "description": "Place options orders (single leg or multi-leg strategies)",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "legs": {
                                    "type": "array",
                                    "description": "Option legs for the order",
                                    "items": {
                                        "type": "object",
                                        "properties": {
                                            "symbol": {"type": "string", "description": "Option symbol"},
                                            "side": {"type": "string", "description": "buy or sell"},
                                            "quantity": {"type": "integer", "description": "Number of contracts"}
                                        }
                                    }
                                },
                                "quantity": {"type": "integer", "default": 1, "description": "Number of contracts"},
                                "order_class": {"type": "string", "description": "Order class (simple, bracket, etc.)"}
                            },
                            "required": ["legs"]
                        }
                    }
                },

                # ADVANCED ALGORITHMIC TRADING (NEW!)
                "run_momentum_algorithm": {
                    "function": self.run_momentum_algorithm,
                    "schema": {
                        "name": "run_momentum_algorithm",
                        "description": "Run multi-timeframe momentum algorithm on symbols",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "symbols": {"type": "array", "items": {"type": "string"}, "description": "Stock symbols to analyze"},
                                "timeframes": {"type": "string", "default": "5T/1H/1D", "description": "Timeframes to analyze"}
                            },
                            "required": ["symbols"]
                        }
                    }
                },

                "run_mean_reversion_algorithm": {
                    "function": self.run_mean_reversion_algorithm,
                    "schema": {
                        "name": "run_mean_reversion_algorithm",
                        "description": "Run mean reversion algorithm with volatility filtering",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "symbols": {"type": "array", "items": {"type": "string"}, "description": "Stock symbols to analyze"}
                            },
                            "required": ["symbols"]
                        }
                    }
                },

                "run_pairs_trading": {
                    "function": self.run_pairs_trading,
                    "schema": {
                        "name": "run_pairs_trading",
                        "description": "Run statistical pairs trading algorithm",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "pairs": {"type": "array", "items": {"type": "string"}, "description": "Symbol pairs like 'AAPL,MSFT'"}
                            },
                            "required": ["pairs"]
                        }
                    }
                },

                "detect_market_regime": {
                    "function": self.detect_market_regime,
                    "schema": {
                        "name": "detect_market_regime",
                        "description": "Detect current market regime and recommend strategies",
                        "parameters": {"type": "object", "properties": {}}
                    }
                },

                # ADVANCED OPTIONS STRATEGIES (NEW!)
                "create_iron_condor": {
                    "function": self.create_iron_condor,
                    "schema": {
                        "name": "create_iron_condor",
                        "description": "Create Iron Condor options strategy",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "symbol": {"type": "string", "description": "Underlying stock symbol"},
                                "expiration": {"type": "string", "description": "Expiration date (YYYY-MM-DD)"},
                                "wing_width": {"type": "number", "default": 10, "description": "Strike width between legs"}
                            },
                            "required": ["symbol", "expiration"]
                        }
                    }
                },

                "create_butterfly_spread": {
                    "function": self.create_butterfly_spread,
                    "schema": {
                        "name": "create_butterfly_spread",
                        "description": "Create Butterfly Spread options strategy",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "symbol": {"type": "string", "description": "Underlying stock symbol"},
                                "expiration": {"type": "string", "description": "Expiration date (YYYY-MM-DD)"},
                                "center_strike": {"type": "number", "description": "Center strike price (optional)"}
                            },
                            "required": ["symbol", "expiration"]
                        }
                    }
                },

                "volatility_strategy_selector": {
                    "function": self.volatility_strategy_selector,
                    "schema": {
                        "name": "volatility_strategy_selector",
                        "description": "Automatically select best options strategy based on volatility",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "symbol": {"type": "string", "description": "Underlying stock symbol"},
                                "expiration": {"type": "string", "description": "Expiration date (YYYY-MM-DD)"}
                            },
                            "required": ["symbol", "expiration"]
                        }
                    }
                }
            }
            
            # Add to existing TOOLS
            TOOLS.update(mcp_tools)
            
            self.is_integrated = True
            info(f"✅ Integrated {len(mcp_tools)} MCP tools into chat system")
            return True
            
        except Exception as e:
            error(f"Failed to integrate MCP tools: {e}")
            return False
    
    # MCP Tool Functions (these get called by the chat system)
    
    def get_account_balance(self) -> str:
        """Get account balance and status."""
        try:
            if 'get_account_info' not in self.mcp_functions:
                return "❌ Account info not available"
            
            # Run async function in sync context
            loop = self._get_event_loop()
            result = loop.run_until_complete(self.mcp_functions['get_account_info']())
            
            return f"💼 **Account Status**\n{result}"
            
        except Exception as e:
            return f"❌ Error getting account info: {str(e)}"
    
    def get_current_positions(self) -> str:
        """Get current positions."""
        try:
            if 'get_positions' not in self.mcp_functions:
                return "❌ Positions data not available"

            loop = self._get_event_loop()
            result = loop.run_until_complete(self.mcp_functions['get_positions']())

            return f"📊 **Current Positions**\n{result}"

        except Exception as e:
            return f"❌ Error getting positions: {str(e)}"

    def get_specific_position(self, symbol: str) -> str:
        """Get specific position details."""
        try:
            if 'get_open_position' not in self.mcp_functions:
                return f"❌ Position data not available for {symbol}"

            loop = self._get_event_loop()
            result = loop.run_until_complete(self.mcp_functions['get_open_position'](symbol))

            return f"📊 **Position Details for {symbol}**\n{result}"

        except Exception as e:
            return f"❌ Error getting position for {symbol}: {str(e)}"
    
    def get_enhanced_quote(self, symbol: str) -> str:
        """Get enhanced stock quote."""
        try:
            if 'get_stock_quote' not in self.mcp_functions:
                return f"❌ Quote data not available for {symbol}"
            
            loop = self._get_event_loop()
            result = loop.run_until_complete(self.mcp_functions['get_stock_quote'](symbol))
            
            return f"📈 **Enhanced Quote for {symbol}**\n{result}"
            
        except Exception as e:
            return f"❌ Error getting quote for {symbol}: {str(e)}"

    def get_stock_history(self, symbol: str, days: int = 5) -> str:
        """Get stock historical data."""
        try:
            if 'get_stock_bars' not in self.mcp_functions:
                return f"❌ Historical data not available for {symbol}"

            loop = self._get_event_loop()
            result = loop.run_until_complete(self.mcp_functions['get_stock_bars'](symbol, days))

            return f"📈 **{days}-Day History for {symbol}**\n{result}"

        except Exception as e:
            return f"❌ Error getting history for {symbol}: {str(e)}"

    def get_stock_trades(self, symbol: str, days: int = 1) -> str:
        """Get stock trade data."""
        try:
            if 'get_stock_trades' not in self.mcp_functions:
                return f"❌ Trade data not available for {symbol}"

            loop = self._get_event_loop()
            result = loop.run_until_complete(self.mcp_functions['get_stock_trades'](symbol, days))

            return f"💹 **Trade Data for {symbol}**\n{result}"

        except Exception as e:
            return f"❌ Error getting trades for {symbol}: {str(e)}"

    def get_latest_trade(self, symbol: str) -> str:
        """Get latest trade."""
        try:
            if 'get_stock_latest_trade' not in self.mcp_functions:
                return f"❌ Latest trade not available for {symbol}"

            loop = self._get_event_loop()
            result = loop.run_until_complete(self.mcp_functions['get_stock_latest_trade'](symbol))

            return f"⚡ **Latest Trade for {symbol}**\n{result}"

        except Exception as e:
            return f"❌ Error getting latest trade for {symbol}: {str(e)}"

    def get_latest_bar(self, symbol: str) -> str:
        """Get latest price bar."""
        try:
            if 'get_stock_latest_bar' not in self.mcp_functions:
                return f"❌ Latest bar not available for {symbol}"

            loop = self._get_event_loop()
            result = loop.run_until_complete(self.mcp_functions['get_stock_latest_bar'](symbol))

            return f"📊 **Latest Bar for {symbol}**\n{result}"

        except Exception as e:
            return f"❌ Error getting latest bar for {symbol}: {str(e)}"
    
    def place_enhanced_order(self, symbol: str, side: str, quantity: float, 
                           order_type: str = "market", limit_price: float = None, 
                           stop_price: float = None) -> str:
        """Place enhanced order."""
        try:
            if 'place_stock_order' not in self.mcp_functions:
                return f"❌ Order placement not available"
            
            loop = self._get_event_loop()
            result = loop.run_until_complete(
                self.mcp_functions['place_stock_order'](
                    symbol, side, quantity, order_type, limit_price, stop_price
                )
            )
            
            return f"✅ **Order Placed**\n{result}"
            
        except Exception as e:
            return f"❌ Error placing order: {str(e)}"

    def get_all_orders(self, status: str = "all", limit: int = 10) -> str:
        """Get all orders with filtering."""
        try:
            if 'get_orders' not in self.mcp_functions:
                return "❌ Orders data not available"

            loop = self._get_event_loop()
            result = loop.run_until_complete(self.mcp_functions['get_orders'](status, limit))

            return f"📋 **Orders ({status})**\n{result}"

        except Exception as e:
            return f"❌ Error getting orders: {str(e)}"

    def cancel_all_orders(self) -> str:
        """Cancel all open orders."""
        try:
            if 'cancel_all_orders' not in self.mcp_functions:
                return "❌ Order cancellation not available"

            loop = self._get_event_loop()
            result = loop.run_until_complete(self.mcp_functions['cancel_all_orders']())

            return f"🚫 **All Orders Cancelled**\n{result}"

        except Exception as e:
            return f"❌ Error cancelling orders: {str(e)}"

    def cancel_specific_order(self, order_id: str) -> str:
        """Cancel specific order."""
        try:
            if 'cancel_order_by_id' not in self.mcp_functions:
                return f"❌ Order cancellation not available for {order_id}"

            loop = self._get_event_loop()
            result = loop.run_until_complete(self.mcp_functions['cancel_order_by_id'](order_id))

            return f"🚫 **Order {order_id} Cancelled**\n{result}"

        except Exception as e:
            return f"❌ Error cancelling order {order_id}: {str(e)}"

    def close_position(self, symbol: str, quantity: str = None, percentage: str = None) -> str:
        """Close a specific position."""
        try:
            if 'close_position' not in self.mcp_functions:
                return f"❌ Position closing not available for {symbol}"

            loop = self._get_event_loop()
            result = loop.run_until_complete(self.mcp_functions['close_position'](symbol, quantity, percentage))

            return f"🔒 **Position Closed for {symbol}**\n{result}"

        except Exception as e:
            return f"❌ Error closing position for {symbol}: {str(e)}"

    def close_all_positions(self, cancel_orders: bool = False) -> str:
        """Close all positions."""
        try:
            if 'close_all_positions' not in self.mcp_functions:
                return "❌ Position closing not available"

            loop = self._get_event_loop()
            result = loop.run_until_complete(self.mcp_functions['close_all_positions'](cancel_orders))

            return f"🔒 **All Positions Closed**\n{result}"

        except Exception as e:
            return f"❌ Error closing all positions: {str(e)}"

    def get_asset_info(self, symbol: str) -> str:
        """Get asset information."""
        try:
            if 'get_asset_info' not in self.mcp_functions:
                return f"❌ Asset info not available for {symbol}"

            loop = self._get_event_loop()
            result = loop.run_until_complete(self.mcp_functions['get_asset_info'](symbol))

            return f"ℹ️ **Asset Info for {symbol}**\n{result}"

        except Exception as e:
            return f"❌ Error getting asset info for {symbol}: {str(e)}"

    def search_assets(self, status: str = None, asset_class: str = None, exchange: str = None) -> str:
        """Search assets."""
        try:
            if 'get_all_assets' not in self.mcp_functions:
                return "❌ Asset search not available"

            loop = self._get_event_loop()
            result = loop.run_until_complete(self.mcp_functions['get_all_assets'](status, asset_class, exchange))

            return f"🔍 **Asset Search Results**\n{result}"

        except Exception as e:
            return f"❌ Error searching assets: {str(e)}"

    def get_watchlists(self) -> str:
        """Get all watchlists."""
        try:
            if 'get_watchlists' not in self.mcp_functions:
                return "❌ Watchlists not available"

            loop = self._get_event_loop()
            result = loop.run_until_complete(self.mcp_functions['get_watchlists']())

            return f"📋 **Watchlists**\n{result}"

        except Exception as e:
            return f"❌ Error getting watchlists: {str(e)}"

    def create_watchlist(self, name: str, symbols: list) -> str:
        """Create new watchlist."""
        try:
            if 'create_watchlist' not in self.mcp_functions:
                return f"❌ Watchlist creation not available"

            loop = self._get_event_loop()
            result = loop.run_until_complete(self.mcp_functions['create_watchlist'](name, symbols))

            return f"📋 **Watchlist '{name}' Created**\n{result}"

        except Exception as e:
            return f"❌ Error creating watchlist: {str(e)}"

    def update_watchlist(self, watchlist_id: str, name: str = None, symbols: list = None) -> str:
        """Update watchlist."""
        try:
            if 'update_watchlist' not in self.mcp_functions:
                return f"❌ Watchlist update not available"

            loop = self._get_event_loop()
            result = loop.run_until_complete(self.mcp_functions['update_watchlist'](watchlist_id, name, symbols))

            return f"📋 **Watchlist Updated**\n{result}"

        except Exception as e:
            return f"❌ Error updating watchlist: {str(e)}"

    def get_market_status(self) -> str:
        """Get market status."""
        try:
            if 'get_market_clock' not in self.mcp_functions:
                return "❌ Market status not available"

            loop = self._get_event_loop()
            result = loop.run_until_complete(self.mcp_functions['get_market_clock']())

            return f"🕐 **Market Status**\n{result}"

        except Exception as e:
            return f"❌ Error getting market status: {str(e)}"

    def get_option_contracts(self, symbol: str, expiration: str = None, strike_min: str = None, strike_max: str = None) -> str:
        """Get option contracts."""
        try:
            if 'get_option_contracts' not in self.mcp_functions:
                return f"❌ Option contracts not available for {symbol}"

            from datetime import datetime
            exp_date = None
            if expiration:
                try:
                    exp_date = datetime.strptime(expiration, '%Y-%m-%d').date()
                except:
                    pass

            loop = self._get_event_loop()
            result = loop.run_until_complete(
                self.mcp_functions['get_option_contracts'](symbol, exp_date, strike_min, strike_max)
            )

            return f"📋 **Option Contracts for {symbol}**\n{result}"

        except Exception as e:
            return f"❌ Error getting option contracts for {symbol}: {str(e)}"

    def get_option_quote(self, symbol: str) -> str:
        """Get option quote."""
        try:
            if 'get_option_latest_quote' not in self.mcp_functions:
                return f"❌ Option quote not available for {symbol}"

            loop = self._get_event_loop()
            result = loop.run_until_complete(self.mcp_functions['get_option_latest_quote'](symbol))

            return f"💰 **Option Quote for {symbol}**\n{result}"

        except Exception as e:
            return f"❌ Error getting option quote for {symbol}: {str(e)}"

    def place_option_order(self, legs: list, quantity: int = 1, order_class: str = None) -> str:
        """Place option order."""
        try:
            if 'place_option_market_order' not in self.mcp_functions:
                return "❌ Option order placement not available"

            from alpaca.trading.enums import TimeInForce

            loop = self._get_event_loop()
            result = loop.run_until_complete(
                self.mcp_functions['place_option_market_order'](legs, order_class, quantity, TimeInForce.DAY)
            )

            return f"✅ **Option Order Placed**\n{result}"

        except Exception as e:
            return f"❌ Error placing option order: {str(e)}"

    # ADVANCED ALGORITHMIC TRADING FUNCTIONS (NEW!)
    def run_momentum_algorithm(self, symbols: list) -> str:
        """Run multi-timeframe momentum algorithm."""
        try:
            from core.advanced_algo_trading import AdvancedAlgoTrading

            algo_engine = AdvancedAlgoTrading(self.mcp_functions)
            loop = self._get_event_loop()
            signals = loop.run_until_complete(algo_engine.momentum_multi_timeframe(symbols))

            if not signals:
                return "📊 **Multi-Timeframe Momentum Analysis**\n\nNo strong momentum signals found in current market conditions."

            result = "🚀 **ADVANCED MOMENTUM ALGORITHM RESULTS**\n"
            result += "=" * 50 + "\n\n"

            for signal in signals:
                result += f"📈 **{signal.symbol}** - {signal.action.upper()}\n"
                result += f"   Confidence: {signal.confidence:.1%}\n"
                result += f"   Position Size: {signal.size_recommendation:.1%} of portfolio\n"
                result += f"   Entry: ${signal.entry_price:.2f}\n"
                result += f"   Stop Loss: ${signal.stop_loss:.2f}\n"
                result += f"   Take Profit: ${signal.take_profit:.2f}\n"
                result += f"   Reasoning: {signal.reasoning}\n\n"

            result += "💡 **This is advanced multi-timeframe analysis your TotalRecall didn't have before!**"
            return result

        except Exception as e:
            return f"❌ Error running momentum algorithm: {str(e)}"

    def run_mean_reversion_algorithm(self, symbols: list) -> str:
        """Run mean reversion algorithm with volatility filtering."""
        try:
            from core.advanced_algo_trading import AdvancedAlgoTrading

            algo_engine = AdvancedAlgoTrading(self.mcp_functions)
            loop = self._get_event_loop()
            signals = loop.run_until_complete(algo_engine.mean_reversion_volatility_filter(symbols))

            if not signals:
                return "📊 **Mean Reversion + Volatility Filter**\n\nNo mean reversion opportunities in current volatility regime."

            result = "🎯 **ADVANCED MEAN REVERSION ALGORITHM**\n"
            result += "=" * 45 + "\n\n"

            for signal in signals:
                result += f"⚖️ **{signal.symbol}** - {signal.action.upper()}\n"
                result += f"   Confidence: {signal.confidence:.1%}\n"
                result += f"   Position Size: {signal.size_recommendation:.1%}\n"
                result += f"   Entry: ${signal.entry_price:.2f}\n"
                result += f"   Analysis: {signal.reasoning}\n\n"

            result += "💡 **This combines mean reversion with volatility regime filtering!**"
            return result

        except Exception as e:
            return f"❌ Error running mean reversion algorithm: {str(e)}"

    def run_pairs_trading(self, pairs: list) -> str:
        """Run statistical pairs trading algorithm."""
        try:
            from core.advanced_algo_trading import AdvancedAlgoTrading

            # Parse pairs from strings like "AAPL,MSFT"
            pair_tuples = []
            for pair_str in pairs:
                symbols = pair_str.split(',')
                if len(symbols) == 2:
                    pair_tuples.append((symbols[0].strip(), symbols[1].strip()))

            algo_engine = AdvancedAlgoTrading(self.mcp_functions)
            loop = self._get_event_loop()
            signals = loop.run_until_complete(algo_engine.pairs_trading_algorithm(pair_tuples))

            if not signals:
                return "📊 **Statistical Pairs Trading**\n\nNo cointegrated pairs with trading opportunities found."

            result = "🔗 **STATISTICAL PAIRS TRADING ALGORITHM**\n"
            result += "=" * 50 + "\n\n"

            # Group signals by pairs
            pair_signals = {}
            for signal in signals:
                key = signal.reasoning.split('.')[0]  # Get pair description
                if key not in pair_signals:
                    pair_signals[key] = []
                pair_signals[key].append(signal)

            for pair_desc, pair_signals_list in pair_signals.items():
                result += f"🎯 **{pair_desc}**\n"
                for signal in pair_signals_list:
                    result += f"   {signal.symbol}: {signal.action.upper()} (Size: {signal.size_recommendation:.1%})\n"
                result += f"   Strategy: Statistical arbitrage on spread divergence\n\n"

            result += "💡 **This is advanced statistical arbitrage your TotalRecall didn't have!**"
            return result

        except Exception as e:
            return f"❌ Error running pairs trading: {str(e)}"

    def detect_market_regime(self) -> str:
        """Detect current market regime and recommend strategies."""
        try:
            from core.advanced_algo_trading import AdvancedAlgoTrading

            algo_engine = AdvancedAlgoTrading(self.mcp_functions)
            loop = self._get_event_loop()
            regime = loop.run_until_complete(algo_engine.detect_market_regime())

            result = "🌍 **MARKET REGIME ANALYSIS**\n"
            result += "=" * 35 + "\n\n"
            result += f"📊 **Current Regime:** {regime.regime.upper()}\n"
            result += f"🎯 **Confidence:** {regime.confidence:.1%}\n"
            result += f"📈 **Volatility Percentile:** {regime.volatility_percentile:.1f}%\n"
            result += f"🔗 **Correlation Breakdown:** {'Yes' if regime.correlation_breakdown else 'No'}\n\n"

            result += "🎯 **Recommended Strategies:**\n"
            for strategy in regime.recommended_strategies:
                result += f"   • {strategy.replace('_', ' ').title()}\n"

            result += "\n💡 **This is advanced market regime detection your TotalRecall didn't have!**"
            return result

        except Exception as e:
            return f"❌ Error detecting market regime: {str(e)}"

    # ADVANCED OPTIONS STRATEGIES (NEW!)
    def create_iron_condor(self, symbol: str, expiration: str, wing_width: float = 10) -> str:
        """Create Iron Condor options strategy."""
        try:
            from core.advanced_options_strategies import AdvancedOptionsStrategies

            options_engine = AdvancedOptionsStrategies(self.mcp_functions)
            loop = self._get_event_loop()
            strategy = loop.run_until_complete(
                options_engine.create_iron_condor(symbol, expiration, wing_width)
            )

            if 'error' in strategy:
                return f"❌ {strategy['error']}"

            result = "🦅 **IRON CONDOR STRATEGY CREATED**\n"
            result += "=" * 40 + "\n\n"
            result += f"📊 **Symbol:** {symbol}\n"
            result += f"📅 **Expiration:** {expiration}\n"
            result += f"📐 **Wing Width:** ${wing_width}\n\n"

            result += "🦵 **Strategy Legs:**\n"
            for i, leg in enumerate(strategy['legs'], 1):
                result += f"   {i}. {leg['side'].upper()} {leg['ratio_qty']}x {leg['symbol']}\n"

            analysis = strategy['analysis']
            result += f"\n📈 **Risk/Reward Analysis:**\n"
            result += f"   Max Profit: ${analysis.max_profit:.2f}\n"
            result += f"   Max Loss: ${analysis.max_loss:.2f}\n"
            result += f"   Probability of Profit: {analysis.probability_of_profit:.1%}\n"
            result += f"   Required Capital: ${analysis.required_capital:.2f}\n\n"

            result += f"💡 **Recommendation:** {strategy['recommendation']}\n\n"
            result += "🚀 **This is a sophisticated 4-leg options strategy your TotalRecall couldn't create before!**"

            return result

        except Exception as e:
            return f"❌ Error creating Iron Condor: {str(e)}"

    def create_butterfly_spread(self, symbol: str, expiration: str, center_strike: float = None) -> str:
        """Create Butterfly Spread options strategy."""
        try:
            from core.advanced_options_strategies import AdvancedOptionsStrategies

            options_engine = AdvancedOptionsStrategies(self.mcp_functions)
            loop = self._get_event_loop()
            strategy = loop.run_until_complete(
                options_engine.create_butterfly_spread(symbol, expiration, center_strike)
            )

            if 'error' in strategy:
                return f"❌ {strategy['error']}"

            result = "🦋 **BUTTERFLY SPREAD STRATEGY**\n"
            result += "=" * 35 + "\n\n"
            result += f"📊 **Symbol:** {symbol}\n"
            result += f"📅 **Expiration:** {expiration}\n\n"

            result += "🦵 **Strategy Legs:**\n"
            for i, leg in enumerate(strategy['legs'], 1):
                result += f"   {i}. {leg['side'].upper()} {leg['ratio_qty']}x {leg['symbol']}\n"

            analysis = strategy['analysis']
            result += f"\n📈 **Analysis:**\n"
            result += f"   Max Profit: ${analysis.max_profit:.2f}\n"
            result += f"   Max Loss: ${analysis.max_loss:.2f}\n"
            result += f"   Breakeven Points: {', '.join([f'${bp:.2f}' for bp in analysis.breakeven_points])}\n\n"

            result += f"💡 **Recommendation:** {strategy['recommendation']}\n\n"
            result += "🚀 **This is an advanced low-volatility strategy your TotalRecall couldn't create!**"

            return result

        except Exception as e:
            return f"❌ Error creating Butterfly Spread: {str(e)}"

    def volatility_strategy_selector(self, symbol: str, expiration: str) -> str:
        """Automatically select best options strategy based on volatility."""
        try:
            from core.advanced_options_strategies import AdvancedOptionsStrategies

            options_engine = AdvancedOptionsStrategies(self.mcp_functions)
            loop = self._get_event_loop()
            strategy = loop.run_until_complete(
                options_engine.volatility_strategy_selector(symbol, expiration)
            )

            if 'error' in strategy:
                return f"❌ {strategy['error']}"

            result = "🧠 **AI VOLATILITY STRATEGY SELECTOR**\n"
            result += "=" * 45 + "\n\n"
            result += f"📊 **Symbol:** {symbol}\n"
            result += f"📅 **Expiration:** {expiration}\n\n"

            result += f"🎯 **Selected Strategy:** {strategy['strategy']}\n\n"

            result += "🦵 **Recommended Legs:**\n"
            for i, leg in enumerate(strategy['legs'], 1):
                result += f"   {i}. {leg['side'].upper()} {leg['ratio_qty']}x {leg['symbol']}\n"

            result += f"\n💡 **AI Reasoning:** {strategy['recommendation']}\n\n"
            result += "🤖 **This is AI-powered strategy selection based on volatility analysis!**"
            result += "\n🚀 **Your TotalRecall now has artificial intelligence for options trading!**"

            return result

        except Exception as e:
            return f"❌ Error in volatility strategy selector: {str(e)}"
    
    def get_options_greeks(self, symbol: str) -> str:
        """Get options Greeks."""
        try:
            if 'get_option_snapshot' not in self.mcp_functions:
                return f"❌ Options data not available for {symbol}"

            loop = self._get_event_loop()
            result = loop.run_until_complete(self.mcp_functions['get_option_snapshot'](symbol))

            return f"🎯 **Options Greeks for {symbol}**\n{result}"

        except Exception as e:
            return f"❌ Error getting options data for {symbol}: {str(e)}"
    
    def get_market_calendar_info(self, days_ahead: int = 7) -> str:
        """Get market calendar."""
        try:
            if 'get_market_calendar' not in self.mcp_functions:
                return "❌ Market calendar not available"
            
            from datetime import datetime, timedelta
            start_date = datetime.now().strftime('%Y-%m-%d')
            end_date = (datetime.now() + timedelta(days=days_ahead)).strftime('%Y-%m-%d')
            
            loop = self._get_event_loop()
            result = loop.run_until_complete(
                self.mcp_functions['get_market_calendar'](start_date, end_date)
            )
            
            return f"📅 **Market Calendar**\n{result}"
            
        except Exception as e:
            return f"❌ Error getting market calendar: {str(e)}"
    
    def get_earnings_calendar(self, symbol: str = None, days_ahead: int = 30) -> str:
        """Get earnings calendar."""
        try:
            if 'get_corporate_announcements' not in self.mcp_functions:
                return "❌ Earnings calendar not available"
            
            from datetime import datetime, timedelta
            from alpaca.trading.enums import CorporateActionType
            
            start_date = datetime.now().date()
            end_date = start_date + timedelta(days=days_ahead)
            
            loop = self._get_event_loop()
            result = loop.run_until_complete(
                self.mcp_functions['get_corporate_announcements'](
                    [CorporateActionType.EARNINGS], start_date, end_date, symbol
                )
            )
            
            return f"📊 **Earnings Calendar**\n{result}"
            
        except Exception as e:
            return f"❌ Error getting earnings calendar: {str(e)}"
    
    def manage_watchlist(self, action: str, name: str = None, symbols: List[str] = None) -> str:
        """Manage watchlists."""
        try:
            if action == "create" and 'create_watchlist' in self.mcp_functions:
                loop = self._get_event_loop()
                result = loop.run_until_complete(
                    self.mcp_functions['create_watchlist'](name, symbols or [])
                )
                return f"📋 **Watchlist Created**\n{result}"
            
            elif action == "get" and 'get_watchlists' in self.mcp_functions:
                loop = self._get_event_loop()
                result = loop.run_until_complete(self.mcp_functions['get_watchlists']())
                return f"📋 **Watchlists**\n{result}"
            
            else:
                return f"❌ Watchlist action '{action}' not available"
            
        except Exception as e:
            return f"❌ Error managing watchlist: {str(e)}"
    
    def _get_event_loop(self):
        """Get or create event loop for async operations."""
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        return loop

# Global instance
_direct_mcp = None

def get_direct_mcp() -> DirectMCPIntegration:
    """Get the global direct MCP integration instance."""
    global _direct_mcp
    if _direct_mcp is None:
        _direct_mcp = DirectMCPIntegration()
    return _direct_mcp

def initialize_direct_mcp_integration() -> bool:
    """Initialize direct MCP integration into chat system."""
    try:
        direct_mcp = get_direct_mcp()
        success = direct_mcp.integrate_with_chat_tools()
        
        if success:
            info("🚀 Direct MCP integration successful!")
            info("💡 New chat commands available:")
            info("   • 'What's my account balance?' - Account info")
            info("   • 'Show my positions' - Current positions")
            info("   • 'Get quote for AAPL' - Enhanced quotes")
            info("   • 'Buy 100 AAPL' - Place orders")
            info("   • 'Greeks for TSLA' - Options analysis")
            info("   • 'Market calendar' - Trading schedule")
            info("   • 'Earnings calendar' - Corporate actions")
            return True
        else:
            warning("⚠️ Direct MCP integration failed")
            return False
            
    except Exception as e:
        error(f"Direct MCP initialization failed: {e}")
        return False

def is_direct_mcp_available() -> bool:
    """Check if direct MCP integration is available."""
    try:
        direct_mcp = get_direct_mcp()
        return direct_mcp.is_integrated and len(direct_mcp.mcp_functions) > 0
    except:
        return False
