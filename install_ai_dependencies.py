#!/usr/bin/env python3
"""
Install AI TTM Dependencies
Installs required packages for the AI TTM system
"""

import subprocess
import sys

def install_package(package):
    """Install a package using pip."""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    """Install all required AI dependencies."""
    print("🤖 INSTALLING AI TTM DEPENDENCIES")
    print("=" * 50)
    
    # Required packages
    packages = [
        "scikit-learn",
        "xgboost", 
        "joblib"
    ]
    
    success_count = 0
    
    for package in packages:
        print(f"\n📦 Installing {package}...")
        if install_package(package):
            print(f"✅ {package} installed successfully")
            success_count += 1
        else:
            print(f"❌ Failed to install {package}")
    
    print(f"\n🎯 INSTALLATION COMPLETE!")
    print(f"✅ Successfully installed: {success_count}/{len(packages)} packages")
    
    if success_count == len(packages):
        print("\n🚀 All dependencies installed! AI TTM system is ready!")
        print("\n💡 Next steps:")
        print("1. Run: python test_ai_ttm_system.py")
        print("2. In TotalRecall chat: 'build AI dataset'")
        print("3. In TotalRecall chat: 'train AI model'")
        print("4. In TotalRecall chat: 'run AI TTM scan'")
    else:
        print(f"\n⚠️ Some packages failed to install.")
        print("Try running manually:")
        for package in packages:
            print(f"pip install {package}")

if __name__ == "__main__":
    main()
