"""Global asynchronous-friendly rate limiter.

This utility prevents us from exceeding vendor-imposed API rate limits.
We keep it deliberately simple and dependency-free.
"""
from __future__ import annotations

import asyncio
import time
from collections import defaultdict, deque
from typing import Deque, Dict

# Default limits (per minute) -------------------------------------------------
DEFAULT_LIMITS = {
    "fmp": 300,     # Financial Modeling Prep (free-tier)
    "alpaca": 200,  # Alpaca data/trading combined
}

# ---------------------------------------------------------------------------
class RateLimiter:
    """Token-bucket style rate limiter compatible with async/await."""

    def __init__(self, limit: int, window: float = 60.0):
        self.limit = limit
        self.window = window
        self._events: Deque[float] = deque(maxlen=limit)
        self._lock = asyncio.Lock()

    async def acquire(self) -> None:
        """Await until a request token is available."""
        async with self._lock:
            while True:
                now = time.perf_counter()
                # Drop events outside the window
                while self._events and now - self._events[0] > self.window:
                    self._events.popleft()
                if len(self._events) < self.limit:
                    self._events.append(now)
                    return  # Token granted
                # Need to wait
                sleep_for = self.window - (now - self._events[0]) + 0.01
                await asyncio.sleep(sleep_for)

    # Synchronous counterpart ------------------------------------------------
    def acquire_sync(self) -> None:  # noqa: D401 – blocking token acquire
        """Blocking version so non-async code can throttle too."""
        while True:
            now = time.perf_counter()
            # Drop stale events
            while self._events and now - self._events[0] > self.window:
                self._events.popleft()
            if len(self._events) < self.limit:
                self._events.append(now)
                return
            sleep_for = self.window - (now - self._events[0]) + 0.01
            time.sleep(sleep_for)

# Registry of limiters per vendor -------------------------------------------
_limiters: Dict[str, RateLimiter] = {
    name: RateLimiter(limit) for name, limit in DEFAULT_LIMITS.items()
}

async def throttle(vendor: str) -> None:
    """Public helper – await this before calling a vendor API."""
    limiter = _limiters.get(vendor)
    if limiter is None:
        # Unknown vendor – create on the fly with generous default
        limiter = _limiters.setdefault(vendor, RateLimiter(500))
    await limiter.acquire() 