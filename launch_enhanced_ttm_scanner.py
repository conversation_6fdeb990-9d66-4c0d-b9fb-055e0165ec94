#!/usr/bin/env python3
"""
Enhanced TTM Scanner Launcher - Launch the sophisticated pattern recognition system
Provides comprehensive scanning with S&P 500 + $100B+ market cap coverage
"""

import sys
import os
import argparse
import time
from pathlib import Path
from datetime import datetime

# Add core directory to path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir / 'core'))
sys.path.insert(0, str(current_dir / 'scanners'))

def check_dependencies():
    """Check if all required dependencies are available."""
    missing_deps = []
    
    # Check core dependencies
    try:
        import aiohttp
    except ImportError:
        missing_deps.append("aiohttp")
    
    try:
        import pandas
    except ImportError:
        missing_deps.append("pandas")
    
    try:
        import numpy
    except ImportError:
        missing_deps.append("numpy")
    
    try:
        import requests
    except ImportError:
        missing_deps.append("requests")
    
    # Check optional dependencies
    optional_missing = []
    
    try:
        import pytz
    except ImportError:
        optional_missing.append("pytz (for market hours)")
    
    if missing_deps:
        print("❌ Missing required dependencies:")
        for dep in missing_deps:
            print(f"   • {dep}")
        print("\nInstall with: pip install " + " ".join(missing_deps))
        return False
    
    if optional_missing:
        print("⚠️ Optional dependencies missing (features will be limited):")
        for dep in optional_missing:
            print(f"   • {dep}")
        print("\nInstall with: pip install " + " ".join([d.split()[0] for d in optional_missing]))
    
    return True


def initialize_symbol_database():
    """Initialize and update the symbol database."""
    try:
        from core.symbol_manager import get_symbol_manager, update_symbol_database
        
        print("🔄 Initializing symbol database...")
        
        # Get symbol manager
        symbol_manager = get_symbol_manager()
        
        # Update symbol database
        print("📊 Fetching S&P 500 constituents and $100B+ market cap stocks...")
        stats = update_symbol_database()
        
        if "error" in stats:
            print(f"❌ Error updating symbol database: {stats['error']}")
            return False
        
        print(f"✅ Symbol database updated:")
        print(f"   • Total symbols: {stats.get('total_symbols', 0)}")
        print(f"   • S&P 500 symbols: {stats.get('sp500_symbols', 0)}")
        print(f"   • Large cap symbols: {stats.get('large_cap_symbols', 0)}")
        print(f"   • New symbols added: {stats.get('new_symbols', 0)}")
        print(f"   • Symbols updated: {stats.get('updated_symbols', 0)}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import symbol manager: {e}")
        return False
    except Exception as e:
        print(f"❌ Error initializing symbol database: {e}")
        return False


def test_enhanced_scanner():
    """Test the enhanced scanner with a few symbols."""
    try:
        from scanners.enhanced_ttm_squeeze_scanner import EnhancedTTMSqueezeScanner
        import asyncio
        
        print("🧪 Testing Enhanced TTM Scanner...")
        
        scanner = EnhancedTTMSqueezeScanner()
        test_symbols = ['AAPL', 'PLTR', 'MSFT']
        
        async def test_scan():
            results = []
            for symbol in test_symbols:
                print(f"   Testing {symbol}...")
                setup = await scanner.scan_symbol(symbol, "15min")
                if setup:
                    results.append(setup)
                    print(f"   ✅ {symbol}: Grade {setup.grade}, Confidence {setup.confidence:.1%}")
                else:
                    print(f"   ⚪ {symbol}: No setup found")
            return results
        
        # Run test
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            results = loop.run_until_complete(test_scan())
            print(f"✅ Scanner test completed: {len(results)} setups found")
            return True
        finally:
            loop.close()
            
    except ImportError as e:
        print(f"❌ Failed to import enhanced scanner: {e}")
        return False
    except Exception as e:
        print(f"❌ Scanner test failed: {e}")
        return False


def start_high_frequency_scanning():
    """Start the high-frequency scanning system."""
    try:
        from core.high_frequency_scanner import start_high_frequency_scanning, get_scanner_statistics
        
        print("🚀 Starting High-Frequency TTM Scanning System...")
        
        # Start the scanner
        if start_high_frequency_scanning():
            print("✅ High-frequency scanner started successfully!")
            
            # Show initial statistics
            stats = get_scanner_statistics()
            print(f"\n📊 Scanner Configuration:")
            print(f"   • Scan Interval: {stats.get('scan_interval_minutes', 5)} minutes")
            print(f"   • Timeframes: {', '.join(stats.get('timeframes', []))}")
            print(f"   • Max Concurrent Scans: {stats.get('max_concurrent_scans', 20)}")
            print(f"   • Market Time: {'🟢 Yes' if stats.get('is_market_time') else '🔴 No'}")
            
            print(f"\n🎯 The scanner will:")
            print(f"   • Monitor S&P 500 + $100B+ market cap stocks")
            print(f"   • Scan every 5 minutes during market hours")
            print(f"   • Use advanced pattern recognition algorithms")
            print(f"   • Store all setups found in the database")
            print(f"   • Send alerts for high-quality setups")
            
            print(f"\n⏰ Scanner is now running continuously...")
            print(f"Press Ctrl+C to stop the scanner")
            
            # Keep running until interrupted
            try:
                while True:
                    time.sleep(10)
                    
                    # Show periodic updates
                    stats = get_scanner_statistics()
                    if stats.get('last_scan_time'):
                        print(f"📊 Last scan: {stats.get('setups_found', 0)} setups found, "
                              f"{stats.get('symbols_per_minute', 0):.1f} symbols/min")
                    
                    time.sleep(290)  # Wait ~5 minutes between updates
                    
            except KeyboardInterrupt:
                print(f"\n⏹️ Stopping scanner...")
                from core.high_frequency_scanner import stop_high_frequency_scanning
                stop_high_frequency_scanning()
                print(f"✅ Scanner stopped")
                return True
        else:
            print("❌ Failed to start high-frequency scanner")
            return False
            
    except ImportError as e:
        print(f"❌ Failed to import high-frequency scanner: {e}")
        return False
    except Exception as e:
        print(f"❌ Error starting high-frequency scanning: {e}")
        return False


def show_scanner_status():
    """Show current scanner status and statistics."""
    try:
        from core.high_frequency_scanner import get_scanner_statistics
        from core.symbol_manager import get_symbol_statistics
        
        print("📊 ENHANCED TTM SCANNER STATUS")
        print("=" * 50)
        
        # Scanner statistics
        scanner_stats = get_scanner_statistics()
        print(f"\n🔍 SCANNER STATUS:")
        print(f"   • Running: {'🟢 Yes' if scanner_stats.get('is_running') else '🔴 No'}")
        print(f"   • Market Time: {'🟢 Yes' if scanner_stats.get('is_market_time') else '🔴 No'}")
        print(f"   • Dependencies: {'🟢 Available' if scanner_stats.get('dependencies_available') else '🔴 Missing'}")
        
        print(f"\n📈 SCANNING STATISTICS:")
        print(f"   • Total Scans: {scanner_stats.get('total_scans', 0):,}")
        print(f"   • Symbols Scanned: {scanner_stats.get('symbols_scanned', 0):,}")
        print(f"   • Setups Found: {scanner_stats.get('setups_found', 0):,}")
        print(f"   • High-Quality Setups: {scanner_stats.get('high_quality_setups', 0):,}")
        print(f"   • Scan Errors: {scanner_stats.get('scan_errors', 0):,}")
        
        if scanner_stats.get('last_scan_time'):
            print(f"   • Last Scan: {scanner_stats['last_scan_time']}")
            print(f"   • Scan Duration: {scanner_stats.get('scan_duration', 0):.1f}s")
            print(f"   • Symbols/Minute: {scanner_stats.get('symbols_per_minute', 0):.1f}")
        
        # Symbol statistics
        symbol_stats = get_symbol_statistics()
        if symbol_stats:
            print(f"\n🎯 SYMBOL COVERAGE:")
            print(f"   • Total Active Symbols: {symbol_stats.get('total_active_symbols', 0):,}")
            print(f"   • S&P 500 Symbols: {symbol_stats.get('sp500_symbols', 0):,}")
            print(f"   • Large Cap Symbols: {symbol_stats.get('large_cap_symbols', 0):,}")
            print(f"   • Priority Symbols: {symbol_stats.get('priority_symbols', 0):,}")
            
            market_cap_ranges = symbol_stats.get('market_cap_ranges', {})
            print(f"\n💰 MARKET CAP DISTRIBUTION:")
            print(f"   • $1T+: {market_cap_ranges.get('trillion_plus', 0):,}")
            print(f"   • $100B-$1T: {market_cap_ranges.get('hundred_billion_plus', 0):,}")
            print(f"   • $10B-$100B: {market_cap_ranges.get('ten_billion_plus', 0):,}")
            print(f"   • <$10B: {market_cap_ranges.get('under_ten_billion', 0):,}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import status modules: {e}")
        return False
    except Exception as e:
        print(f"❌ Error getting scanner status: {e}")
        return False


def main():
    """Main launcher function."""
    parser = argparse.ArgumentParser(
        description="Enhanced TTM Scanner - Advanced Pattern Recognition System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python launch_enhanced_ttm_scanner.py --init          # Initialize symbol database
  python launch_enhanced_ttm_scanner.py --test          # Test the scanner
  python launch_enhanced_ttm_scanner.py --start         # Start high-frequency scanning
  python launch_enhanced_ttm_scanner.py --status        # Show scanner status
        """
    )
    
    parser.add_argument('--init', action='store_true',
                       help='Initialize and update symbol database')
    parser.add_argument('--test', action='store_true',
                       help='Test the enhanced scanner')
    parser.add_argument('--start', action='store_true',
                       help='Start high-frequency scanning')
    parser.add_argument('--status', action='store_true',
                       help='Show scanner status and statistics')
    parser.add_argument('--check-deps', action='store_true',
                       help='Check dependencies')
    
    args = parser.parse_args()
    
    # Show banner
    print("""
🎯 ENHANCED TTM SQUEEZE SCANNER
===============================
Advanced Pattern Recognition System for High-Quality TTM Setups

Features:
• 🔍 Sophisticated pattern detection algorithms
• 📊 S&P 500 + $100B+ market cap coverage (~500+ symbols)
• ⚡ 5-minute scanning frequency during market hours
• 🎯 Enhanced 5-point quality criteria system
• 🤖 Automated symbol list management
• 📈 Real-time setup storage and tracking
""")
    
    # Check dependencies first
    if not check_dependencies():
        print("\n❌ Please install missing dependencies before continuing.")
        return 1
    
    # Handle arguments
    if args.check_deps:
        print("✅ All required dependencies are available!")
        return 0
    
    elif args.init:
        success = initialize_symbol_database()
        return 0 if success else 1
    
    elif args.test:
        success = test_enhanced_scanner()
        return 0 if success else 1
    
    elif args.start:
        success = start_high_frequency_scanning()
        return 0 if success else 1
    
    elif args.status:
        success = show_scanner_status()
        return 0 if success else 1
    
    else:
        # No arguments - show interactive menu
        print("🎛️ LAUNCH OPTIONS:")
        print("1. 🔄 Initialize Symbol Database")
        print("2. 🧪 Test Enhanced Scanner")
        print("3. 🚀 Start High-Frequency Scanning")
        print("4. 📊 Show Scanner Status")
        print("5. ❌ Exit")
        
        while True:
            try:
                choice = input("\nSelect option (1-5): ").strip()
                
                if choice == '1':
                    return 0 if initialize_symbol_database() else 1
                elif choice == '2':
                    return 0 if test_enhanced_scanner() else 1
                elif choice == '3':
                    return 0 if start_high_frequency_scanning() else 1
                elif choice == '4':
                    show_scanner_status()
                    continue
                elif choice == '5':
                    print("👋 Goodbye!")
                    return 0
                else:
                    print("❌ Invalid choice. Please select 1-5.")
                    
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                return 0
            except Exception as e:
                print(f"❌ Error: {e}")
                return 1


if __name__ == "__main__":
    sys.exit(main())
