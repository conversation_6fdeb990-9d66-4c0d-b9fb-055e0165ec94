#!/usr/bin/env python3
"""
TTM Alert Control Panel - GUI for managing real-time TTM squeeze alerts
Provides comprehensive control over monitoring, filtering, and notifications
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
from datetime import datetime
from typing import Dict, Any

try:
    from core.ttm_alert_system import (
        get_alert_system, start_ttm_alerts, stop_ttm_alerts,
        get_alert_status, get_alert_statistics, update_alert_filters,
        configure_notifications, get_recent_ttm_alerts
    )
    ALERT_SYSTEM_AVAILABLE = True
except ImportError:
    ALERT_SYSTEM_AVAILABLE = False


class TTMAlertControlPanel:
    """GUI Control Panel for TTM Alert System."""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("TotalRecall - TTM Alert Control Panel")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # Alert system reference
        self.alert_system = get_alert_system() if ALERT_SYSTEM_AVAILABLE else None
        
        # GUI update thread
        self.update_thread = None
        self.running = True
        
        self.setup_gui()
        self.start_status_updates()
    
    def setup_gui(self):
        """Setup the main GUI layout."""
        # Create notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Main Control Tab
        self.setup_control_tab(notebook)
        
        # Filtering Tab
        self.setup_filtering_tab(notebook)
        
        # Notifications Tab
        self.setup_notifications_tab(notebook)
        
        # Statistics Tab
        self.setup_statistics_tab(notebook)
        
        # Alert History Tab
        self.setup_history_tab(notebook)
    
    def setup_control_tab(self, notebook):
        """Setup main control tab."""
        control_frame = ttk.Frame(notebook)
        notebook.add(control_frame, text="🎛️ Control")
        
        # Status Section
        status_group = ttk.LabelFrame(control_frame, text="📊 System Status", padding=10)
        status_group.pack(fill=tk.X, padx=10, pady=5)
        
        self.status_label = tk.Label(status_group, text="🔴 Stopped", 
                                   font=("Arial", 12, "bold"), fg="red")
        self.status_label.pack()
        
        self.status_details = tk.Label(status_group, text="Ready to start monitoring", 
                                     font=("Arial", 10))
        self.status_details.pack()
        
        # Control Buttons
        button_frame = ttk.Frame(status_group)
        button_frame.pack(pady=10)
        
        self.start_button = ttk.Button(button_frame, text="🚀 Start Monitoring", 
                                     command=self.start_monitoring)
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(button_frame, text="⏹️ Stop Monitoring", 
                                    command=self.stop_monitoring, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        # Quick Settings
        settings_group = ttk.LabelFrame(control_frame, text="⚙️ Quick Settings", padding=10)
        settings_group.pack(fill=tk.X, padx=10, pady=5)
        
        # Grade selection
        grade_frame = ttk.Frame(settings_group)
        grade_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(grade_frame, text="Minimum Grade:").pack(side=tk.LEFT)
        self.grade_var = tk.StringVar(value="A")
        grade_combo = ttk.Combobox(grade_frame, textvariable=self.grade_var, 
                                 values=["A+", "A", "B", "C"], width=5)
        grade_combo.pack(side=tk.LEFT, padx=5)
        
        # Scan interval
        interval_frame = ttk.Frame(settings_group)
        interval_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(interval_frame, text="Scan Interval (minutes):").pack(side=tk.LEFT)
        self.interval_var = tk.StringVar(value="2")
        interval_spin = ttk.Spinbox(interval_frame, from_=0.5, to=60, increment=0.5,
                                  textvariable=self.interval_var, width=10)
        interval_spin.pack(side=tk.LEFT, padx=5)
        
        # Market hours only
        self.market_hours_var = tk.BooleanVar(value=True)
        market_check = ttk.Checkbutton(settings_group, text="Market Hours Only",
                                     variable=self.market_hours_var)
        market_check.pack(anchor=tk.W, pady=2)
        
        # Live Feed
        feed_group = ttk.LabelFrame(control_frame, text="📡 Live Alert Feed", padding=10)
        feed_group.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.alert_feed = scrolledtext.ScrolledText(feed_group, height=10, 
                                                  font=("Consolas", 9))
        self.alert_feed.pack(fill=tk.BOTH, expand=True)
        
        # Add initial message
        self.log_message("🎯 TTM Alert Control Panel Ready")
        self.log_message("📋 Configure settings and click 'Start Monitoring' to begin")
    
    def setup_filtering_tab(self, notebook):
        """Setup filtering configuration tab."""
        filter_frame = ttk.Frame(notebook)
        notebook.add(filter_frame, text="🔍 Filters")
        
        # Price Filters
        price_group = ttk.LabelFrame(filter_frame, text="💰 Price Filters", padding=10)
        price_group.pack(fill=tk.X, padx=10, pady=5)
        
        price_frame = ttk.Frame(price_group)
        price_frame.pack(fill=tk.X)
        
        ttk.Label(price_frame, text="Min Price: $").pack(side=tk.LEFT)
        self.min_price_var = tk.StringVar(value="1.00")
        ttk.Entry(price_frame, textvariable=self.min_price_var, width=10).pack(side=tk.LEFT, padx=5)
        
        ttk.Label(price_frame, text="Max Price: $").pack(side=tk.LEFT, padx=(20,0))
        self.max_price_var = tk.StringVar(value="1000.00")
        ttk.Entry(price_frame, textvariable=self.max_price_var, width=10).pack(side=tk.LEFT, padx=5)
        
        # Volume Filters
        volume_group = ttk.LabelFrame(filter_frame, text="📊 Volume Filters", padding=10)
        volume_group.pack(fill=tk.X, padx=10, pady=5)
        
        volume_frame = ttk.Frame(volume_group)
        volume_frame.pack(fill=tk.X)
        
        ttk.Label(volume_frame, text="Min Volume:").pack(side=tk.LEFT)
        self.min_volume_var = tk.StringVar(value="100000")
        ttk.Entry(volume_frame, textvariable=self.min_volume_var, width=15).pack(side=tk.LEFT, padx=5)
        
        # Quality Filters
        quality_group = ttk.LabelFrame(filter_frame, text="🎯 Quality Filters", padding=10)
        quality_group.pack(fill=tk.X, padx=10, pady=5)
        
        conf_frame = ttk.Frame(quality_group)
        conf_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(conf_frame, text="Min Confidence (%):").pack(side=tk.LEFT)
        self.min_confidence_var = tk.StringVar(value="60")
        ttk.Spinbox(conf_frame, from_=0, to=100, textvariable=self.min_confidence_var, 
                   width=10).pack(side=tk.LEFT, padx=5)
        
        rr_frame = ttk.Frame(quality_group)
        rr_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(rr_frame, text="Min Risk/Reward:").pack(side=tk.LEFT)
        self.min_rr_var = tk.StringVar(value="1.5")
        ttk.Spinbox(rr_frame, from_=0.5, to=10.0, increment=0.1, 
                   textvariable=self.min_rr_var, width=10).pack(side=tk.LEFT, padx=5)
        
        # Rate Limiting
        rate_group = ttk.LabelFrame(filter_frame, text="⏱️ Rate Limiting", padding=10)
        rate_group.pack(fill=tk.X, padx=10, pady=5)
        
        rate_frame = ttk.Frame(rate_group)
        rate_frame.pack(fill=tk.X)
        
        ttk.Label(rate_frame, text="Max Alerts per Hour:").pack(side=tk.LEFT)
        self.max_alerts_var = tk.StringVar(value="10")
        ttk.Spinbox(rate_frame, from_=1, to=100, textvariable=self.max_alerts_var, 
                   width=10).pack(side=tk.LEFT, padx=5)
        
        # Apply Filters Button
        ttk.Button(filter_frame, text="✅ Apply Filters", 
                  command=self.apply_filters).pack(pady=20)
    
    def setup_notifications_tab(self, notebook):
        """Setup notification configuration tab."""
        notif_frame = ttk.Frame(notebook)
        notebook.add(notif_frame, text="🔔 Notifications")
        
        # Notification Types
        types_group = ttk.LabelFrame(notif_frame, text="📢 Notification Types", padding=10)
        types_group.pack(fill=tk.X, padx=10, pady=5)
        
        self.desktop_notif_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(types_group, text="🖥️ Desktop Notifications", 
                       variable=self.desktop_notif_var).pack(anchor=tk.W)
        
        self.sound_notif_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(types_group, text="🔊 Sound Alerts", 
                       variable=self.sound_notif_var).pack(anchor=tk.W)
        
        self.gui_popup_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(types_group, text="📱 GUI Popups", 
                       variable=self.gui_popup_var).pack(anchor=tk.W)
        
        self.voice_notif_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(types_group, text="🗣️ Voice Alerts", 
                       variable=self.voice_notif_var).pack(anchor=tk.W)
        
        # Notification Settings
        settings_group = ttk.LabelFrame(notif_frame, text="⚙️ Notification Settings", padding=10)
        settings_group.pack(fill=tk.X, padx=10, pady=5)
        
        timeout_frame = ttk.Frame(settings_group)
        timeout_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(timeout_frame, text="Desktop Timeout (seconds):").pack(side=tk.LEFT)
        self.desktop_timeout_var = tk.StringVar(value="10")
        ttk.Spinbox(timeout_frame, from_=1, to=60, textvariable=self.desktop_timeout_var, 
                   width=10).pack(side=tk.LEFT, padx=5)
        
        popup_frame = ttk.Frame(settings_group)
        popup_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(popup_frame, text="Popup Duration (ms):").pack(side=tk.LEFT)
        self.popup_duration_var = tk.StringVar(value="5000")
        ttk.Spinbox(popup_frame, from_=1000, to=30000, increment=1000,
                   textvariable=self.popup_duration_var, width=10).pack(side=tk.LEFT, padx=5)
        
        # Apply Notifications Button
        ttk.Button(notif_frame, text="✅ Apply Notification Settings", 
                  command=self.apply_notifications).pack(pady=20)
    
    def setup_statistics_tab(self, notebook):
        """Setup statistics display tab."""
        stats_frame = ttk.Frame(notebook)
        notebook.add(stats_frame, text="📈 Statistics")
        
        self.stats_text = scrolledtext.ScrolledText(stats_frame, font=("Consolas", 10))
        self.stats_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Refresh button
        ttk.Button(stats_frame, text="🔄 Refresh Statistics", 
                  command=self.refresh_statistics).pack(pady=5)
    
    def setup_history_tab(self, notebook):
        """Setup alert history tab."""
        history_frame = ttk.Frame(notebook)
        notebook.add(history_frame, text="📋 History")
        
        self.history_text = scrolledtext.ScrolledText(history_frame, font=("Consolas", 9))
        self.history_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Refresh button
        ttk.Button(history_frame, text="🔄 Refresh History",
                  command=self.refresh_history).pack(pady=5)

    def start_monitoring(self):
        """Start TTM alert monitoring."""
        if not ALERT_SYSTEM_AVAILABLE:
            messagebox.showerror("Error", "TTM Alert System not available!")
            return

        try:
            # Get settings
            min_grade = self.grade_var.get()
            scan_interval = int(float(self.interval_var.get()) * 60)  # Convert to seconds

            # Create custom filters
            custom_filters = {
                "market_hours_only": self.market_hours_var.get()
            }

            # Start monitoring
            result = start_ttm_alerts(min_grade, scan_interval, custom_filters)

            # Update UI
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)

            self.log_message("🚀 TTM Alert Monitoring Started!")
            self.log_message(f"📊 Grade: {min_grade}+, Interval: {scan_interval//60} min")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to start monitoring: {e}")
            self.log_message(f"❌ Error starting monitoring: {e}")

    def stop_monitoring(self):
        """Stop TTM alert monitoring."""
        try:
            result = stop_ttm_alerts()

            # Update UI
            self.start_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)

            self.log_message("⏹️ TTM Alert Monitoring Stopped")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to stop monitoring: {e}")
            self.log_message(f"❌ Error stopping monitoring: {e}")

    def apply_filters(self):
        """Apply filtering settings."""
        try:
            filters = {
                "min_price": float(self.min_price_var.get()),
                "max_price": float(self.max_price_var.get()),
                "min_volume": int(self.min_volume_var.get()),
                "min_confidence": float(self.min_confidence_var.get()) / 100,
                "min_risk_reward": float(self.min_rr_var.get()),
                "max_alerts_per_hour": int(self.max_alerts_var.get())
            }

            result = update_alert_filters(filters)
            self.log_message("✅ Filters updated successfully")
            self.log_message(f"📊 {len(filters)} filter settings applied")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to apply filters: {e}")
            self.log_message(f"❌ Error applying filters: {e}")

    def apply_notifications(self):
        """Apply notification settings."""
        try:
            config = {
                "desktop": {
                    "enabled": self.desktop_notif_var.get(),
                    "timeout": int(self.desktop_timeout_var.get())
                },
                "sound": {
                    "enabled": self.sound_notif_var.get()
                },
                "gui": {
                    "enabled": self.gui_popup_var.get(),
                    "popup_duration": int(self.popup_duration_var.get())
                },
                "voice": {
                    "enabled": self.voice_notif_var.get()
                }
            }

            result = configure_notifications(config)
            self.log_message("✅ Notification settings updated")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to update notifications: {e}")
            self.log_message(f"❌ Error updating notifications: {e}")

    def refresh_statistics(self):
        """Refresh statistics display."""
        try:
            stats = get_alert_statistics()

            stats_text = f"""📈 TTM ALERT SYSTEM STATISTICS
{'='*50}

🎯 MONITORING STATUS:
• System Running: {'🟢 Yes' if stats.get('is_running', False) else '🔴 No'}
• Market Time: {'🟢 Yes' if stats.get('is_market_time', False) else '🔴 No'}
• Last Scan: {stats.get('last_scan_time', 'Never')}
• Uptime: {stats.get('uptime_seconds', 0):.0f} seconds

📊 SCAN STATISTICS:
• Total Scans: {stats.get('total_scans', 0):,}
• Scan Errors: {stats.get('scan_errors', 0):,}
• Success Rate: {stats.get('success_rate', 0):.1f}%

🚨 ALERT STATISTICS:
• Alerts Sent: {stats.get('alerts_sent', 0):,}
• Alerts Filtered: {stats.get('alerts_filtered', 0):,}
• Alerts This Hour: {stats.get('alerts_per_hour', 0):,}
• Filter Efficiency: {stats.get('filter_efficiency', 0):.1f}%

⚙️ CURRENT FILTERS:
"""

            filters = stats.get('current_filters', {})
            for key, value in filters.items():
                stats_text += f"• {key}: {value}\n"

            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(1.0, stats_text)

        except Exception as e:
            self.log_message(f"❌ Error refreshing statistics: {e}")

    def refresh_history(self):
        """Refresh alert history display."""
        try:
            alerts = get_recent_ttm_alerts(50)  # Get last 50 alerts

            history_text = f"📋 RECENT TTM ALERTS ({len(alerts)} alerts)\n"
            history_text += "="*60 + "\n\n"

            if not alerts:
                history_text += "No alerts found.\n"
            else:
                for alert in reversed(alerts):  # Show newest first
                    history_text += f"🚨 {alert.timestamp.strftime('%H:%M:%S')} - "
                    history_text += f"{alert.symbol} Grade {alert.grade} "
                    history_text += f"(${alert.price:.2f})\n"
                    history_text += f"   Confidence: {alert.confidence*100:.0f}% | "
                    history_text += f"R/R: {alert.risk_reward_ratio:.1f}:1 | "
                    history_text += f"Criteria: {alert.criteria_count}/5\n\n"

            self.history_text.delete(1.0, tk.END)
            self.history_text.insert(1.0, history_text)

        except Exception as e:
            self.log_message(f"❌ Error refreshing history: {e}")

    def start_status_updates(self):
        """Start background status updates."""
        def update_status():
            while self.running:
                try:
                    if ALERT_SYSTEM_AVAILABLE:
                        status = get_alert_status()

                        # Update status display
                        if status.get('is_running', False):
                            self.status_label.config(text="🟢 Running", fg="green")
                            details = f"Grade: {status.get('min_grade', 'A')}+ | "
                            details += f"Interval: {status.get('scan_interval', 120)//60}min | "
                            details += f"Active Alerts: {status.get('active_alerts', 0)}"
                            self.status_details.config(text=details)
                        else:
                            self.status_label.config(text="🔴 Stopped", fg="red")
                            self.status_details.config(text="Ready to start monitoring")

                    time.sleep(5)  # Update every 5 seconds

                except Exception as e:
                    print(f"Status update error: {e}")
                    time.sleep(10)

        self.update_thread = threading.Thread(target=update_status, daemon=True)
        self.update_thread.start()

    def log_message(self, message: str):
        """Add message to alert feed."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.alert_feed.insert(tk.END, log_entry)
        self.alert_feed.see(tk.END)

    def run(self):
        """Run the control panel."""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
        except KeyboardInterrupt:
            self.on_closing()

    def on_closing(self):
        """Handle window closing."""
        self.running = False
        if self.update_thread and self.update_thread.is_alive():
            self.update_thread.join(timeout=1)
        self.root.destroy()


def main():
    """Launch the TTM Alert Control Panel."""
    if not ALERT_SYSTEM_AVAILABLE:
        print("❌ TTM Alert System not available!")
        print("Make sure all required modules are installed.")
        return

    print("🚀 Launching TTM Alert Control Panel...")

    try:
        panel = TTMAlertControlPanel()
        panel.run()
    except Exception as e:
        print(f"❌ Error launching control panel: {e}")


if __name__ == "__main__":
    main()
