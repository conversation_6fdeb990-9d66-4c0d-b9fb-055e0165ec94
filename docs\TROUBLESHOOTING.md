# 🔧 TotalRecall Enhanced - Troubleshooting Guide

## 🎯 **QUICK DIAGNOSTICS**

### **System Health Check**
Run this command to check your system status:
```python
python -c "from core.chat_core import TOOLS; print(f'🎉 Total tools: {len(TOOLS)}'); print('✅ System operational' if len(TOOLS) >= 90 else '⚠️ System needs attention')"
```

**Expected Results:**
- **90+ tools:** ✅ System fully operational
- **80-89 tools:** ⚠️ Partial functionality (some MCP features missing)
- **<80 tools:** ❌ System needs repair

### **MCP Integration Check**
```python
python -c "
try:
    from core.direct_mcp_integration import get_direct_mcp
    mcp = get_direct_mcp()
    print(f'✅ MCP Available: {mcp.is_integrated}')
    print(f'📊 MCP Functions: {len(mcp.mcp_functions)}')
except Exception as e:
    print(f'❌ MCP Error: {e}')
"
```

## 🚨 **COMMON ISSUES & SOLUTIONS**

### **Issue 1: Tool Count Less Than 90**

**Symptoms:**
- Missing advanced features
- Options strategies not available
- Algorithmic trading commands fail

**Diagnosis:**
```python
python -c "from core.chat_core import TOOLS; print(f'Current tools: {len(TOOLS)}')"
```

**Solutions:**
1. **Restart TotalRecall:**
   ```bash
   python main.py
   ```

2. **Check MCP Integration:**
   ```python
   python add_missing_tools.py
   ```

3. **Verify Environment:**
   - Check `.env` file exists
   - Verify API keys are set
   - Ensure alpaca-mcp-server is in integrations folder

### **Issue 2: "make me $50 today" Not Working**

**Symptoms:**
- Profit planning commands ignored
- No TTM analysis response
- Generic responses instead of profit plans

**Diagnosis:**
```python
python -c "from core.chat_core import chat_gpt; result = chat_gpt('make me 50 today'); print('✅ Working' if 'TTM' in result else '❌ Not working')"
```

**Solutions:**
1. **Check Detection Logic:**
   - Ensure message contains numbers
   - Try variations: "make me $50 today", "make me 50 dollars"

2. **Verify TTM Functions:**
   ```python
   python -c "from core.chat_core import TOOLS; print('make_profit_ttm' in TOOLS)"
   ```

3. **Test Fallback System:**
   - System should work even without OpenAI API
   - Check for TTM scanner availability

### **Issue 3: Options Strategies Not Available**

**Symptoms:**
- "Create Iron Condor" commands fail
- No options strategy responses
- Missing options analysis tools

**Diagnosis:**
```python
python -c "from core.chat_core import TOOLS; options_tools = [t for t in TOOLS if 'option' in t.lower() or 'iron' in t.lower() or 'butterfly' in t.lower()]; print(f'Options tools: {len(options_tools)}')"
```

**Solutions:**
1. **Check MCP Integration:**
   ```python
   python test_enhanced_mcp_integration.py
   ```

2. **Verify alpaca-mcp-server:**
   - Check `integrations/alpaca-mcp-server` exists
   - Verify server files are present

3. **Restart with MCP:**
   ```bash
   python launch_enhanced_totalrecall.py
   ```

### **Issue 4: API Connection Errors**

**Symptoms:**
- "API key not found" errors
- Connection timeout messages
- Market data not updating

**Diagnosis:**
```python
python -c "
import os
from pathlib import Path
env_file = Path('.env')
print(f'✅ .env exists: {env_file.exists()}')
if env_file.exists():
    content = env_file.read_text()
    print(f'✅ Has ALPACA_API_KEY: {\"ALPACA_API_KEY\" in content}')
    print(f'✅ Has FMP_API_KEY: {\"FMP_API_KEY\" in content}')
"
```

**Solutions:**
1. **Check .env File:**
   ```bash
   # Verify .env file exists and contains:
   ALPACA_API_KEY=your_key_here
   ALPACA_SECRET_KEY=your_secret_here
   ALPACA_BASE_URL=https://paper-api.alpaca.markets
   FMP_API_KEY=your_fmp_key_here
   OPENAI_API_KEY=your_openai_key_here
   ```

2. **Test API Connections:**
   ```python
   python tests/test_alpaca_auth.py
   ```

3. **Verify API Keys:**
   - Check Alpaca dashboard for correct keys
   - Ensure paper trading is enabled
   - Verify FMP subscription is active

### **Issue 5: Chat Commands Not Recognized**

**Symptoms:**
- Commands return generic responses
- No tool execution
- Missing functionality

**Diagnosis:**
```python
python -c "
from core.chat_core import chat_gpt
result = chat_gpt('system status')
print('✅ Working' if 'system' in result.lower() else '❌ Not working')
"
```

**Solutions:**
1. **Check OpenAI API:**
   - Verify OPENAI_API_KEY in .env
   - Test with simple command
   - Check API quota/billing

2. **Use Fallback Mode:**
   - System works without OpenAI
   - Try direct function calls
   - Check enhanced fallback responses

3. **Restart Chat System:**
   ```python
   python test_enhanced_chat.py
   ```

### **Issue 6: Performance Issues**

**Symptoms:**
- Slow response times
- System freezing
- High memory usage

**Diagnosis:**
```python
python -c "
import psutil
import time
start = time.time()
from core.chat_core import TOOLS
load_time = time.time() - start
memory = psutil.Process().memory_info().rss / 1024 / 1024
print(f'Load time: {load_time:.2f}s')
print(f'Memory usage: {memory:.1f}MB')
print(f'Tools loaded: {len(TOOLS)}')
"
```

**Solutions:**
1. **Optimize System:**
   - Close unnecessary applications
   - Restart TotalRecall
   - Check available RAM

2. **Reduce Load:**
   - Limit concurrent scans
   - Use smaller symbol lists
   - Disable unused features

3. **System Requirements:**
   - Minimum 8GB RAM recommended
   - Python 3.8+ required
   - Stable internet connection

## 🔍 **DIAGNOSTIC COMMANDS**

### **System Status Commands**
```python
# Check overall system health
"System status"

# Check automation status
"Automation status"

# Check tool availability
"Show help"

# Check MCP integration
python test_enhanced_mcp_integration.py
```

### **API Testing Commands**
```python
# Test market data
"Get quote for AAPL"

# Test account access
"What's my account balance?"

# Test order placement (paper trading)
"Buy 1 AAPL at market"

# Test options data
"Get options for AAPL"
```

### **Feature Testing Commands**
```python
# Test TTM scanning
"Scan for TTM opportunities"

# Test options strategies
"Create Iron Condor for AAPL"

# Test algorithmic trading
"Run momentum algorithm on AAPL"

# Test profit planning
"Make me $50 today"
```

## 🛠️ **ADVANCED TROUBLESHOOTING**

### **Database Issues**
```python
# Check database files
import os
db_files = [f for f in os.listdir('data') if f.endswith('.db')]
print(f"Database files: {db_files}")

# Reset databases if corrupted
python -c "
import sqlite3
import os
for db_file in ['data/ai_brain.db', 'data/performance_tracker.db']:
    if os.path.exists(db_file):
        try:
            conn = sqlite3.connect(db_file)
            conn.execute('SELECT 1')
            conn.close()
            print(f'✅ {db_file} OK')
        except:
            print(f'❌ {db_file} corrupted')
"
```

### **Module Import Issues**
```python
# Test critical imports
python -c "
modules = [
    'core.chat_core',
    'core.direct_mcp_integration', 
    'core.automation_engine',
    'scanners.ttm_squeeze_scanner'
]
for module in modules:
    try:
        __import__(module)
        print(f'✅ {module}')
    except Exception as e:
        print(f'❌ {module}: {e}')
"
```

### **Configuration Issues**
```python
# Check configuration files
python -c "
import json
from pathlib import Path
configs = ['config/config.json', 'integrations/mcp_config.json']
for config in configs:
    path = Path(config)
    if path.exists():
        try:
            data = json.loads(path.read_text())
            print(f'✅ {config}: {len(data)} keys')
        except:
            print(f'❌ {config}: Invalid JSON')
    else:
        print(f'⚠️ {config}: Not found')
"
```

## 🚀 **RECOVERY PROCEDURES**

### **Complete System Reset**
```bash
# 1. Backup important data
cp -r data data_backup

# 2. Reset configuration
python config/setup_alpaca.py

# 3. Reinstall dependencies
pip install -r config/requirements.txt

# 4. Test basic functionality
python test_system.py

# 5. Restore MCP integration
python setup_enhanced_mcp_integration.py
```

### **Partial Reset (Keep Data)**
```bash
# 1. Reset only configuration
rm config/config.json
python config/setup_alpaca.py

# 2. Restart system
python main.py

# 3. Test functionality
python comprehensive_launch_test.py
```

### **Emergency Fallback Mode**
```python
# Use basic functionality only
python -c "
from core.chat_core import chat_gpt
print('Testing basic chat...')
result = chat_gpt('hello')
print('✅ Basic chat working' if result else '❌ Chat failed')
"
```

## 📞 **GETTING HELP**

### **Self-Diagnosis Steps**
1. Run system health check
2. Check API connections
3. Verify tool count
4. Test basic commands
5. Review error logs

### **Information to Collect**
- Tool count and missing tools
- Error messages (exact text)
- System specifications
- API key status
- Recent changes made

### **Common Solutions Summary**
- **90% of issues:** Restart TotalRecall
- **5% of issues:** Check API keys
- **3% of issues:** Reinstall MCP integration
- **2% of issues:** System requirements/resources

## ✅ **PREVENTION TIPS**

### **Regular Maintenance**
- Restart system daily
- Check tool count weekly
- Update API keys as needed
- Monitor system performance
- Backup data regularly

### **Best Practices**
- Use stable internet connection
- Keep system updated
- Monitor resource usage
- Test new features in paper trading
- Follow risk management rules

**🎯 Most issues are resolved by restarting TotalRecall and verifying API keys! 🎯**
