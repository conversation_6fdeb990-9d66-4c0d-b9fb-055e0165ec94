#!/usr/bin/env python3
"""
Enhanced MCP Integration Setup for TotalRecall TTM Trading System

This script seamlessly integrates the alpaca-mcp-server capabilities
with your existing TotalRecall system without disrupting current functionality.

Features:
- Automatic detection of alpaca-mcp-server
- Seamless integration with existing chat system
- Enhanced capabilities without breaking changes
- Fallback to existing functionality
"""

import os
import sys
import shutil
from pathlib import Path
from typing import Dict, Any, Optional

def setup_enhanced_integration():
    """Setup enhanced MCP integration."""
    print("🚀 ENHANCED MCP INTEGRATION SETUP")
    print("=" * 50)
    
    # Step 1: Locate alpaca-mcp-server
    print("\n📍 Step 1: Locating alpaca-mcp-server...")
    mcp_server_path = locate_mcp_server()
    
    if not mcp_server_path:
        print("❌ alpaca-mcp-server not found!")
        print("\n💡 Please ensure alpaca-mcp-server is available at:")
        print("   • C:\\Users\\<USER>\\Downloads\\alpaca-mcp-server-main")
        print("   • Or in integrations/alpaca-mcp-server")
        return False
    
    print(f"✅ Found alpaca-mcp-server at: {mcp_server_path}")
    
    # Step 2: Setup integration
    print("\n🔧 Step 2: Setting up integration...")
    if not setup_integration_files(mcp_server_path):
        print("❌ Integration setup failed!")
        return False
    
    print("✅ Integration files configured")
    
    # Step 3: Initialize enhanced capabilities
    print("\n⚡ Step 3: Initializing enhanced capabilities...")
    if not initialize_enhanced_capabilities():
        print("❌ Enhanced capabilities initialization failed!")
        return False
    
    print("✅ Enhanced capabilities initialized")
    
    # Step 4: Test integration
    print("\n🧪 Step 4: Testing integration...")
    if not test_integration():
        print("⚠️ Integration test had issues, but basic functionality should work")
    else:
        print("✅ Integration test passed")
    
    # Step 5: Display usage instructions
    print("\n📚 Step 5: Usage Instructions")
    display_usage_instructions()
    
    print("\n🎉 ENHANCED MCP INTEGRATION COMPLETE!")
    print("\nYour TotalRecall system now has enhanced capabilities!")
    return True

def locate_mcp_server() -> Optional[Path]:
    """Locate the alpaca-mcp-server."""
    # Check Downloads directory
    downloads_path = Path.home() / "Downloads" / "alpaca-mcp-server-main"
    if downloads_path.exists() and (downloads_path / "alpaca_mcp_server.py").exists():
        return downloads_path
    
    # Check integrations directory
    integrations_path = Path("integrations") / "alpaca-mcp-server"
    if integrations_path.exists() and (integrations_path / "alpaca_mcp_server.py").exists():
        return integrations_path
    
    # Check current directory
    current_path = Path("alpaca-mcp-server-main")
    if current_path.exists() and (current_path / "alpaca_mcp_server.py").exists():
        return current_path
    
    return None

def setup_integration_files(mcp_server_path: Path) -> bool:
    """Setup integration files."""
    try:
        # Create integrations directory if it doesn't exist
        integrations_dir = Path("integrations")
        integrations_dir.mkdir(exist_ok=True)
        
        # Copy alpaca-mcp-server to integrations if not already there
        target_path = integrations_dir / "alpaca-mcp-server"
        if not target_path.exists():
            print(f"📁 Copying MCP server to integrations directory...")
            shutil.copytree(mcp_server_path, target_path)
            print("✅ MCP server copied to integrations")
        
        # Create configuration file
        config_file = integrations_dir / "mcp_config.json"
        config = {
            "mcp_server_path": str(target_path / "alpaca_mcp_server.py"),
            "enhanced_features_enabled": True,
            "fallback_to_existing": True,
            "integration_version": "1.0.0"
        }
        
        import json
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)
        
        print("✅ Configuration file created")
        return True
        
    except Exception as e:
        print(f"❌ Setup failed: {e}")
        return False

def initialize_enhanced_capabilities() -> bool:
    """Initialize enhanced capabilities."""
    try:
        print("🔧 Checking enhanced MCP integration...")

        # Try to import enhanced modules
        try:
            from core.enhanced_mcp_integration import get_enhanced_mcp, is_enhanced_mcp_available
            mcp_available = True
            print("✅ Enhanced MCP module available")
        except ImportError as e:
            print(f"⚠️ Enhanced MCP module not available: {e}")
            mcp_available = False

        try:
            from core.enhanced_chat_integration import get_enhanced_chat
            chat_available = True
            print("✅ Enhanced chat module available")
        except ImportError as e:
            print(f"⚠️ Enhanced chat module not available: {e}")
            chat_available = False

        if mcp_available and chat_available:
            print("✅ Enhanced modules loaded successfully")

            # Test basic functionality
            try:
                enhanced_mcp = get_enhanced_mcp()
                enhanced_chat = get_enhanced_chat()

                if enhanced_mcp and enhanced_chat:
                    print("✅ Enhanced instances created")
                    return True
                else:
                    print("⚠️ Enhanced instances creation had issues")
                    return False
            except Exception as e:
                print(f"⚠️ Enhanced instance creation failed: {e}")
                return False
        else:
            print("⚠️ Some enhanced modules not available")
            print("💡 System will work with existing functionality")
            return False

    except Exception as e:
        print(f"❌ Initialization error: {e}")
        print("💡 System will work with existing functionality")
        return False

def test_integration() -> bool:
    """Test the integration."""
    try:
        # Test enhanced MCP availability
        from core.enhanced_mcp_integration import is_enhanced_mcp_available, get_enhanced_capabilities
        
        if is_enhanced_mcp_available():
            capabilities = get_enhanced_capabilities()
            print(f"✅ Enhanced MCP available with {len(capabilities)} capabilities")
            return True
        else:
            print("⚠️ Enhanced MCP not available - using fallback")
            return False
            
    except Exception as e:
        print(f"⚠️ Test error: {e}")
        return False

def display_usage_instructions():
    """Display usage instructions."""
    print("\n💡 **ENHANCED CAPABILITIES USAGE:**")
    print("\n🎯 **Advanced Options Analysis:**")
    print("   • 'Greeks for AAPL' - Get detailed options Greeks")
    print("   • 'IV analysis for TSLA' - Implied volatility analysis")
    print("   • 'Options risk/reward for NVDA' - Risk metrics")
    
    print("\n💼 **Enhanced Portfolio Analysis:**")
    print("   • 'Portfolio analysis' - Complete portfolio overview")
    print("   • 'Portfolio risk' - Risk-focused analysis")
    print("   • 'My positions' - Detailed position breakdown")
    
    print("\n📈 **Market Intelligence:**")
    print("   • 'Earnings calendar' - Upcoming earnings")
    print("   • 'Market calendar' - Trading schedule")
    print("   • 'Corporate actions' - Dividends, splits, etc.")
    
    print("\n✅ **Smart Order Execution:**")
    print("   • 'Buy 100 AAPL with stop' - Advanced order types")
    print("   • 'Sell 50 TSLA with limit' - Limit orders")
    print("   • 'Place trailing stop' - Trailing stop orders")
    
    print("\n📋 **Watchlist Management:**")
    print("   • 'Create watchlist' - New watchlist creation")
    print("   • 'Analyze watchlist' - Watchlist performance")
    print("   • 'Update watchlist' - Modify existing lists")
    
    print("\n🔄 **Seamless Integration:**")
    print("   • All existing commands still work exactly the same")
    print("   • Enhanced features automatically activate when available")
    print("   • Fallback to existing functionality if MCP unavailable")
    print("   • No breaking changes to current workflow")
    
    print("\n🚀 **Getting Started:**")
    print("   1. Use your existing chat interface as normal")
    print("   2. Try enhanced commands for advanced features")
    print("   3. Enhanced responses will show 'Powered by Enhanced MCP'")
    print("   4. All safety systems and existing features remain active")

def create_enhanced_launcher():
    """Create enhanced launcher script."""
    try:
        launcher_content = '''#!/usr/bin/env python3
"""
Enhanced TotalRecall Launcher with MCP Integration

This launcher starts TotalRecall with enhanced MCP capabilities.
"""

import sys
from pathlib import Path

# Add current directory to path
sys.path.append(str(Path(__file__).parent))

def main():
    """Main launcher function."""
    print("🚀 Starting Enhanced TotalRecall with MCP Integration...")
    
    try:
        # Initialize enhanced integration
        from core.enhanced_mcp_integration import initialize_enhanced_mcp
        from core.enhanced_chat_integration import initialize_enhanced_chat
        
        print("🔧 Initializing enhanced capabilities...")
        initialize_enhanced_mcp()
        initialize_enhanced_chat()
        
        # Launch main interface
        print("🎯 Launching TotalRecall interface...")
        from launch_enhanced_desktop import main as launch_desktop
        launch_desktop()
        
    except ImportError:
        print("⚠️ Enhanced modules not available - launching standard interface")
        try:
            from launch_enhanced_desktop import main as launch_desktop
            launch_desktop()
        except ImportError:
            print("❌ Could not launch interface")
            return False
    
    except Exception as e:
        print(f"❌ Launch error: {e}")
        return False
    
    return True

if __name__ == "__main__":
    main()
'''
        
        launcher_file = Path("launch_enhanced_totalrecall.py")
        with open(launcher_file, 'w') as f:
            f.write(launcher_content)
        
        print(f"✅ Enhanced launcher created: {launcher_file}")
        return True
        
    except Exception as e:
        print(f"❌ Launcher creation failed: {e}")
        return False

def main():
    """Main setup function."""
    try:
        print("🎯 TotalRecall Enhanced MCP Integration Setup")
        print("=" * 50)
        print("This will enhance your existing system with advanced capabilities")
        print("without disrupting current functionality.\n")
        
        # Run setup
        success = setup_enhanced_integration()
        
        if success:
            # Create enhanced launcher
            create_enhanced_launcher()
            
            print("\n🎉 **SETUP COMPLETE!**")
            print("\n🚀 **Next Steps:**")
            print("1. Run: python launch_enhanced_totalrecall.py")
            print("2. Or use your existing launch scripts - they'll auto-detect enhancements")
            print("3. Try enhanced commands in the chat interface")
            print("4. All existing functionality remains unchanged")
            
            print("\n💡 **What's New:**")
            print("• Advanced options Greeks analysis")
            print("• Enhanced portfolio risk/reward metrics")
            print("• Corporate actions and earnings intelligence")
            print("• Smart order execution with advanced types")
            print("• Intelligent watchlist management")
            print("• Seamless fallback to existing features")
            
            return True
        else:
            print("\n❌ Setup failed - but your existing system is unchanged")
            return False
            
    except Exception as e:
        print(f"\n❌ Setup error: {e}")
        print("Your existing system is unchanged and should work normally.")
        return False

if __name__ == "__main__":
    main()
