#!/usr/bin/env python3
"""Clean News Formatter - Beautiful, Easy-to-Read News

Formats ugly news data into clean, professional, easy-to-read format.
No more messy formatting - just beautiful, clear news presentation.
"""
import requests
from datetime import datetime
from typing import List, Dict, Any


class CleanNewsFormatter:
    """Format news articles in a beautiful, readable way."""
    
    def __init__(self):
        self.fmp_key = None
        try:
            from config import get_api_key
            self.fmp_key = get_api_key('FMP_API_KEY')
        except:
            pass
    
    def get_clean_news(self, symbol: str, limit: int = 3) -> str:
        """Get beautifully formatted news for a symbol."""
        if not self.fmp_key:
            return "📰 **News:** Configure FMP API key to see latest news"
        
        try:
            # Get news from FMP
            url = f"https://financialmodelingprep.com/api/v3/stock_news?tickers={symbol}&limit={limit}&apikey={self.fmp_key}"
            response = requests.get(url, timeout=10)
            
            if response.status_code != 200:
                return "📰 **News:** Unable to fetch latest news"
            
            news_data = response.json()
            
            if not news_data:
                return f"📰 **News for ${symbol}:** No recent news available"
            
            return self._format_news_beautifully(news_data, symbol)
            
        except Exception as e:
            return f"📰 **News:** Error fetching news - {str(e)}"
    
    def _format_news_beautifully(self, news_articles: List[Dict], symbol: str) -> str:
        """Format news articles beautifully."""
        if not news_articles:
            return f"📰 **News for ${symbol}:** No recent news available"
        
        # Header
        formatted = f"📰 **Latest News for ${symbol}**\n"
        formatted += "=" * 40 + "\n\n"
        
        for i, article in enumerate(news_articles, 1):
            title = article.get('title', 'No title')
            published_date = article.get('publishedDate', '')
            site = article.get('site', 'Unknown')
            text = article.get('text', '')
            
            # Clean up the date
            clean_date = self._format_date(published_date)
            
            # Clean up the title (remove excessive caps, clean symbols)
            clean_title = self._clean_title(title)
            
            # Get summary (first sentence or first 100 chars)
            summary = self._get_summary(text)
            
            # Format each article beautifully
            formatted += f"**{i}. {clean_title}**\n"
            formatted += f"📅 {clean_date} • 🌐 {site}\n"
            if summary:
                formatted += f"💬 {summary}\n"
            formatted += "\n"
        
        # Add sentiment analysis
        sentiment = self._analyze_overall_sentiment(news_articles)
        formatted += f"📊 **Overall Sentiment:** {sentiment}\n"
        
        return formatted
    
    def _format_date(self, date_string: str) -> str:
        """Format date string nicely."""
        if not date_string:
            return "Recent"
        
        try:
            # Handle different date formats
            if 'T' in date_string:
                date_obj = datetime.fromisoformat(date_string.replace('Z', '+00:00'))
            else:
                date_obj = datetime.strptime(date_string, '%Y-%m-%d')
            
            # Format as "June 3, 2025"
            return date_obj.strftime('%B %d, %Y')
        except:
            return "Recent"
    
    def _clean_title(self, title: str) -> str:
        """Clean up news title for better readability."""
        if not title:
            return "No title"
        
        # Remove excessive punctuation
        title = title.replace('!!!', '!')
        title = title.replace('???', '?')
        
        # Fix common formatting issues
        title = title.replace(' - ', ' — ')
        
        # Limit length
        if len(title) > 80:
            title = title[:77] + "..."
        
        return title
    
    def _get_summary(self, text: str) -> str:
        """Get a clean summary from article text."""
        if not text:
            return ""
        
        # Get first sentence or first 120 characters
        sentences = text.split('. ')
        if sentences and len(sentences[0]) > 20:
            summary = sentences[0]
            if not summary.endswith('.'):
                summary += '.'
        else:
            summary = text[:120]
            if len(text) > 120:
                summary += "..."
        
        # Clean up summary
        summary = summary.replace('\n', ' ').replace('\r', ' ')
        summary = ' '.join(summary.split())  # Remove extra spaces
        
        return summary
    
    def _analyze_overall_sentiment(self, articles: List[Dict]) -> str:
        """Analyze overall sentiment of news articles."""
        if not articles:
            return "Neutral"
        
        positive_words = ['up', 'gain', 'rise', 'bull', 'positive', 'growth', 'profit', 'beat', 'strong', 'good']
        negative_words = ['down', 'fall', 'drop', 'bear', 'negative', 'loss', 'miss', 'weak', 'bad', 'decline']
        
        positive_count = 0
        negative_count = 0
        
        for article in articles:
            title = article.get('title', '').lower()
            text = article.get('text', '').lower()
            content = title + ' ' + text
            
            positive_count += sum(1 for word in positive_words if word in content)
            negative_count += sum(1 for word in negative_words if word in content)
        
        if positive_count > negative_count * 1.5:
            return "🟢 Bullish"
        elif negative_count > positive_count * 1.5:
            return "🔴 Bearish"
        else:
            return "🟡 Neutral"


def get_clean_news_for_symbol(symbol: str) -> str:
    """Get beautifully formatted news for a symbol."""
    formatter = CleanNewsFormatter()
    return formatter.get_clean_news(symbol)


def get_market_news_summary() -> str:
    """Get clean summary of general market news."""
    formatter = CleanNewsFormatter()
    
    try:
        if not formatter.fmp_key:
            return "📰 **Market News:** Configure FMP API key to see market news"
        
        # Get general market news
        url = f"https://financialmodelingprep.com/api/v3/fmp/articles?page=0&size=5&apikey={formatter.fmp_key}"
        response = requests.get(url, timeout=10)
        
        if response.status_code != 200:
            return "📰 **Market News:** Unable to fetch market news"
        
        news_data = response.json()
        
        if not news_data:
            return "📰 **Market News:** No recent market news available"
        
        # Format market news
        formatted = "📰 **Market News Summary**\n"
        formatted += "=" * 30 + "\n\n"
        
        for i, article in enumerate(news_data[:3], 1):
            title = article.get('title', 'No title')
            date = article.get('date', '')
            
            clean_date = formatter._format_date(date)
            clean_title = formatter._clean_title(title)
            
            formatted += f"**{i}. {clean_title}**\n"
            formatted += f"📅 {clean_date}\n\n"
        
        return formatted
        
    except Exception as e:
        return f"📰 **Market News:** Error fetching news - {str(e)}"


if __name__ == "__main__":
    # Test the clean news formatter
    formatter = CleanNewsFormatter()
    
    print("🧪 Testing Clean News Formatter")
    print("=" * 40)
    
    # Test with TSLA
    result = formatter.get_clean_news('TSLA')
    print(result)
    
    print("\n" + "=" * 40)
    print("🧪 Testing Market News")
    print("=" * 40)
    
    market_news = get_market_news_summary()
    print(market_news)
