#!/usr/bin/env python3
"""
Pandas TA TTM Scanner - Uses your specific pattern analysis
Implements the exact pattern logic you provided with pandas_ta
"""

import asyncio
import aiohttp
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import os

try:
    import pandas_ta as ta
    PANDAS_TA_AVAILABLE = True
except ImportError:
    PANDAS_TA_AVAILABLE = False
    print("⚠️ pandas_ta not available. Using manual implementation of your pattern.")

try:
    from core.logger_util import info, warning, error
except ImportError:
    def info(msg): print(f"ℹ️ {msg}")
    def warning(msg): print(f"⚠️ {msg}")
    def error(msg): print(f"❌ {msg}")

try:
    from config import get_api_key
    FMP_API_KEY = get_api_key('FMP_API_KEY')
except ImportError:
    FMP_API_KEY = os.getenv('FMP_API_KEY', 'demo')


@dataclass
class PandasTASetup:
    """TTM Setup using your pandas_ta pattern analysis."""
    symbol: str
    timeframe: str
    timestamp: datetime
    price: float
    volume: float
    
    # Signal components
    ema5_rising: bool
    ema8_rising: bool
    momentum_rising: bool
    histogram_rising: bool
    five_dots: bool
    signal: bool
    
    # Values
    ema5_value: float
    ema8_value: float
    momentum_value: float
    histogram_value: float
    
    # Trade levels
    entry_price: float
    stop_loss: float
    target_price: float
    risk_reward_ratio: float
    
    # Quality assessment
    grade: str
    confidence: float
    criteria_count: int
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for compatibility."""
        return {
            'symbol': self.symbol,
            'timeframe': self.timeframe,
            'grade': self.grade,
            'confidence': self.confidence,
            'criteria_count': self.criteria_count,
            'price': self.price,
            'volume': self.volume,
            'entry_price': self.entry_price,
            'stop_loss': self.stop_loss,
            'target_price': self.target_price,
            'momentum_value': self.momentum_value,
            'breakout_probability': self.confidence,
            'sector': '',
            'market_cap': 0.0
        }


class PandasTATTMScanner:
    """TTM Scanner using your pandas_ta pattern analysis."""

    def __init__(self):
        self.api_key = FMP_API_KEY

        if not PANDAS_TA_AVAILABLE:
            info("Using manual implementation of your pattern (pandas_ta not available)")

    def calculate_ema(self, data: pd.Series, length: int) -> pd.Series:
        """Calculate Exponential Moving Average manually."""
        return data.ewm(span=length, adjust=False).mean()

    def calculate_momentum(self, data: pd.Series, length: int = 14) -> pd.Series:
        """Calculate Momentum manually."""
        return data - data.shift(length)

    def calculate_bollinger_bands(self, data: pd.Series, length: int = 20, std: float = 2.0) -> tuple:
        """Calculate Bollinger Bands manually."""
        sma = data.rolling(window=length).mean()
        rolling_std = data.rolling(window=length).std()
        upper = sma + (rolling_std * std)
        lower = sma - (rolling_std * std)
        return upper, sma, lower

    def calculate_keltner_channels(self, high: pd.Series, low: pd.Series, close: pd.Series,
                                  length: int = 20, multiplier: float = 2.0) -> tuple:
        """Calculate Keltner Channels manually."""
        ema = self.calculate_ema(close, length)
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = true_range.rolling(window=length).mean()

        upper = ema + (atr * multiplier)
        lower = ema - (atr * multiplier)
        return upper, ema, lower

    def calculate_ttm_squeeze_manual(self, high: pd.Series, low: pd.Series, close: pd.Series) -> Dict[str, pd.Series]:
        """Calculate TTM Squeeze indicators manually."""
        # Bollinger Bands
        bb_upper, bb_middle, bb_lower = self.calculate_bollinger_bands(close)

        # Keltner Channels
        kc_upper, kc_middle, kc_lower = self.calculate_keltner_channels(high, low, close)

        # Squeeze condition: BB inside KC
        squeeze_on = (bb_lower > kc_lower) & (bb_upper < kc_upper)
        squeeze_off = (bb_lower < kc_lower) | (bb_upper > kc_upper)
        no_squeeze = ~squeeze_on

        # Calculate histogram (momentum oscillator)
        # Using Linear Regression for momentum calculation
        def linear_regression_slope(series, length=20):
            """Calculate linear regression slope."""
            x = np.arange(length)
            slopes = []
            for i in range(length-1, len(series)):
                y = series.iloc[i-length+1:i+1].values
                if len(y) == length:
                    slope = np.polyfit(x, y, 1)[0]
                    slopes.append(slope)
                else:
                    slopes.append(0)
            return pd.Series(slopes, index=series.index[length-1:])

        # Calculate momentum histogram
        momentum_hist = linear_regression_slope(close - ((bb_upper + bb_lower) / 2))

        return {
            'SQZ_ON': squeeze_on.astype(int),
            'SQZ_OFF': squeeze_off.astype(int),
            'SQZ_NO': no_squeeze.astype(int),
            'SQZ_HIST': momentum_hist
        }
    
    def calculate_signal(self, df: pd.DataFrame) -> pd.Series:
        """
        Your exact pattern analysis implementation.
        """
        try:
            # Calculate indicators (use manual implementation if pandas_ta not available)
            if PANDAS_TA_AVAILABLE:
                try:
                    # Try pandas_ta first
                    ema5 = ta.ema(df['close'], length=5)
                    ema8 = ta.ema(df['close'], length=8)
                    momentum = ta.mom(df['close'], length=14)
                    ttm_squeeze = ta.squeeze(df['high'], df['low'], df['close'])
                    hist = ttm_squeeze['SQZ_HIST']
                    squeeze_alert = ttm_squeeze['SQZ_NO']
                except Exception:
                    # Fall back to manual implementation
                    PANDAS_TA_AVAILABLE = False

            if not PANDAS_TA_AVAILABLE:
                # Use manual implementation
                ema5 = self.calculate_ema(df['close'], 5)
                ema8 = self.calculate_ema(df['close'], 8)
                momentum = self.calculate_momentum(df['close'], 14)
                ttm_squeeze = self.calculate_ttm_squeeze_manual(df['high'], df['low'], df['close'])
                hist = ttm_squeeze['SQZ_HIST']
                squeeze_alert = ttm_squeeze['SQZ_NO']

            # Your exact conditions
            # EMA5 conditions
            ema5_rising = ema5 > ema5.shift(1)

            # EMA8 conditions
            ema8_rising = ema8 > ema8.shift(3)

            # Momentum conditions
            mom_rising = momentum > momentum.shift(3)

            # TTM Squeeze Histogram conditions
            hist_rising = (hist > hist.shift(1)) & \
                          (hist.shift(1) < hist.shift(2)) & \
                          (hist.shift(2) < hist.shift(3)) & \
                          (hist.shift(3) < hist.shift(4)) & \
                          (hist.shift(4) < hist.shift(5))

            # Five dots in a row (SqueezeAlert == 0 for 3 periods)
            five_dots = squeeze_alert.rolling(window=3).sum() == 3

            # Combine all conditions
            signal = ema5_rising & ema8_rising & mom_rising & hist_rising & five_dots

            return signal.astype(int)

        except Exception as e:
            error(f"Error calculating signal: {e}")
            return pd.Series([0] * len(df), index=df.index)
    
    def get_signal_components(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Get individual signal components for analysis."""
        try:
            # Calculate indicators (use manual implementation if pandas_ta not available)
            if PANDAS_TA_AVAILABLE:
                try:
                    # Try pandas_ta first
                    ema5 = ta.ema(df['close'], length=5)
                    ema8 = ta.ema(df['close'], length=8)
                    momentum = ta.mom(df['close'], length=14)
                    ttm_squeeze = ta.squeeze(df['high'], df['low'], df['close'])
                    hist = ttm_squeeze['SQZ_HIST']
                    squeeze_alert = ttm_squeeze['SQZ_NO']
                except Exception:
                    # Fall back to manual implementation
                    ema5 = self.calculate_ema(df['close'], 5)
                    ema8 = self.calculate_ema(df['close'], 8)
                    momentum = self.calculate_momentum(df['close'], 14)
                    ttm_squeeze = self.calculate_ttm_squeeze_manual(df['high'], df['low'], df['close'])
                    hist = ttm_squeeze['SQZ_HIST']
                    squeeze_alert = ttm_squeeze['SQZ_NO']
            else:
                # Use manual implementation
                ema5 = self.calculate_ema(df['close'], 5)
                ema8 = self.calculate_ema(df['close'], 8)
                momentum = self.calculate_momentum(df['close'], 14)
                ttm_squeeze = self.calculate_ttm_squeeze_manual(df['high'], df['low'], df['close'])
                hist = ttm_squeeze['SQZ_HIST']
                squeeze_alert = ttm_squeeze['SQZ_NO']

            # Individual conditions
            ema5_rising = ema5 > ema5.shift(1)
            ema8_rising = ema8 > ema8.shift(3)
            mom_rising = momentum > momentum.shift(3)

            hist_rising = (hist > hist.shift(1)) & \
                          (hist.shift(1) < hist.shift(2)) & \
                          (hist.shift(2) < hist.shift(3)) & \
                          (hist.shift(3) < hist.shift(4)) & \
                          (hist.shift(4) < hist.shift(5))

            five_dots = squeeze_alert.rolling(window=3).sum() == 3

            # Get latest values (handle NaN)
            def safe_bool(series):
                return bool(series.iloc[-1]) if len(series) > 0 and not pd.isna(series.iloc[-1]) else False

            def safe_float(series):
                return float(series.iloc[-1]) if len(series) > 0 and not pd.isna(series.iloc[-1]) else 0.0

            return {
                "ema5_rising": safe_bool(ema5_rising),
                "ema8_rising": safe_bool(ema8_rising),
                "momentum_rising": safe_bool(mom_rising),
                "histogram_rising": safe_bool(hist_rising),
                "five_dots": safe_bool(five_dots),
                "ema5_value": safe_float(ema5),
                "ema8_value": safe_float(ema8),
                "momentum_value": safe_float(momentum),
                "histogram_value": safe_float(hist),
                "squeeze_active": safe_bool(squeeze_alert)
            }

        except Exception as e:
            error(f"Error getting signal components: {e}")
            return {}
    
    async def get_market_data(self, symbol: str, timeframe: str = "15min", limit: int = 100) -> pd.DataFrame:
        """Get market data for analysis."""
        try:
            # Convert timeframe to FMP format
            interval_map = {
                "1min": "1min", "5min": "5min", "15min": "15min", 
                "30min": "30min", "1hour": "1hour", "4hour": "4hour"
            }
            
            fmp_interval = interval_map.get(timeframe, "15min")
            
            url = f"https://financialmodelingprep.com/api/v3/historical-chart/{fmp_interval}/{symbol}"
            params = {"apikey": self.api_key, "limit": limit}
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        if not data:
                            return pd.DataFrame()
                        
                        df = pd.DataFrame(data)
                        df['date'] = pd.to_datetime(df['date'])
                        df = df.sort_values('date').reset_index(drop=True)
                        
                        # Ensure we have required columns
                        required_cols = ['open', 'high', 'low', 'close', 'volume']
                        for col in required_cols:
                            if col not in df.columns:
                                warning(f"Missing column {col} for {symbol}")
                                return pd.DataFrame()
                        
                        return df
                    else:
                        warning(f"Failed to get data for {symbol}: {response.status}")
                        return pd.DataFrame()
                        
        except Exception as e:
            error(f"Error getting market data for {symbol}: {e}")
            return pd.DataFrame()
    
    def calculate_trade_levels(self, df: pd.DataFrame) -> tuple:
        """Calculate entry, stop loss, and target prices."""
        try:
            current_price = df['close'].iloc[-1]
            
            # Simple trade level calculation
            # You can customize this based on your trading strategy
            entry_price = current_price * 1.001  # Small premium for entry
            stop_loss = current_price * 0.98     # 2% stop loss
            target_price = current_price * 1.06  # 6% target (3:1 R/R)
            
            return entry_price, stop_loss, target_price
            
        except Exception as e:
            error(f"Error calculating trade levels: {e}")
            return current_price, current_price * 0.95, current_price * 1.05
    
    def assign_grade(self, components: Dict[str, Any]) -> tuple:
        """Assign grade based on signal components."""
        try:
            # Count how many conditions are met
            conditions = [
                components.get("ema5_rising", False),
                components.get("ema8_rising", False),
                components.get("momentum_rising", False),
                components.get("histogram_rising", False),
                components.get("five_dots", False)
            ]
            
            criteria_count = sum(conditions)
            
            # Assign grade based on criteria count
            if criteria_count == 5:
                grade = "A+"
                confidence = 0.95
            elif criteria_count == 4:
                grade = "A"
                confidence = 0.85
            elif criteria_count == 3:
                grade = "B"
                confidence = 0.75
            elif criteria_count == 2:
                grade = "C"
                confidence = 0.65
            else:
                grade = "D"
                confidence = 0.50
            
            return grade, confidence, criteria_count
            
        except Exception as e:
            error(f"Error assigning grade: {e}")
            return "F", 0.0, 0
    
    async def scan_symbol(self, symbol: str, timeframe: str = "15min") -> Optional[PandasTASetup]:
        """Scan a single symbol using your pandas_ta pattern analysis."""
        try:
            if not PANDAS_TA_AVAILABLE:
                return None
            
            # Get market data
            df = await self.get_market_data(symbol, timeframe, 100)
            
            if df.empty or len(df) < 50:
                return None
            
            # Calculate signal
            signal_series = self.calculate_signal(df)
            
            # Check if current signal is active
            current_signal = signal_series.iloc[-1] if len(signal_series) > 0 else 0
            
            if not current_signal:
                return None
            
            # Get signal components
            components = self.get_signal_components(df)
            
            # Calculate trade levels
            entry_price, stop_loss, target_price = self.calculate_trade_levels(df)
            
            # Calculate risk/reward ratio
            risk = abs(entry_price - stop_loss)
            reward = abs(target_price - entry_price)
            risk_reward_ratio = reward / risk if risk > 0 else 0.0
            
            # Assign grade
            grade, confidence, criteria_count = self.assign_grade(components)
            
            # Create setup
            setup = PandasTASetup(
                symbol=symbol,
                timeframe=timeframe,
                timestamp=datetime.now(),
                price=df['close'].iloc[-1],
                volume=df['volume'].iloc[-1],
                ema5_rising=components.get("ema5_rising", False),
                ema8_rising=components.get("ema8_rising", False),
                momentum_rising=components.get("momentum_rising", False),
                histogram_rising=components.get("histogram_rising", False),
                five_dots=components.get("five_dots", False),
                signal=bool(current_signal),
                ema5_value=components.get("ema5_value", 0.0),
                ema8_value=components.get("ema8_value", 0.0),
                momentum_value=components.get("momentum_value", 0.0),
                histogram_value=components.get("histogram_value", 0.0),
                entry_price=entry_price,
                stop_loss=stop_loss,
                target_price=target_price,
                risk_reward_ratio=risk_reward_ratio,
                grade=grade,
                confidence=confidence,
                criteria_count=criteria_count
            )
            
            return setup
            
        except Exception as e:
            error(f"Error scanning {symbol}: {e}")
            return None
    
    async def scan_multiple_symbols(self, symbols: List[str], timeframe: str = "15min",
                                   max_concurrent: int = 10, progress_callback=None) -> List[PandasTASetup]:
        """Scan multiple symbols concurrently and return all setups found."""
        try:
            setups = []
            total_symbols = len(symbols)

            info(f"🚀 Starting comprehensive scan of {total_symbols} symbols...")

            # Create semaphore to limit concurrent scans
            semaphore = asyncio.Semaphore(max_concurrent)

            async def scan_with_semaphore(symbol, index):
                async with semaphore:
                    try:
                        if progress_callback:
                            progress_callback(index, total_symbols, symbol)

                        setup = await self.scan_symbol(symbol, timeframe)

                        if setup:
                            info(f"✅ {symbol}: Grade {setup.grade}, Confidence {setup.confidence:.1%}")
                            return setup
                        else:
                            # Only log for priority symbols to reduce noise
                            if symbol in ['PLTR', 'AAPL']:
                                info(f"⚪ {symbol}: No signal from pattern")
                            return None
                    except Exception as e:
                        warning(f"Error scanning {symbol}: {e}")
                        return None

            # Execute concurrent scans
            tasks = [scan_with_semaphore(symbol, i) for i, symbol in enumerate(symbols)]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Collect successful setups
            for result in results:
                if isinstance(result, PandasTASetup):
                    setups.append(result)
                elif isinstance(result, Exception):
                    warning(f"Scan exception: {result}")

            info(f"✅ Comprehensive scan completed: {len(setups)} setups found from {total_symbols} symbols")
            return setups

        except Exception as e:
            error(f"Error in comprehensive symbol scanning: {e}")
            return []

    def get_comprehensive_symbol_list(self) -> List[str]:
        """Get comprehensive list of symbols to scan using symbol manager."""
        try:
            # Try to import symbol manager
            from core.symbol_manager import get_symbol_manager, get_trading_symbols

            # Get comprehensive symbol list
            symbols = get_trading_symbols()

            # Ensure priority symbols are included
            priority_symbols = ['PLTR', 'AAPL']
            for symbol in priority_symbols:
                if symbol not in symbols:
                    symbols.append(symbol)

            info(f"📊 Retrieved {len(symbols)} symbols from symbol manager")
            return symbols

        except ImportError:
            warning("Symbol manager not available, using fallback symbol list")
            return self._get_fallback_comprehensive_symbols()
        except Exception as e:
            error(f"Error getting comprehensive symbol list: {e}")
            return self._get_fallback_comprehensive_symbols()

    def _get_fallback_comprehensive_symbols(self) -> List[str]:
        """Fallback comprehensive symbol list when symbol manager is not available."""
        # S&P 500 major constituents + large cap stocks
        return [
            # Technology (Large Cap)
            'AAPL', 'MSFT', 'GOOGL', 'GOOG', 'AMZN', 'NVDA', 'META', 'TSLA', 'AVGO', 'ORCL',
            'CRM', 'ADBE', 'NFLX', 'AMD', 'INTC', 'CSCO', 'ACN', 'TXN', 'QCOM', 'IBM',
            'INTU', 'AMAT', 'MU', 'ADI', 'LRCX', 'KLAC', 'MCHP', 'CDNS', 'SNPS', 'FTNT',
            'NOW', 'PANW', 'CRWD', 'ZS', 'OKTA', 'DDOG', 'NET', 'SNOW', 'PLTR', 'RBLX',

            # Healthcare (Large Cap)
            'UNH', 'JNJ', 'PFE', 'ABBV', 'TMO', 'ABT', 'DHR', 'MRK', 'BMY', 'AMGN',
            'GILD', 'VRTX', 'REGN', 'ISRG', 'ZTS', 'CVS', 'CI', 'HUM', 'ANTM', 'MOH',
            'LLY', 'MRNA', 'BNTX', 'BIIB', 'ILMN', 'DXCM', 'EW', 'SYK', 'BSX', 'MDT',

            # Financials (Large Cap)
            'JPM', 'BAC', 'WFC', 'GS', 'MS', 'C', 'BLK', 'SPGI', 'AXP', 'USB',
            'TFC', 'PNC', 'COF', 'SCHW', 'CB', 'MMC', 'ICE', 'CME', 'AON', 'AJG',
            'V', 'MA', 'PYPL', 'SQ', 'FIS', 'FISV', 'ADP', 'INTU', 'TRV', 'ALL',

            # Consumer Discretionary (Large Cap)
            'HD', 'MCD', 'NKE', 'LOW', 'SBUX', 'TJX', 'BKNG', 'ORLY', 'MAR', 'GM',
            'F', 'CCL', 'RCL', 'NCLH', 'MGM', 'WYNN', 'LVS', 'CZR', 'DIS', 'CMCSA',
            'NFLX', 'ROKU', 'SPOT', 'UBER', 'LYFT', 'ABNB', 'DASH', 'ETSY', 'EBAY', 'AMZN',

            # Consumer Staples (Large Cap)
            'PG', 'KO', 'PEP', 'WMT', 'COST', 'MDLZ', 'CL', 'KMB', 'GIS', 'K',
            'HSY', 'MKC', 'SJM', 'CAG', 'CPB', 'HRL', 'TSN', 'TAP', 'KHC', 'CHD',
            'EL', 'CLX', 'SYY', 'KR', 'TGT', 'DG', 'DLTR', 'BJ', 'CTSH', 'WBA',

            # Energy (Large Cap)
            'XOM', 'CVX', 'COP', 'EOG', 'SLB', 'PSX', 'VLO', 'MPC', 'OXY', 'BKR',
            'HAL', 'DVN', 'FANG', 'EQT', 'CTRA', 'MRO', 'APA', 'OVV', 'MTDR', 'SM',
            'KMI', 'OKE', 'WMB', 'EPD', 'ET', 'MPLX', 'TRGP', 'ENB', 'TRP', 'SU',

            # Industrials (Large Cap)
            'BA', 'CAT', 'HON', 'UNP', 'RTX', 'LMT', 'DE', 'UPS', 'GE', 'MMM',
            'NOC', 'GD', 'FDX', 'NSC', 'CSX', 'WM', 'EMR', 'ETN', 'ITW', 'PH',
            'LUV', 'DAL', 'UAL', 'AAL', 'JBLU', 'ALK', 'SAVE', 'HA', 'MESA', 'SKYW',

            # Materials (Large Cap)
            'LIN', 'APD', 'SHW', 'FCX', 'NEM', 'DOW', 'DD', 'PPG', 'ECL', 'IFF',
            'ALB', 'CE', 'FMC', 'VMC', 'MLM', 'PKG', 'AMCR', 'IP', 'AVY', 'SEE',
            'NUE', 'STLD', 'X', 'CLF', 'MT', 'TX', 'VALE', 'RIO', 'BHP', 'SCCO',

            # Real Estate (Large Cap)
            'AMT', 'PLD', 'CCI', 'EQIX', 'PSA', 'WELL', 'DLR', 'O', 'SBAC', 'EXR',
            'AVB', 'EQR', 'VTR', 'ESS', 'MAA', 'KIM', 'REG', 'BXP', 'ARE', 'UDR',
            'SPG', 'SLG', 'VNO', 'KRC', 'HIW', 'DEI', 'CUZ', 'JBGS', 'PGRE', 'SHO',

            # Utilities (Large Cap)
            'NEE', 'SO', 'DUK', 'AEP', 'SRE', 'D', 'PEG', 'EXC', 'XEL', 'ED',
            'WEC', 'AWK', 'DTE', 'ES', 'FE', 'EIX', 'ETR', 'CMS', 'CNP', 'NI',
            'PPL', 'AEE', 'LNT', 'EVRG', 'PNW', 'IDA', 'PCG', 'SCG', 'SWX', 'UGI',

            # Communication Services (Large Cap)
            'T', 'VZ', 'CHTR', 'TMUS', 'ATVI', 'EA', 'TTWO', 'ZNGA', 'MTCH', 'NTES',
            'BIDU', 'JD', 'PDD', 'BABA', 'TME', 'BILI', 'IQ', 'VIPS', 'WB', 'SINA',

            # ETFs and Indices
            'SPY', 'QQQ', 'IWM', 'DIA', 'VTI', 'VOO', 'VEA', 'VWO', 'BND', 'AGG',
            'XLF', 'XLK', 'XLE', 'XLV', 'XLI', 'XLP', 'XLU', 'XLB', 'XLRE', 'XLC'
        ]


# Convenience functions for integration
async def scan_with_pandas_ta(symbols: List[str] = None, timeframe: str = "15min",
                             max_concurrent: int = 10, progress_callback=None) -> List[Dict[str, Any]]:
    """Scan symbols using pandas_ta pattern and return results as dictionaries."""
    try:
        scanner = PandasTATTMScanner()

        # If no symbols provided, get comprehensive list
        if symbols is None:
            symbols = scanner.get_comprehensive_symbol_list()

        setups = await scanner.scan_multiple_symbols(symbols, timeframe, max_concurrent, progress_callback)

        # Convert to dictionaries for compatibility
        return [setup.to_dict() for setup in setups]

    except Exception as e:
        error(f"Error in pandas_ta scan: {e}")
        return []

async def scan_comprehensive_pandas_ta(timeframe: str = "15min", max_concurrent: int = 10,
                                      progress_callback=None) -> List[Dict[str, Any]]:
    """Scan comprehensive symbol universe using pandas_ta pattern."""
    try:
        scanner = PandasTATTMScanner()

        # Get comprehensive symbol list
        symbols = scanner.get_comprehensive_symbol_list()
        info(f"🚀 Starting comprehensive pandas_ta scan of {len(symbols)} symbols...")

        setups = await scanner.scan_multiple_symbols(symbols, timeframe, max_concurrent, progress_callback)

        # Convert to dictionaries for compatibility
        results = [setup.to_dict() for setup in setups]

        info(f"✅ Comprehensive pandas_ta scan completed: {len(results)} setups found")
        return results

    except Exception as e:
        error(f"Error in comprehensive pandas_ta scan: {e}")
        return []


def test_pandas_ta_scanner():
    """Test the pandas_ta scanner with comprehensive symbol list."""
    import asyncio

    async def run_test():
        print("🧪 Testing pandas_ta TTM Scanner with comprehensive symbol universe...")

        def progress_callback(current, total, symbol):
            if current % 50 == 0 or current < 10:  # Show progress every 50 symbols or first 10
                print(f"📊 Progress: {current+1}/{total} - Scanning {symbol}")

        # Test comprehensive scan
        results = await scan_comprehensive_pandas_ta(
            timeframe="15min",
            max_concurrent=15,
            progress_callback=progress_callback
        )

        print(f"\n📊 COMPREHENSIVE SCAN RESULTS:")
        print(f"Total symbols scanned: {len(results) if results else 'Unknown'}")
        print(f"Setups found: {len([r for r in results if r]) if results else 0}")

        if results:
            # Show results by grade
            grade_counts = {}
            for result in results:
                grade = result.get('grade', 'Unknown')
                grade_counts[grade] = grade_counts.get(grade, 0) + 1

            print(f"\n📈 Results by Grade:")
            for grade in ['A+', 'A', 'B', 'C', 'D']:
                count = grade_counts.get(grade, 0)
                if count > 0:
                    print(f"   {grade}: {count} setups")

            # Show top setups
            high_grade_setups = [r for r in results if r.get('grade') in ['A+', 'A', 'B']]
            if high_grade_setups:
                print(f"\n🎯 Top Setups Found:")
                for setup in high_grade_setups[:10]:  # Show top 10
                    print(f"✅ {setup['symbol']}: Grade {setup['grade']}, "
                          f"Confidence {setup['confidence']:.1%}, "
                          f"Entry ${setup['entry_price']:.2f}")
        else:
            print("⚪ No setups found matching the 5-criteria pattern")

    asyncio.run(run_test())


if __name__ == "__main__":
    test_pandas_ta_scanner()
