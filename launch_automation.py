#!/usr/bin/env python3
"""Launch TTM Automation System

Simple launcher to start the TTM automation system with proper imports.
This script handles all the path configuration automatically.
"""
import sys
import os

def setup_paths():
    """Setup Python paths for all modules."""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Add core and gui directories to path
    paths_to_add = [
        current_dir,
        os.path.join(current_dir, 'core'),
        os.path.join(current_dir, 'gui'),
    ]
    
    for path in paths_to_add:
        if os.path.exists(path) and path not in sys.path:
            sys.path.insert(0, path)

def test_automation_system():
    """Test the automation system components."""
    print("🤖 **TTM AUTOMATION SYSTEM TEST**")
    print("=" * 50)
    
    # Test automation engine
    try:
        from automation_engine import AutomationEngine, AutomationMode
        engine = AutomationEngine()
        print("✅ Automation Engine: Available")
        
        # Test starting conservative mode
        result = engine.start_automation(AutomationMode.CONSERVATIVE)
        if "STARTED" in result:
            print("✅ Conservative Mode: Working")
            engine.stop_automation()
        else:
            print("⚠️ Conservative Mode: Issue detected")
            
    except Exception as e:
        print(f"❌ Automation Engine: Error - {e}")
    
    # Test automation control
    try:
        from automation_control import start_automation_conservative, get_automation_status
        print("✅ Automation Control: Available")
        
        # Test status function
        status = get_automation_status()
        if "AUTOMATION STATUS" in status:
            print("✅ Status Function: Working")
        else:
            print("⚠️ Status Function: Unexpected output")
            
    except Exception as e:
        print(f"❌ Automation Control: Error - {e}")
    
    # Test control panel
    try:
        from automation_control import AutomationControlPanel
        panel = AutomationControlPanel()
        print("✅ Control Panel: Available")
    except Exception as e:
        print(f"❌ Control Panel: Error - {e}")
    
    print("\n" + "=" * 50)

def launch_automation_panel():
    """Launch the automation control panel."""
    print("🚀 **LAUNCHING AUTOMATION CONTROL PANEL**")
    print("=" * 50)
    
    try:
        from automation_control import show_automation_control_panel
        print("✅ Opening automation control panel...")
        show_automation_control_panel()
        
        # Keep the script running
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # Hide the root window
        root.mainloop()
        
    except Exception as e:
        print(f"❌ Error launching control panel: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure all files are in the correct directories")
        print("2. Check that automation_engine.py and automation_control.py exist in core/")
        print("3. Try running: python main.py instead")

def launch_main_system():
    """Launch the main TTM trading system."""
    print("🚀 **LAUNCHING MAIN TTM SYSTEM**")
    print("=" * 40)
    
    try:
        # Try to launch the main GUI
        from tkinter_trading_interface import TradingInterface
        
        print("✅ Starting TTM Trading Interface...")
        interface = TradingInterface()
        interface.run()
        
    except Exception as e:
        print(f"❌ Error launching main system: {e}")
        print("\nTrying alternative launch method...")
        
        try:
            import subprocess
            subprocess.run([sys.executable, "main.py"])
        except Exception as e2:
            print(f"❌ Alternative launch failed: {e2}")
            print("\nManual launch instructions:")
            print("1. Open terminal/command prompt")
            print("2. Navigate to the TotalRecall directory")
            print("3. Run: python main.py")

def show_menu():
    """Show the launcher menu."""
    print("\n🤖 **TTM AUTOMATION SYSTEM LAUNCHER**")
    print("=" * 45)
    print()
    print("Choose an option:")
    print("1. 🧪 Test Automation System")
    print("2. 🎮 Launch Automation Control Panel")
    print("3. 🚀 Launch Main TTM System")
    print("4. ❌ Exit")
    print()
    
    while True:
        try:
            choice = input("Enter your choice (1-4): ").strip()
            
            if choice == "1":
                test_automation_system()
                break
            elif choice == "2":
                launch_automation_panel()
                break
            elif choice == "3":
                launch_main_system()
                break
            elif choice == "4":
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice. Please enter 1, 2, 3, or 4.")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

def main():
    """Main launcher function."""
    # Setup paths first
    setup_paths()
    
    # Show menu
    show_menu()

if __name__ == "__main__":
    main()
