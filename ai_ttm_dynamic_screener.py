#!/usr/bin/env python3
"""
AI TTM Dynamic Screener - Pattern-Based Filtering
Uses statistical patterns from training data for adaptive, flexible filtering
instead of rigid thresholds. Implements composite scoring and pattern alignment.
"""

import pandas as pd
import numpy as np
import talib
import json
import requests
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Try to import joblib with fallback
try:
    import joblib
    JOBLIB_AVAILABLE = True
except ImportError:
    JOBLIB_AVAILABLE = False

# Use existing FMP infrastructure
try:
    from core.config import get_api_key
except ImportError:
    try:
        from config import get_api_key
    except ImportError:
        def get_api_key(key_name):
            import os
            key = os.getenv(key_name)
            if key is None:
                raise KeyError(f"Environment variable '{key_name}' is not set.")
            return key

class DynamicTTMScreener:
    """Dynamic TTM screener with pattern-based filtering."""
    
    def __init__(self, model_path="ttm_ai_model.pkl", scaler_path="ttm_scaler.pkl", 
                 pattern_config_path="ttm_pattern_config.json"):
        self.model = None
        self.scaler = None
        self.pattern_config = None
        self.load_model(model_path, scaler_path)
        self.load_pattern_config(pattern_config_path)
        
    def load_model(self, model_path, scaler_path):
        """Load trained model and scaler."""
        if not JOBLIB_AVAILABLE:
            print("❌ joblib not available! Install with: pip install joblib")
            return
        
        try:
            self.model = joblib.load(model_path)
            self.scaler = joblib.load(scaler_path)
            print("✅ AI Model loaded successfully")
        except FileNotFoundError:
            print("❌ Model files not found! Run training first.")
        except Exception as e:
            print(f"❌ Error loading model: {e}")
    
    def load_pattern_config(self, config_path):
        """Load pattern configuration for dynamic filtering."""
        try:
            with open(config_path, 'r') as f:
                self.pattern_config = json.load(f)
            print("✅ Pattern configuration loaded successfully")
        except FileNotFoundError:
            print("❌ Pattern config not found! Run ai_ttm_pattern_analyzer.py first.")
        except Exception as e:
            print(f"❌ Error loading pattern config: {e}")
    
    def get_live_data(self, symbol, interval="1h"):
        """Get recent data using FMP API."""
        try:
            api_key = get_api_key("FMP_API_KEY")
            if not api_key:
                return None

            fmp_interval = "1hour" if interval == "1h" else "15min"
            url = f"https://financialmodelingprep.com/api/v3/historical-chart/{fmp_interval}/{symbol}"
            params = {"apikey": api_key}

            response = requests.get(url, params=params)
            if response.status_code != 200:
                return None

            data = response.json()
            if not data or len(data) < 50:
                return None

            df = pd.DataFrame(data)
            df['date'] = pd.to_datetime(df['date'])
            df.set_index('date', inplace=True)
            df.sort_index(inplace=True)

            df.rename(columns={
                'open': 'Open', 'high': 'High', 'low': 'Low', 
                'close': 'Close', 'volume': 'Volume'
            }, inplace=True)

            cutoff_date = datetime.now() - timedelta(days=7)
            df = df[df.index >= cutoff_date]

            return df if len(df) >= 50 else None

        except Exception:
            return None
    
    def calculate_features(self, df):
        """Calculate the same features used in training."""
        # Bollinger Bands & Keltner Channels
        df['bb_upper'], df['bb_middle'], df['bb_lower'] = talib.BBANDS(
            df['Close'], timeperiod=20, nbdevup=2, nbdevdn=2
        )
        df['ema20'] = talib.EMA(df['Close'], timeperiod=20)
        df['atr'] = talib.ATR(df['High'], df['Low'], df['Close'], timeperiod=20)
        df['kc_upper'] = df['ema20'] + (df['atr'] * 1.5)
        df['kc_lower'] = df['ema20'] - (df['atr'] * 1.5)
        
        # TTM Squeeze Detection
        df['ttm_squeeze_active'] = (
            (df['bb_upper'] <= df['kc_upper']) & 
            (df['bb_lower'] >= df['kc_lower'])
        ).astype(int)
        
        # MACD and Histogram
        df['macd'], df['macd_signal'], df['macd_histogram'] = talib.MACD(
            df['Close'], fastperiod=12, slowperiod=26, signalperiod=9
        )
        df['momentum_histogram'] = df['Close'] - df['ema20']
        
        # EMAs and crossovers
        df['ema5'] = talib.EMA(df['Close'], timeperiod=5)
        df['ema8'] = talib.EMA(df['Close'], timeperiod=8)
        df['ema21'] = talib.EMA(df['Close'], timeperiod=21)
        df['ema5_above_ema8'] = (df['ema5'] > df['ema8']).astype(int)
        df['ema8_above_ema21'] = (df['ema8'] > df['ema21']).astype(int)
        df['price_above_ema8'] = (df['Close'] > df['ema8']).astype(int)
        df['price_above_ema21'] = (df['Close'] > df['ema21']).astype(int)
        
        # Volume and momentum features
        df['volume_sma20'] = df['Volume'].rolling(20).mean()
        df['volume_ratio'] = df['Volume'] / df['volume_sma20']
        df['high_volume'] = (df['volume_ratio'] > 1.5).astype(int)
        df['green_candle'] = (df['Close'] > df['Open']).astype(int)
        df['last_3_candles_green'] = (df['green_candle'].rolling(3).sum() == 3).astype(int)
        df['true_range'] = talib.TRANGE(df['High'], df['Low'], df['Close'])
        df['volatility_ratio'] = df['true_range'] / df['Close']
        df['rsi'] = talib.RSI(df['Close'], timeperiod=14)
        df['momentum_rising'] = (
            df['momentum_histogram'] > df['momentum_histogram'].shift(1)
        ).astype(int)
        df['histogram_rising_3'] = (
            (df['momentum_histogram'] > df['momentum_histogram'].shift(1)) &
            (df['momentum_histogram'].shift(1) > df['momentum_histogram'].shift(2)) &
            (df['momentum_histogram'].shift(2) > df['momentum_histogram'].shift(3))
        ).astype(int)
        
        return df
    
    def calculate_pattern_alignment_score(self, latest_data):
        """Calculate composite score based on pattern alignment."""
        if self.pattern_config is None:
            return 0.5  # Default neutral score
        
        continuous_patterns = self.pattern_config['continuous_patterns']
        boolean_patterns = self.pattern_config['boolean_patterns']
        weights = self.pattern_config['scoring_weights']
        
        total_score = 0
        total_weight = 0
        
        # Score continuous features based on sweet spot alignment
        for feature, pattern in continuous_patterns.items():
            if feature not in latest_data.index:
                continue
            
            value = latest_data[feature]
            if pd.isna(value):
                continue
            
            sweet_spot_low, sweet_spot_high = pattern['sweet_spot_range']
            optimal_value = pattern['optimal_value']
            
            # Calculate alignment score (0-1)
            if sweet_spot_low <= value <= sweet_spot_high:
                # In sweet spot - score based on distance from optimal
                distance_from_optimal = abs(value - optimal_value)
                range_size = sweet_spot_high - sweet_spot_low
                alignment_score = 1.0 - (distance_from_optimal / (range_size / 2))
                alignment_score = max(0.5, min(1.0, alignment_score))  # Clamp to 0.5-1.0
            else:
                # Outside sweet spot - score based on distance from range
                if value < sweet_spot_low:
                    distance = sweet_spot_low - value
                else:
                    distance = value - sweet_spot_high
                
                # Gradual falloff instead of hard cutoff
                range_size = sweet_spot_high - sweet_spot_low
                falloff_factor = max(0.1, 1.0 - (distance / range_size))
                alignment_score = 0.5 * falloff_factor
            
            weight = weights['continuous_features'].get(feature, 0)
            total_score += alignment_score * weight
            total_weight += weight
        
        # Score boolean features based on preference alignment
        for feature, pattern in boolean_patterns.items():
            if feature not in latest_data.index:
                continue
            
            value = bool(latest_data[feature])
            preferred_state = pattern['preference'] == 'true'
            strength = pattern['strength']
            
            # Score based on alignment with preferred state
            if value == preferred_state:
                alignment_score = 0.5 + (strength / 2)  # 0.5 to 1.0
            else:
                alignment_score = 0.5 - (strength / 2)  # 0.0 to 0.5
            
            alignment_score = max(0.0, min(1.0, alignment_score))
            
            weight = weights['boolean_features'].get(feature, 0)
            total_score += alignment_score * weight
            total_weight += weight
        
        # Normalize final score
        final_score = total_score / total_weight if total_weight > 0 else 0.5
        return max(0.0, min(1.0, final_score))
    
    def predict_setup_success(self, df, symbol):
        """Use AI model and pattern alignment for comprehensive scoring."""
        if self.model is None:
            return None
        
        latest = df.iloc[-1]
        
        # Get AI prediction
        feature_cols = [
            'ttm_squeeze_active', 'momentum_histogram', 'ema5_above_ema8',
            'ema8_above_ema21', 'price_above_ema8', 'price_above_ema21',
            'last_3_candles_green', 'high_volume', 'volume_ratio',
            'volatility_ratio', 'rsi', 'momentum_rising', 'histogram_rising_3',
            'macd', 'macd_histogram', 'atr'
        ]
        
        features = latest[feature_cols].values.reshape(1, -1)
        if np.isnan(features).any():
            return None
        
        features_scaled = self.scaler.transform(features)
        ai_probability = self.model.predict_proba(features_scaled)[0, 1]
        
        # Get pattern alignment score
        pattern_score = self.calculate_pattern_alignment_score(latest)
        
        # Composite score: weighted combination of AI and pattern alignment
        composite_score = (ai_probability * 0.7) + (pattern_score * 0.3)
        
        # Create setup description with pattern insights
        setup_parts = []
        if latest['ttm_squeeze_active']:
            setup_parts.append("TTM Squeeze")
        if latest['histogram_rising_3']:
            setup_parts.append("Histogram Rising")
        if latest['ema5_above_ema8'] and latest['ema8_above_ema21']:
            setup_parts.append("EMA Stack")
        if latest['last_3_candles_green']:
            setup_parts.append("3 Green Candles")
        if latest['high_volume']:
            setup_parts.append("High Volume")
        
        setup_description = " + ".join(setup_parts) if setup_parts else "Basic Setup"
        
        # Determine confidence based on composite score
        if composite_score >= 0.8:
            confidence = "High"
        elif composite_score >= 0.6:
            confidence = "Moderate"
        else:
            confidence = "Low"
        
        return {
            "symbol": symbol,
            "setup": setup_description,
            "ai_score": round(ai_probability, 3),
            "pattern_score": round(pattern_score, 3),
            "composite_score": round(composite_score, 3),
            "confidence": confidence,
            "price": round(latest['Close'], 2),
            "volume_ratio": round(latest['volume_ratio'], 2),
            "squeeze_active": bool(latest['ttm_squeeze_active']),
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

    def scan_symbols(self, symbols, min_composite_score=0.5):
        """Scan symbols using dynamic pattern-based filtering."""
        print(f"🤖 Dynamic TTM Screener scanning {len(symbols)} symbols...")
        print(f"🔍 Using pattern-based filtering (min composite score: {min_composite_score:.1%})")

        opportunities = []

        for i, symbol in enumerate(symbols):
            print(f"📊 Scanning {symbol} ({i+1}/{len(symbols)})...")

            # Get data
            df = self.get_live_data(symbol)
            if df is None:
                continue

            # Calculate features
            df = self.calculate_features(df)

            # Get prediction with pattern alignment
            prediction = self.predict_setup_success(df, symbol)
            if prediction is None:
                continue

            # Filter by composite score (flexible threshold)
            if prediction['composite_score'] >= min_composite_score:
                opportunities.append(prediction)

        # Sort by composite score (highest first)
        opportunities.sort(key=lambda x: x['composite_score'], reverse=True)

        return opportunities

    def format_results(self, opportunities):
        """Format results showing both AI and pattern scores."""
        if not opportunities:
            return "🤖 No setups found meeting dynamic pattern alignment criteria."

        result = f"🤖 DYNAMIC TTM SCREENER RESULTS ({len(opportunities)} opportunities):\n"
        result += f"🧠 Scoring: AI Prediction (70%) + Pattern Alignment (30%) = Composite Score\n\n"

        for i, opp in enumerate(opportunities, 1):
            confidence_emoji = "🔥" if opp['confidence'] == "High" else "⭐" if opp['confidence'] == "Moderate" else "📊"
            squeeze_emoji = "🎁" if opp['squeeze_active'] else ""

            result += f"{confidence_emoji} #{i} {opp['symbol']} - Composite: {opp['composite_score']:.1%} ({opp['confidence']})\n"
            result += f"   AI: {opp['ai_score']:.1%} | Pattern: {opp['pattern_score']:.1%} | Setup: {opp['setup']} {squeeze_emoji}\n"
            result += f"   Price: ${opp['price']} | Volume: {opp['volume_ratio']:.1f}x | Time: {opp['timestamp']}\n\n"

        return result


def main():
    """Main dynamic screening execution."""
    print("🤖 AI TTM DYNAMIC SCREENER - PATTERN-BASED FILTERING")
    print("=" * 70)

    # Test symbols for demonstration
    symbols = [
        'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'TSLA', 'META', 'NFLX',
        'AMD', 'INTC', 'ORCL', 'CRM', 'ADBE', 'PYPL', 'CSCO', 'QCOM'
    ]

    # Initialize dynamic screener
    screener = DynamicTTMScreener()

    if screener.model is None or screener.pattern_config is None:
        print("❌ Required files not found!")
        print("🔧 Run ai_ttm_model_trainer.py and ai_ttm_pattern_analyzer.py first")
        return

    # Scan with flexible threshold (50% composite score)
    opportunities = screener.scan_symbols(symbols, min_composite_score=0.5)

    # Display results
    results = screener.format_results(opportunities)
    print(results)

    # Save results
    if opportunities:
        with open(f"dynamic_ttm_results_{datetime.now().strftime('%Y%m%d_%H%M')}.json", 'w') as f:
            json.dump(opportunities, f, indent=2)
        print(f"💾 Results saved to JSON file")

    print(f"\n🎯 Dynamic scan complete! Found {len(opportunities)} pattern-aligned setups")
    print(f"🧠 Pattern-based filtering adapts to market conditions vs rigid thresholds")


if __name__ == "__main__":
    main()
