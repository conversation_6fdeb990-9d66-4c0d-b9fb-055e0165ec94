"""Paper Trading System for TTM Squeeze

This system executes real TTM squeeze trades in simulation mode:
- Finds actual TTM squeeze setups
- Executes paper trades with real position sizing
- Tracks P&L and performance
- Implements trailing stops
- Provides detailed trade reports
- Safe testing environment
"""
from __future__ import annotations

import time
import threading
from datetime import datetime
from typing import Dict, List, Optional
import requests

from config import get_api_key
from logger_util import info, warning
from ttm_squeeze_watchlist import TTMSqueezeWatchlist
from ttm_options_specialist import get_ttm_options_specialist


class PaperTradingSystem:
    """Complete paper trading system for TTM squeeze strategies."""
    
    def __init__(self, starting_capital: float = 10000):
        self.api_key = get_api_key('FMP_API_KEY')
        self.ttm_watchlist = TTMSqueezeWatchlist()
        self.ttm_specialist = get_ttm_options_specialist()
        
        # Account settings
        self.starting_capital = starting_capital
        self.current_capital = starting_capital
        self.available_cash = starting_capital
        
        # Risk management
        self.max_risk_per_trade = 0.02  # 2% max risk per trade
        self.max_position_size = 0.10   # 10% max position size
        self.trailing_stop_pct = 0.03   # 3% trailing stop
        
        # Trading state
        self.active_positions = {}
        self.closed_trades = []
        self.trade_counter = 0
        self.monitoring = False
        
        info(f"📊 Paper Trading System initialized with ${starting_capital:,.2f}")
    
    def find_best_ttm_setup(self) -> Optional[Dict]:
        """Find the most promising TTM squeeze setup."""
        info("🔍 Scanning for best TTM squeeze setup...")
        
        try:
            # Update watchlist
            self.ttm_watchlist.update_watchlist()
            
            # Get candidates
            candidates = self.ttm_watchlist.get_top_breakout_candidates(15)
            
            if not candidates:
                return None
            
            # Score and find best
            best_setup = None
            best_score = 0
            
            for candidate in candidates:
                score = self._score_setup(candidate)
                if score > best_score and score >= 70:  # Minimum quality threshold
                    best_score = score
                    best_setup = candidate
            
            if best_setup:
                info(f"🎯 Best setup: {best_setup['symbol']} (Score: {best_score})")
                return best_setup
            
            return None
            
        except Exception as e:
            warning(f"Error finding TTM setup: {e}")
            return None
    
    def _score_setup(self, setup: Dict) -> int:
        """Score a TTM setup for trading quality."""
        score = setup.get('breakout_score', 0)
        
        # Volume bonus
        volume_ratio = setup.get('volume_ratio', 1.0)
        if volume_ratio > 2.0:
            score += 15
        elif volume_ratio > 1.5:
            score += 10
        elif volume_ratio > 1.2:
            score += 5
        
        # Squeeze duration bonus
        duration = setup.get('squeeze_duration', 0)
        if duration > 20:
            score += 15
        elif duration > 15:
            score += 10
        elif duration > 10:
            score += 5
        
        # Timeframe preference
        if setup.get('timeframe') == '15min':
            score += 5
        
        return min(score, 100)
    
    def execute_paper_trade(self, setup: Dict) -> bool:
        """Execute a paper trade based on TTM setup."""
        symbol = setup['symbol']
        current_price = setup['price']
        
        if symbol in self.active_positions:
            info(f"⏭️ Skipping {symbol} - already have position")
            return False
        
        info(f"🎯 Executing paper trade for {symbol} at ${current_price:.2f}")
        
        try:
            # Calculate position size
            position_value = self._calculate_position_size(current_price)
            shares = int(position_value / current_price)
            actual_value = shares * current_price
            
            if shares == 0 or actual_value > self.available_cash:
                warning(f"❌ Insufficient capital for {symbol}")
                return False
            
            # Calculate stops and targets
            stop_loss = current_price * (1 - self.trailing_stop_pct)
            take_profit = current_price + (2 * (current_price - stop_loss))  # 2:1 R/R
            
            # Create trade record
            self.trade_counter += 1
            trade = {
                'trade_id': self.trade_counter,
                'symbol': symbol,
                'side': 'BUY',
                'shares': shares,
                'entry_price': current_price,
                'entry_time': datetime.now(),
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'trailing_stop_pct': self.trailing_stop_pct,
                'position_value': actual_value,
                'setup_data': setup,
                'status': 'OPEN'
            }
            
            # Update account
            self.available_cash -= actual_value
            self.active_positions[symbol] = trade
            
            # Log trade details
            risk_amount = shares * (current_price - stop_loss)
            potential_profit = shares * (take_profit - current_price)
            
            info(f"✅ PAPER TRADE EXECUTED:")
            info(f"   Trade ID: {self.trade_counter}")
            info(f"   Symbol: {symbol}")
            info(f"   Shares: {shares:,}")
            info(f"   Entry: ${current_price:.2f}")
            info(f"   Position Value: ${actual_value:,.2f}")
            info(f"   Stop Loss: ${stop_loss:.2f}")
            info(f"   Take Profit: ${take_profit:.2f}")
            info(f"   Risk: ${risk_amount:.2f}")
            info(f"   Potential Profit: ${potential_profit:.2f}")
            info(f"   Available Cash: ${self.available_cash:,.2f}")
            
            return True
            
        except Exception as e:
            warning(f"Error executing paper trade for {symbol}: {e}")
            return False
    
    def _calculate_position_size(self, price: float) -> float:
        """Calculate position size based on risk management."""
        # Risk-based sizing
        risk_amount = self.current_capital * self.max_risk_per_trade  # $200 for $10k account
        stop_distance = price * self.trailing_stop_pct  # 3% stop distance
        risk_based_size = risk_amount / stop_distance

        # Position size limit (10% of account max)
        max_position = self.current_capital * self.max_position_size  # $1000 max

        # Use smaller of the two, but ensure minimum viable trade
        position_size = min(risk_based_size, max_position)

        # For high-priced stocks, use a minimum position size
        min_position = min(500, self.available_cash * 0.05)  # At least $500 or 5% of cash
        position_size = max(position_size, min_position)

        # Ensure we don't exceed available cash
        final_size = min(position_size, self.available_cash * 0.90)  # Leave 10% buffer

        return max(final_size, 100)  # Absolute minimum $100 position
    
    def monitor_positions(self):
        """Monitor active positions and manage stops."""
        self.monitoring = True
        info("👁️ Starting position monitoring...")
        
        while self.monitoring and self.active_positions:
            try:
                for symbol in list(self.active_positions.keys()):
                    self._check_position(symbol)
                
                time.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                warning(f"Error monitoring positions: {e}")
                time.sleep(60)
        
        info("🔚 Position monitoring stopped")
    
    def _check_position(self, symbol: str):
        """Check and update a position."""
        if symbol not in self.active_positions:
            return
        
        position = self.active_positions[symbol]
        
        try:
            # Get current price
            current_price = self._get_current_price(symbol)
            if not current_price:
                return
            
            entry_price = position['entry_price']
            current_stop = position['stop_loss']
            take_profit = position['take_profit']
            shares = position['shares']
            
            # Update trailing stop (only move up for long positions)
            new_stop = current_price * (1 - self.trailing_stop_pct)
            if new_stop > current_stop:
                old_stop = current_stop
                position['stop_loss'] = new_stop
                profit_protected = (new_stop - entry_price) * shares
                info(f"📈 {symbol}: Trailing stop updated ${old_stop:.2f} → ${new_stop:.2f}")
                info(f"   Profit protected: ${profit_protected:.2f}")
            
            # Check exit conditions
            current_pnl = (current_price - entry_price) * shares
            
            if current_price <= position['stop_loss']:
                self._close_position(symbol, current_price, "STOP LOSS")
            elif current_price >= take_profit:
                self._close_position(symbol, current_price, "TAKE PROFIT")
            else:
                # Log current status
                pnl_pct = ((current_price - entry_price) / entry_price) * 100
                info(f"📊 {symbol}: ${current_price:.2f} | P&L: ${current_pnl:.2f} ({pnl_pct:+.1f}%)")
                
        except Exception as e:
            warning(f"Error checking position {symbol}: {e}")
    
    def _get_current_price(self, symbol: str) -> Optional[float]:
        """Get current price for a symbol."""
        try:
            url = f"https://financialmodelingprep.com/api/v3/quote/{symbol}?apikey={self.api_key}"
            response = requests.get(url, timeout=10)
            data = response.json()
            
            if data and len(data) > 0:
                return float(data[0]['price'])
            return None
            
        except Exception as e:
            warning(f"Error getting price for {symbol}: {e}")
            return None
    
    def _close_position(self, symbol: str, exit_price: float, reason: str):
        """Close a position."""
        if symbol not in self.active_positions:
            return
        
        position = self.active_positions[symbol]
        
        # Calculate P&L
        entry_price = position['entry_price']
        shares = position['shares']
        gross_pnl = (exit_price - entry_price) * shares
        
        # Update position record
        position['exit_price'] = exit_price
        position['exit_time'] = datetime.now()
        position['exit_reason'] = reason
        position['gross_pnl'] = gross_pnl
        position['status'] = 'CLOSED'
        
        # Update account
        proceeds = shares * exit_price
        self.available_cash += proceeds
        self.current_capital = self.available_cash + self._get_open_positions_value()
        
        # Move to closed trades
        self.closed_trades.append(position)
        del self.active_positions[symbol]
        
        # Calculate performance metrics
        pnl_pct = (gross_pnl / position['position_value']) * 100
        hold_time = position['exit_time'] - position['entry_time']
        
        info(f"🔚 POSITION CLOSED:")
        info(f"   Symbol: {symbol}")
        info(f"   Reason: {reason}")
        info(f"   Entry: ${entry_price:.2f} → Exit: ${exit_price:.2f}")
        info(f"   Shares: {shares:,}")
        info(f"   Gross P&L: ${gross_pnl:.2f} ({pnl_pct:+.2f}%)")
        info(f"   Hold Time: {hold_time}")
        info(f"   New Capital: ${self.current_capital:,.2f}")
    
    def _get_open_positions_value(self) -> float:
        """Get total value of open positions."""
        total_value = 0
        for symbol, position in self.active_positions.items():
            current_price = self._get_current_price(symbol)
            if current_price:
                total_value += position['shares'] * current_price
        return total_value
    
    def get_performance_report(self) -> str:
        """Generate performance report."""
        total_trades = len(self.closed_trades)
        if total_trades == 0:
            return "📊 No completed trades yet"
        
        # Calculate metrics
        winning_trades = [t for t in self.closed_trades if t['gross_pnl'] > 0]
        losing_trades = [t for t in self.closed_trades if t['gross_pnl'] <= 0]
        
        total_pnl = sum(t['gross_pnl'] for t in self.closed_trades)
        win_rate = len(winning_trades) / total_trades * 100
        
        avg_win = sum(t['gross_pnl'] for t in winning_trades) / len(winning_trades) if winning_trades else 0
        avg_loss = sum(t['gross_pnl'] for t in losing_trades) / len(losing_trades) if losing_trades else 0
        
        total_return = ((self.current_capital - self.starting_capital) / self.starting_capital) * 100
        
        report = f"""
📊 PAPER TRADING PERFORMANCE REPORT
{'='*50}
💰 ACCOUNT SUMMARY:
   Starting Capital: ${self.starting_capital:,.2f}
   Current Capital: ${self.current_capital:,.2f}
   Available Cash: ${self.available_cash:,.2f}
   Total Return: {total_return:+.2f}%
   Total P&L: ${total_pnl:+,.2f}

📈 TRADING STATISTICS:
   Total Trades: {total_trades}
   Winning Trades: {len(winning_trades)}
   Losing Trades: {len(losing_trades)}
   Win Rate: {win_rate:.1f}%
   
   Average Win: ${avg_win:.2f}
   Average Loss: ${avg_loss:.2f}
   Profit Factor: {abs(avg_win/avg_loss) if avg_loss != 0 else 'N/A'}

🎯 ACTIVE POSITIONS: {len(self.active_positions)}
"""
        
        # Add active positions
        if self.active_positions:
            report += "\n📊 OPEN POSITIONS:\n"
            for symbol, pos in self.active_positions.items():
                current_price = self._get_current_price(symbol)
                if current_price:
                    unrealized_pnl = (current_price - pos['entry_price']) * pos['shares']
                    pnl_pct = ((current_price - pos['entry_price']) / pos['entry_price']) * 100
                    report += f"   {symbol}: ${current_price:.2f} | P&L: ${unrealized_pnl:+.2f} ({pnl_pct:+.1f}%)\n"
        
        return report
    
    def start_automated_trading(self, max_positions: int = 3):
        """Start automated paper trading."""
        info(f"🚀 Starting automated paper trading (max {max_positions} positions)")
        
        # Start monitoring thread
        monitor_thread = threading.Thread(target=self.monitor_positions, daemon=True)
        monitor_thread.start()
        
        try:
            while True:
                # Check if we can take new positions
                if len(self.active_positions) < max_positions:
                    setup = self.find_best_ttm_setup()
                    if setup:
                        self.execute_paper_trade(setup)
                
                # Show status
                if self.active_positions:
                    info(f"📊 Active positions: {len(self.active_positions)} | Capital: ${self.current_capital:,.2f}")
                
                # Wait before next scan
                time.sleep(300)  # 5 minutes
                
        except KeyboardInterrupt:
            info("🛑 Automated trading stopped by user")
        finally:
            self.monitoring = False


def run_paper_trading_demo():
    """Run paper trading demonstration."""
    print("📊 PAPER TRADING SYSTEM - TTM SQUEEZE STRATEGIES")
    print("=" * 60)
    
    # Create paper trading system
    paper_trader = PaperTradingSystem(starting_capital=10000)
    
    print(f"🎯 FINDING AND EXECUTING TTM SQUEEZE TRADE...")
    
    # Find and execute one trade
    setup = paper_trader.find_best_ttm_setup()
    
    if setup:
        success = paper_trader.execute_paper_trade(setup)
        
        if success:
            print(f"\n✅ Paper trade executed successfully!")
            
            # Start monitoring for a short demo
            print(f"\n👁️ Monitoring position for 2 minutes...")
            paper_trader.monitoring = True
            
            # Monitor for 2 minutes
            for i in range(4):  # 4 x 30 seconds = 2 minutes
                for symbol in list(paper_trader.active_positions.keys()):
                    paper_trader._check_position(symbol)
                time.sleep(30)
            
            paper_trader.monitoring = False
            
            # Show performance report
            print(paper_trader.get_performance_report())
            
        else:
            print(f"❌ Paper trade execution failed")
    else:
        print(f"⏳ No qualifying TTM setups found")
    
    return paper_trader


if __name__ == "__main__":
    paper_trader = run_paper_trading_demo()
    
    print(f"\n🚀 TO CONTINUE AUTOMATED PAPER TRADING:")
    print(f"   paper_trader.start_automated_trading()")
    print(f"\n📊 TO GET PERFORMANCE REPORT:")
    print(f"   print(paper_trader.get_performance_report())")
