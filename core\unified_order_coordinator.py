#!/usr/bin/env python3
"""
Unified Order Coordinator for TotalRecall Enhanced

This module prevents conflicts between TTM automation and MCP integration
by providing centralized order management and duplicate prevention.

ESSENTIAL FUNCTIONALITY ONLY - No UI bloat, pure trading safety.
"""

import time
import threading
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

class OrderSource(Enum):
    TTM_AUTOMATION = "ttm_automation"
    MCP_MANUAL = "mcp_manual"
    DIRECT_MANUAL = "direct_manual"

class OrderStatus(Enum):
    PENDING = "pending"
    ACTIVE = "active"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"

@dataclass
class UnifiedOrder:
    """Represents an order from any source in the system."""
    order_id: str
    symbol: str
    side: str  # "buy" or "sell"
    quantity: int
    order_type: str  # "market", "limit", etc.
    price: Optional[float]
    source: OrderSource
    timestamp: datetime
    status: OrderStatus
    alpaca_order_id: Optional[str] = None

class UnifiedOrderCoordinator:
    """
    Centralized order coordination to prevent conflicts between systems.
    
    CORE FUNCTIONS:
    1. Prevent duplicate orders for same symbol
    2. Track all orders from all sources
    3. Coordinate position management
    4. Provide conflict warnings
    """
    
    def __init__(self):
        self.active_orders: Dict[str, UnifiedOrder] = {}
        self.order_history: List[UnifiedOrder] = []
        self.position_locks: Dict[str, OrderSource] = {}  # symbol -> source that owns position
        self.lock = threading.Lock()
        
        # Safety limits
        self.max_orders_per_symbol = 2  # Prevent order spam
        self.duplicate_order_window = 30  # seconds
        
    def check_order_conflicts(self, symbol: str, side: str, quantity: int, 
                            source: OrderSource) -> Tuple[bool, str]:
        """
        Check if proposed order conflicts with existing orders/positions.
        
        Returns: (is_safe, warning_message)
        """
        with self.lock:
            warnings = []
            
            # Check for duplicate orders in same direction
            recent_orders = self._get_recent_orders(symbol, side)
            if recent_orders:
                for order in recent_orders:
                    if order.source != source:
                        warnings.append(f"⚠️ {order.source.value} already has {side} order for {symbol}")
            
            # Check position ownership
            if symbol in self.position_locks:
                owner = self.position_locks[symbol]
                if owner != source:
                    warnings.append(f"⚠️ {symbol} position managed by {owner.value}")
            
            # Check order count for symbol
            symbol_orders = [o for o in self.active_orders.values() if o.symbol == symbol]
            if len(symbol_orders) >= self.max_orders_per_symbol:
                warnings.append(f"⚠️ Too many active orders for {symbol} ({len(symbol_orders)})")
            
            # Check for opposing orders
            opposing_side = "sell" if side == "buy" else "buy"
            opposing_orders = [o for o in self.active_orders.values() 
                             if o.symbol == symbol and o.side == opposing_side]
            if opposing_orders:
                warnings.append(f"⚠️ Conflicting {opposing_side} orders exist for {symbol}")
            
            is_safe = len(warnings) == 0
            warning_msg = "\n".join(warnings) if warnings else "✅ No conflicts detected"
            
            return is_safe, warning_msg
    
    def register_order(self, symbol: str, side: str, quantity: int, order_type: str,
                      price: Optional[float], source: OrderSource, 
                      alpaca_order_id: Optional[str] = None) -> str:
        """Register a new order from any source."""
        with self.lock:
            order_id = f"{source.value}_{symbol}_{int(time.time())}"
            
            order = UnifiedOrder(
                order_id=order_id,
                symbol=symbol,
                side=side,
                quantity=quantity,
                order_type=order_type,
                price=price,
                source=source,
                timestamp=datetime.now(),
                status=OrderStatus.PENDING,
                alpaca_order_id=alpaca_order_id
            )
            
            self.active_orders[order_id] = order
            self.order_history.append(order)
            
            # Lock position if this is a new position
            if symbol not in self.position_locks and side == "buy":
                self.position_locks[symbol] = source
            
            return order_id
    
    def update_order_status(self, order_id: str, status: OrderStatus, 
                           alpaca_order_id: Optional[str] = None):
        """Update order status when filled/cancelled."""
        with self.lock:
            if order_id in self.active_orders:
                self.active_orders[order_id].status = status
                if alpaca_order_id:
                    self.active_orders[order_id].alpaca_order_id = alpaca_order_id
                
                # Remove from active if filled/cancelled
                if status in [OrderStatus.FILLED, OrderStatus.CANCELLED, OrderStatus.REJECTED]:
                    order = self.active_orders.pop(order_id)
                    
                    # Release position lock if position closed
                    if status == OrderStatus.FILLED and order.side == "sell":
                        if order.symbol in self.position_locks:
                            del self.position_locks[order.symbol]
    
    def get_symbol_status(self, symbol: str) -> Dict:
        """Get comprehensive status for a symbol."""
        with self.lock:
            active_orders = [o for o in self.active_orders.values() if o.symbol == symbol]
            position_owner = self.position_locks.get(symbol)
            
            return {
                "symbol": symbol,
                "active_orders": len(active_orders),
                "orders": [
                    {
                        "side": o.side,
                        "quantity": o.quantity,
                        "source": o.source.value,
                        "status": o.status.value
                    } for o in active_orders
                ],
                "position_owner": position_owner.value if position_owner else None,
                "has_conflicts": len(set(o.source for o in active_orders)) > 1
            }
    
    def get_system_conflicts(self) -> List[Dict]:
        """Get all current conflicts in the system."""
        with self.lock:
            conflicts = []
            
            # Group orders by symbol
            symbol_orders = {}
            for order in self.active_orders.values():
                if order.symbol not in symbol_orders:
                    symbol_orders[order.symbol] = []
                symbol_orders[order.symbol].append(order)
            
            # Check for conflicts
            for symbol, orders in symbol_orders.items():
                sources = set(o.source for o in orders)
                if len(sources) > 1:
                    conflicts.append({
                        "symbol": symbol,
                        "conflict_type": "multiple_sources",
                        "sources": [s.value for s in sources],
                        "orders": len(orders),
                        "details": f"Both {' and '.join(s.value for s in sources)} have orders"
                    })
            
            return conflicts
    
    def emergency_clear_symbol(self, symbol: str) -> Dict:
        """Emergency function to clear all orders for a symbol."""
        with self.lock:
            cleared_orders = []
            
            # Cancel all active orders for symbol
            for order_id, order in list(self.active_orders.items()):
                if order.symbol == symbol:
                    order.status = OrderStatus.CANCELLED
                    cleared_orders.append(order_id)
                    del self.active_orders[order_id]
            
            # Release position lock
            if symbol in self.position_locks:
                del self.position_locks[symbol]
            
            return {
                "symbol": symbol,
                "cleared_orders": len(cleared_orders),
                "order_ids": cleared_orders,
                "status": "cleared"
            }
    
    def _get_recent_orders(self, symbol: str, side: str) -> List[UnifiedOrder]:
        """Get recent orders for symbol/side within duplicate window."""
        cutoff = datetime.now() - timedelta(seconds=self.duplicate_order_window)
        return [
            o for o in self.active_orders.values()
            if o.symbol == symbol and o.side == side and o.timestamp > cutoff
        ]
    
    def get_coordination_status(self) -> Dict:
        """Get overall coordination system status."""
        with self.lock:
            conflicts = self.get_system_conflicts()
            
            return {
                "active_orders": len(self.active_orders),
                "locked_positions": len(self.position_locks),
                "conflicts": len(conflicts),
                "conflict_details": conflicts,
                "symbols_with_orders": len(set(o.symbol for o in self.active_orders.values())),
                "status": "⚠️ CONFLICTS DETECTED" if conflicts else "✅ NO CONFLICTS"
            }

# Global coordinator instance
_coordinator = None

def get_order_coordinator() -> UnifiedOrderCoordinator:
    """Get the global order coordinator instance."""
    global _coordinator
    if _coordinator is None:
        _coordinator = UnifiedOrderCoordinator()
    return _coordinator

def check_order_safety(symbol: str, side: str, quantity: int, 
                      source: OrderSource) -> Tuple[bool, str]:
    """Quick safety check for any order."""
    coordinator = get_order_coordinator()
    return coordinator.check_order_conflicts(symbol, side, quantity, source)

def register_ttm_order(symbol: str, side: str, quantity: int, order_type: str,
                      price: Optional[float] = None, alpaca_order_id: Optional[str] = None) -> str:
    """Register order from TTM automation."""
    coordinator = get_order_coordinator()
    return coordinator.register_order(symbol, side, quantity, order_type, price, 
                                    OrderSource.TTM_AUTOMATION, alpaca_order_id)

def register_mcp_order(symbol: str, side: str, quantity: int, order_type: str,
                      price: Optional[float] = None, alpaca_order_id: Optional[str] = None) -> str:
    """Register order from MCP integration."""
    coordinator = get_order_coordinator()
    return coordinator.register_order(symbol, side, quantity, order_type, price, 
                                    OrderSource.MCP_MANUAL, alpaca_order_id)

def get_trading_conflicts() -> List[Dict]:
    """Get current trading conflicts for user display."""
    coordinator = get_order_coordinator()
    return coordinator.get_system_conflicts()

def emergency_clear_all_conflicts() -> Dict:
    """Emergency function to clear all conflicts."""
    coordinator = get_order_coordinator()
    conflicts = coordinator.get_system_conflicts()
    
    results = []
    for conflict in conflicts:
        result = coordinator.emergency_clear_symbol(conflict["symbol"])
        results.append(result)
    
    return {
        "cleared_symbols": len(results),
        "details": results,
        "status": "All conflicts cleared"
    }
