"""Central API registry mapping function names to HTTP endpoints.

This file documents 200+ FMP endpoints conceptually. For the MVP we only map a
subset actually used in code to keep size manageable.
"""
from __future__ import annotations

from typing import Dict

FMP_ENDPOINTS: Dict[str, str] = {
    # Keyed by short name used by the program.
    "profile": "/api/v3/profile/{symbol}",
    "quote": "/api/v3/quote/{symbol}",
    "historical": "/api/v3/historical-price-full/{symbol}",
}

BASE_URL = "https://financialmodelingprep.com"


def get_fmp_url(endpoint_name: str, **path_params: str) -> str:
    """Return a fully formatted FMP URL including API key placeholder."""
    path_template = FMP_ENDPOINTS[endpoint_name]
    return BASE_URL + path_template.format(**path_params) 