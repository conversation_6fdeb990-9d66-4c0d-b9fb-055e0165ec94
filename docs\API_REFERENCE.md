# 📚 API Reference

Complete API documentation for the Ultimate TTM Trading System with AI Self-Awareness.

## 🧠 **AI SELF-AWARENESS API**

### **AIBrain Class**

#### **Initialization**
```python
from ai_self_awareness import AI<PERSON>rain, get_ai_brain

# Get global instance
brain = get_ai_brain()

# Or create new instance
brain = AIBrain(db_path="data/ai_brain.db")
```

#### **State Management**
```python
# Update system state
brain.update(key: str, value: Any, reason: str = None)

# Get state value
value = brain.get(key: str, default: Any = None)

# Log decision with reasoning
brain.log_decision(
    decision_type: str,
    symbol: str, 
    reasoning: str,
    data: Dict = None
)
```

#### **Natural Language Explanations**
```python
# Explain current system state
explanation = brain.explain_current_state() -> str

# Explain symbol-specific analysis
analysis = brain.explain_symbol_analysis(symbol: str) -> str

# Get session summary
summary = brain.get_session_summary() -> str

# Explain trade reasoning
reasoning = brain.explain_why_trade_taken(symbol: str) -> str
```

#### **Convenience Functions**
```python
from ai_self_awareness import (
    brain_update, brain_get, brain_log_decision,
    brain_explain_state, brain_explain_symbol
)

# Quick state updates
brain_update("daily_pnl", 125.50, "Updated P&L")

# Quick state retrieval
positions = brain_get("active_positions", {})

# Quick explanations
state = brain_explain_state()
aapl_analysis = brain_explain_symbol("AAPL")
```

### **Investment Judge API**

#### **Initialization**
```python
from investment_judge import InvestmentJudge, get_investment_judge, judge_investment

# Get global instance
judge = get_investment_judge()

# Or create new instance
judge = InvestmentJudge()
```

#### **Investment Analysis**
```python
# Judge investment idea
judgment = judge.judge_investment(
    symbol: str,
    strategy: str = "buy stock",
    time_horizon: str = "1-3 days",
    amount: float = None
) -> Dict

# Format judgment response
response = judge.format_judgment_response(judgment: Dict) -> str

# Convenience function
verdict = judge_investment(
    symbol: str,
    strategy: str = "buy stock", 
    time_horizon: str = "1-3 days"
) -> str
```

#### **Judgment Response Structure**
```python
{
    "symbol": "AAPL",
    "strategy": "buy stock",
    "time_horizon": "1-3 days",
    "timestamp": "2024-01-15T10:30:00",
    
    "verdict": {
        "is_good_idea": True,
        "confidence": "High",
        "summary": "Strong buy signal with high confidence",
        "score": 87.5
    },
    
    "scores": {
        "ttm_squeeze": 85,
        "momentum": 80,
        "sentiment": 75,
        "volatility": 70,
        "risk_factors": 90
    },
    
    "reasoning": "Detailed plain-English explanation...",
    "alternatives": ["List of better options if bad idea"],
    
    "risk_assessment": {
        "risk_level": "Medium",
        "suggested_position_size": "Standard size",
        "conflicting_signals": False,
        "confidence_consistency": True
    }
}
```

## 🎯 **ULTIMATE CONFIDENCE ENGINE API**

### **Confidence Engine Class**
```python
from ultimate_confidence_engine import UltimateConfidenceEngine, get_confidence_engine

# Get global instance
engine = get_confidence_engine()

# Calculate confidence for symbol
result = engine.calculate_confidence(symbol: str, data: Dict) -> Dict
```

### **Confidence Analysis Input**
```python
data = {
    # Technical indicators
    "bb_squeeze": True,
    "kc_squeeze": True,
    "momentum_direction": "bullish",
    "volume_ratio": 2.1,
    "rsi": 65,
    "macd_signal": "bullish_crossover",
    
    # Sentiment data
    "reddit_sentiment": 0.35,
    "twitter_sentiment": 0.25,
    "news_sentiment": 0.15,
    "fear_greed_index": 25,
    
    # Options flow
    "unusual_options_activity": True,
    "options_flow_direction": "bullish",
    "smart_money_flow": 0.4
}
```

### **Confidence Analysis Output**
```python
{
    "symbol": "AAPL",
    "timestamp": "2024-01-15T10:30:00",
    "overall_score": 87.5,
    "grade": "A",
    "confidence_level": "Very High",
    "recommendation": "Strong Buy Signal",
    
    "component_breakdown": {
        "technical": {
            "name": "Technical Analysis",
            "score": 85.0,
            "weight": 0.40,
            "weighted_score": 34.0,
            "confidence_level": "High",
            "sub_scores": {
                "ttm_squeeze": 90,
                "momentum": 85,
                "volume": 80
            }
        }
        # ... other components
    },
    
    "key_signals": [
        "🔥 Dual squeeze active (BB + KC)",
        "📈 Bullish momentum building",
        "🔊 Exceptional volume surge"
    ],
    
    "risk_assessment": {
        "risk_level": "Medium",
        "suggested_position_size": "Standard size",
        "conflicting_signals": False
    }
}
```

## 📊 **ADVANCED ANALYTICS API**

### **Strategy Environment Engine**
```python
from strategy_environment_engine import (
    MarketEnvironment, StrategyRanker,
    get_current_market_environment, get_strategy_ranker
)

# Get current market environment
env = get_current_market_environment()

# Get strategy rankings
ranker = get_strategy_ranker()
rankings = ranker.rank_strategies(env) -> List[Dict]

# Get top strategy
top_strategy = ranker.get_top_strategy(env) -> Dict
```

### **Performance Heatmaps**
```python
from enhanced_performance_heatmaps import PerformanceHeatmapGenerator, get_heatmap_generator

# Get heatmap generator
generator = get_heatmap_generator()

# Generate different heatmap types
strategy_heatmap = generator.generate_strategy_performance_heatmap(days=30)
time_heatmap = generator.generate_time_performance_heatmap()
sector_heatmap = generator.generate_sector_heatmap()
risk_heatmap = generator.generate_risk_adjusted_heatmap("sharpe")
```

### **Unified TTM Scanner**
```python
from unified_ttm_scanner import UnifiedTTMScanner, get_unified_scanner

# Get scanner instance
scanner = get_unified_scanner()

# Scan for opportunities
setups = scanner.scan_all_opportunities(
    symbols: List[str] = None,
    min_confidence: float = 70.0
) -> List[TTMSetup]

# Get top setups
top_setups = scanner.get_top_setups(
    count: int = 10,
    min_grade: str = "B"
) -> List[TTMSetup]
```

### **Auto Trade Planner**
```python
from auto_trade_planner import AutoTradePlanner, get_auto_planner, make_profit_plan

# Get planner instance
planner = get_auto_planner()

# Create profit plan
plan = planner.create_profit_plan(
    target_profit: float,
    account_size: float = 25000,
    max_risk_pct: float = 8.0
) -> TradePlan

# Convenience function
plan_response = make_profit_plan(
    target_amount: float,
    account_size: float = 25000,
    max_risk_pct: float = 8.0
) -> str
```

## 💬 **CHAT INTERFACE API**

### **Available Chat Tools (90+ Total)**
```python
from chat_core import TOOLS

# Core TTM Tools (Original)
ttm_tools = [
    "scan_ttm_squeeze_opportunities",  # TTM scanning
    "analyze_ttm_setup",              # TTM analysis
    "make_profit_plan",               # Profit planning
    "unified_ttm_scan",               # Multi-scanner
    "best_ttm_trade_now",             # Top opportunity
]

# MCP Enhanced Tools (New)
mcp_tools = [
    "get_account_balance",            # Real-time account
    "get_current_positions",          # Live positions
    "create_iron_condor",             # Options strategies
    "run_momentum_algorithm",         # Algo trading
    "volatility_strategy_selector",   # AI strategy selection
]

# Performance & Analytics
analytics_tools = [
    "performance_summary",            # P&L analysis
    "confidence_analysis",            # Confidence scoring
    "strategy_ranking",               # Strategy comparison
    "performance_heatmap",            # Visual analytics
    "risk_analysis",                  # Risk metrics
]
```

### **Chat Function Examples**
```python
from chat_core import (
    _get_system_status,
    _explain_symbol_analysis,
    _judge_investment_idea,
    _analyze_confidence,
    _get_strategy_ranking,
    _generate_performance_heatmap
)

# Get system status
status = _get_system_status() -> str

# Explain symbol
analysis = _explain_symbol_analysis("AAPL") -> str

# Judge investment
verdict = _judge_investment_idea("TSLA", "buy calls", "1 week") -> str

# Analyze confidence
confidence = _analyze_confidence("NVDA", {}) -> str
```

## 🚀 **MCP INTEGRATION API (NEW)**

### **Account Management**
```python
from core.direct_mcp_integration import get_direct_mcp

# Get MCP instance
mcp = get_direct_mcp()

# Account information
balance = mcp.get_account_balance() -> Dict
positions = mcp.get_current_positions() -> List[Dict]
specific_position = mcp.get_specific_position(symbol: str) -> Dict
```

### **Market Data**
```python
# Enhanced quotes
quote = mcp.get_enhanced_quote(symbol: str) -> Dict
history = mcp.get_stock_history(symbol: str, days: int = 30) -> Dict
trades = mcp.get_stock_trades(symbol: str) -> List[Dict]
```

### **Options Strategies**
```python
# Iron Condor
iron_condor = mcp.create_iron_condor(
    symbol: str,
    expiration: str = None
) -> Dict

# Butterfly Spread
butterfly = mcp.create_butterfly_spread(
    symbol: str,
    expiration: str = None
) -> Dict

# AI Strategy Selection
strategy = mcp.volatility_strategy_selector(
    symbol: str,
    expiration: str
) -> Dict
```

### **Algorithmic Trading**
```python
# Momentum Algorithm
momentum = mcp.run_momentum_algorithm(
    symbols: List[str],
    timeframes: List[str] = ["5min", "1hour", "daily"]
) -> Dict

# Mean Reversion
mean_reversion = mcp.run_mean_reversion_algorithm(
    symbols: List[str]
) -> Dict

# Pairs Trading
pairs = mcp.run_pairs_trading(
    pairs: List[Tuple[str, str]]
) -> Dict
```

### **Order Management**
```python
# Enhanced orders
order = mcp.place_enhanced_order(
    symbol: str,
    qty: int,
    side: str,
    order_type: str = "market"
) -> Dict

# Cancel orders
cancel_all = mcp.cancel_all_orders() -> Dict
cancel_specific = mcp.cancel_specific_order(order_id: str) -> Dict

# Position management
close_position = mcp.close_position(symbol: str) -> Dict
close_all = mcp.close_all_positions() -> Dict
```

## 🔧 **TRADING INFRASTRUCTURE API**

### **Enhanced Automation Engine**
```python
from automation_engine import AutomationEngine

# Initialize automation
engine = AutomationEngine()

# Start/stop automation
engine.start_automation()
engine.stop_automation()

# Check status
status = engine.get_automation_status()

# Execute trade
result = engine.execute_trade(
    symbol: str,
    action: str,
    quantity: int,
    order_type: str = "market"
)
```

### **Enhanced Risk Management**
```python
from enhanced_risk_management import EnhancedRiskManager

# Initialize risk manager
risk_manager = EnhancedRiskManager()

# Calculate position size
position_size = risk_manager.calculate_position_size(
    symbol: str,
    confidence_score: float,
    account_size: float,
    max_risk_pct: float = 2.0
)

# Check risk limits
risk_check = risk_manager.check_risk_limits(
    symbol: str,
    quantity: int,
    price: float
)
```

## 📈 **ADAPTIVE LEARNING API**

### **Learning Engine**
```python
from adaptive_learning import AdaptiveLearningEngine, get_learning_engine

# Get learning engine
learning = get_learning_engine()

# Record trade outcome
learning.record_trade_outcome(trade_data: Dict)

# Adjust confidence based on learning
adjusted_confidence = learning.adjust_setup_confidence(setup_data: Dict)

# Get learning insights
insights = learning.get_learning_insights() -> Dict
```

### **Trading Pattern API**
```python
from adaptive_learning import TradingPattern

# Create pattern
pattern = TradingPattern(
    pattern_id: str,
    conditions: Dict
)

# Update pattern performance
pattern.update_performance(
    outcome: bool,
    pnl: float,
    hold_time: float
)

# Get pattern statistics
stats = pattern.get_statistics() -> Dict
```

## 🛠️ **UTILITY FUNCTIONS**

### **Configuration Management**
```python
from config import load_config, get_api_keys

# Load configuration
config = load_config()

# Get API keys
api_keys = get_api_keys()
```

### **Logging Utilities**
```python
from logger_util import setup_logger

# Setup logger
logger = setup_logger("module_name")

# Log messages
logger.info("Information message")
logger.warning("Warning message")
logger.error("Error message")
```

### **Market Data Utilities**
```python
from market_data import get_market_data, get_options_data

# Get market data
data = get_market_data(symbol: str, timeframe: str)

# Get options data
options = get_options_data(symbol: str, expiration: str)
```

## 🔍 **ERROR HANDLING**

### **Common Exceptions**
```python
from core.exceptions import (
    TTMScannerError,
    ConfidenceEngineError,
    InvestmentJudgeError,
    AutomationError,
    RiskManagementError
)

try:
    result = scanner.scan_opportunities()
except TTMScannerError as e:
    logger.error(f"Scanner error: {e}")
except Exception as e:
    logger.error(f"Unexpected error: {e}")
```

### **Error Response Format**
```python
{
    "error": True,
    "error_type": "TTMScannerError",
    "message": "Failed to scan opportunities",
    "details": "Detailed error information",
    "timestamp": "2024-01-15T10:30:00"
}
```

## 📊 **RESPONSE FORMATS**

### **Standard Success Response**
```python
{
    "success": True,
    "data": {...},
    "timestamp": "2024-01-15T10:30:00",
    "execution_time": 0.125
}
```

### **Standard Error Response**
```python
{
    "success": False,
    "error": "Error message",
    "error_code": "ERROR_CODE",
    "timestamp": "2024-01-15T10:30:00"
}
```

---

**This API provides complete programmatic access to all AI self-awareness and trading intelligence features of the Ultimate TTM Trading System.** 🚀
