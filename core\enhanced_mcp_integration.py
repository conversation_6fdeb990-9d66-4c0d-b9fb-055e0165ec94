#!/usr/bin/env python3
"""
Enhanced MCP Integration for TotalRecall TTM Trading System

This module seamlessly enhances the existing chat trading capabilities
with advanced MCP server features without disrupting current functionality.

Key Enhancements:
- Advanced options Greeks analysis through chat
- Real-time portfolio risk/reward calculations  
- Enhanced market data with corporate actions
- Multi-leg options strategy execution
- Advanced order types (stop-limit, trailing stops)
- Watchlist management through natural language
"""

import os
import asyncio
import json
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from pathlib import Path

# Import existing system components
try:
    from core.logger_util import info, warning, error
    LOGGER_AVAILABLE = True
except ImportError:
    LOGGER_AVAILABLE = False
    def info(msg): print(f"ℹ️ {msg}")
    def warning(msg): print(f"⚠️ {msg}")
    def error(msg): print(f"❌ {msg}")

try:
    from core.chat_core import TOOLS, chat_gpt
    CHAT_AVAILABLE = True
except ImportError:
    CHAT_AVAILABLE = False
    TOOLS = {}

try:
    from trading.alpaca_trading import get_paper_trader, get_live_trader
    TRADING_AVAILABLE = True
except ImportError:
    TRADING_AVAILABLE = False

CORE_AVAILABLE = CHAT_AVAILABLE and TRADING_AVAILABLE

class EnhancedMCPIntegration:
    """Enhanced MCP integration that adds advanced capabilities to existing chat system."""
    
    def __init__(self):
        self.mcp_server_path = None
        self.enhanced_tools = {}
        self.is_enhanced = False
        
        # Load the downloaded alpaca-mcp-server
        self._locate_mcp_server()
        
    def _locate_mcp_server(self):
        """Locate the alpaca-mcp-server from Downloads."""
        downloads_path = Path.home() / "Downloads" / "alpaca-mcp-server-main"
        if downloads_path.exists():
            self.mcp_server_path = downloads_path / "alpaca_mcp_server.py"
            info(f"📍 Found MCP server at: {self.mcp_server_path}")
        else:
            # Try integrations directory
            integrations_path = Path("integrations") / "alpaca-mcp-server"
            if integrations_path.exists():
                self.mcp_server_path = integrations_path / "alpaca_mcp_server.py"
                info(f"📍 Found MCP server at: {self.mcp_server_path}")
    
    def enhance_existing_tools(self):
        """Enhance existing chat tools with MCP capabilities."""
        if not CORE_AVAILABLE:
            warning("Core modules not available - cannot enhance tools")
            return False
            
        try:
            # Add enhanced tools to existing TOOLS registry
            enhanced_tools = {
                "advanced_options_analysis": {
                    "function": self.advanced_options_analysis,
                    "schema": {
                        "name": "advanced_options_analysis",
                        "description": "Advanced options analysis with Greeks, IV, and risk metrics using MCP server capabilities",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "symbol": {"type": "string", "description": "Options symbol or underlying stock"},
                                "analysis_type": {"type": "string", "enum": ["greeks", "iv_analysis", "risk_reward", "strategy_comparison"]}
                            },
                            "required": ["symbol"]
                        }
                    }
                },
                
                "enhanced_portfolio_analysis": {
                    "function": self.enhanced_portfolio_analysis,
                    "schema": {
                        "name": "enhanced_portfolio_analysis", 
                        "description": "Enhanced portfolio analysis with real-time risk metrics and position details",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "analysis_type": {"type": "string", "enum": ["full", "risk", "performance", "positions"]},
                                "include_options": {"type": "boolean", "default": True}
                            }
                        }
                    }
                },
                
                "advanced_market_intelligence": {
                    "function": self.advanced_market_intelligence,
                    "schema": {
                        "name": "advanced_market_intelligence",
                        "description": "Advanced market intelligence including corporate actions, earnings, and market calendar",
                        "parameters": {
                            "type": "object", 
                            "properties": {
                                "query_type": {"type": "string", "enum": ["earnings", "dividends", "splits", "market_calendar", "corporate_actions"]},
                                "symbol": {"type": "string", "description": "Optional symbol filter"},
                                "days_ahead": {"type": "integer", "default": 30}
                            },
                            "required": ["query_type"]
                        }
                    }
                },
                
                "smart_order_execution": {
                    "function": self.smart_order_execution,
                    "schema": {
                        "name": "smart_order_execution",
                        "description": "Smart order execution with advanced order types and risk management",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "symbol": {"type": "string"},
                                "action": {"type": "string", "enum": ["buy", "sell"]},
                                "quantity": {"type": "number"},
                                "order_type": {"type": "string", "enum": ["market", "limit", "stop", "stop_limit", "trailing_stop"]},
                                "price": {"type": "number", "description": "Limit or stop price"},
                                "trail_percent": {"type": "number", "description": "Trailing stop percentage"}
                            },
                            "required": ["symbol", "action", "quantity"]
                        }
                    }
                },
                
                "watchlist_intelligence": {
                    "function": self.watchlist_intelligence,
                    "schema": {
                        "name": "watchlist_intelligence",
                        "description": "Intelligent watchlist management with TTM integration",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "action": {"type": "string", "enum": ["create", "update", "analyze", "scan"]},
                                "name": {"type": "string", "description": "Watchlist name"},
                                "symbols": {"type": "array", "items": {"type": "string"}},
                                "ttm_filter": {"type": "boolean", "default": True}
                            },
                            "required": ["action"]
                        }
                    }
                }
            }
            
            # Merge with existing tools
            if hasattr(TOOLS, 'update'):
                TOOLS.update(enhanced_tools)
            else:
                # If TOOLS is a different structure, handle appropriately
                for name, tool in enhanced_tools.items():
                    setattr(TOOLS, name, tool)
            
            self.enhanced_tools = enhanced_tools
            self.is_enhanced = True
            info("✅ Enhanced MCP tools integrated with existing chat system")
            return True
            
        except Exception as e:
            error(f"Failed to enhance tools: {e}")
            return False
    
    async def advanced_options_analysis(self, symbol: str, analysis_type: str = "greeks") -> str:
        """Advanced options analysis using MCP server capabilities."""
        try:
            # Import MCP server functions if available
            if self.mcp_server_path and self.mcp_server_path.exists():
                # Use the enhanced alpaca-mcp-server for options analysis
                return await self._call_mcp_options_analysis(symbol, analysis_type)
            else:
                # Fallback to existing options analysis
                return self._fallback_options_analysis(symbol, analysis_type)
                
        except Exception as e:
            error(f"Options analysis failed: {e}")
            return f"❌ Options analysis failed for {symbol}: {str(e)}"
    
    async def enhanced_portfolio_analysis(self, analysis_type: str = "full", include_options: bool = True) -> str:
        """Enhanced portfolio analysis with real-time metrics."""
        try:
            if self.mcp_server_path and self.mcp_server_path.exists():
                return await self._call_mcp_portfolio_analysis(analysis_type, include_options)
            else:
                return self._fallback_portfolio_analysis(analysis_type, include_options)
                
        except Exception as e:
            error(f"Portfolio analysis failed: {e}")
            return f"❌ Portfolio analysis failed: {str(e)}"
    
    async def advanced_market_intelligence(self, query_type: str, symbol: str = None, days_ahead: int = 30) -> str:
        """Advanced market intelligence with corporate actions."""
        try:
            if self.mcp_server_path and self.mcp_server_path.exists():
                return await self._call_mcp_market_intelligence(query_type, symbol, days_ahead)
            else:
                return self._fallback_market_intelligence(query_type, symbol, days_ahead)
                
        except Exception as e:
            error(f"Market intelligence failed: {e}")
            return f"❌ Market intelligence failed: {str(e)}"
    
    async def smart_order_execution(self, symbol: str, action: str, quantity: float, 
                                  order_type: str = "market", price: float = None, 
                                  trail_percent: float = None) -> str:
        """Smart order execution with advanced order types."""
        try:
            if self.mcp_server_path and self.mcp_server_path.exists():
                return await self._call_mcp_order_execution(symbol, action, quantity, order_type, price, trail_percent)
            else:
                return self._fallback_order_execution(symbol, action, quantity, order_type, price, trail_percent)
                
        except Exception as e:
            error(f"Order execution failed: {e}")
            return f"❌ Order execution failed: {str(e)}"
    
    async def watchlist_intelligence(self, action: str, name: str = None,
                                   symbols: List[str] = None, ttm_filter: bool = True) -> str:
        """Intelligent watchlist management."""
        try:
            if self.mcp_server_path and self.mcp_server_path.exists():
                return await self._call_mcp_watchlist_management(action, name, symbols, ttm_filter)
            else:
                return self._fallback_watchlist_management(action, name, symbols, ttm_filter)

        except Exception as e:
            error(f"Watchlist management failed: {e}")
            return f"❌ Watchlist management failed: {str(e)}"

    # MCP Server Integration Methods
    async def _call_mcp_options_analysis(self, symbol: str, analysis_type: str) -> str:
        """Call MCP server for advanced options analysis."""
        try:
            # Import the MCP server functions
            import sys
            sys.path.append(str(self.mcp_server_path.parent))

            if analysis_type == "greeks":
                # Use MCP server's option snapshot for Greeks
                from alpaca_mcp_server import get_option_snapshot
                result = await get_option_snapshot(symbol)
                return self._format_greeks_analysis(result)

            elif analysis_type == "iv_analysis":
                # Enhanced IV analysis using MCP data
                from alpaca_mcp_server import get_option_contracts, get_option_latest_quote
                contracts = await get_option_contracts(symbol, None)
                return self._format_iv_analysis(contracts)

            else:
                return f"📊 Advanced options analysis for {symbol} using MCP server capabilities"

        except Exception as e:
            error(f"MCP options analysis failed: {e}")
            return self._fallback_options_analysis(symbol, analysis_type)

    async def _call_mcp_portfolio_analysis(self, analysis_type: str, include_options: bool) -> str:
        """Call MCP server for enhanced portfolio analysis."""
        try:
            import sys
            sys.path.append(str(self.mcp_server_path.parent))

            from alpaca_mcp_server import get_account_info, get_positions

            account_info = await get_account_info()
            positions = await get_positions()

            return self._format_enhanced_portfolio(account_info, positions, analysis_type, include_options)

        except Exception as e:
            error(f"MCP portfolio analysis failed: {e}")
            return self._fallback_portfolio_analysis(analysis_type, include_options)

    async def _call_mcp_market_intelligence(self, query_type: str, symbol: str, days_ahead: int) -> str:
        """Call MCP server for advanced market intelligence."""
        try:
            import sys
            sys.path.append(str(self.mcp_server_path.parent))

            if query_type == "earnings":
                from alpaca_mcp_server import get_corporate_announcements
                from alpaca.trading.enums import CorporateActionType
                from datetime import date

                result = await get_corporate_announcements(
                    [CorporateActionType.EARNINGS],
                    date.today(),
                    date.today() + timedelta(days=days_ahead),
                    symbol=symbol
                )
                return self._format_earnings_intelligence(result)

            elif query_type == "market_calendar":
                from alpaca_mcp_server import get_market_calendar
                start_date = datetime.now().strftime('%Y-%m-%d')
                end_date = (datetime.now() + timedelta(days=days_ahead)).strftime('%Y-%m-%d')

                result = await get_market_calendar(start_date, end_date)
                return self._format_market_calendar(result)

            else:
                return f"📈 Advanced market intelligence for {query_type}"

        except Exception as e:
            error(f"MCP market intelligence failed: {e}")
            return self._fallback_market_intelligence(query_type, symbol, days_ahead)

    async def _call_mcp_order_execution(self, symbol: str, action: str, quantity: float,
                                      order_type: str, price: float, trail_percent: float) -> str:
        """Call MCP server for smart order execution."""
        try:
            import sys
            sys.path.append(str(self.mcp_server_path.parent))

            from alpaca_mcp_server import place_stock_order

            # Map action to side
            side = "buy" if action.lower() == "buy" else "sell"

            # Prepare order parameters
            order_params = {
                "symbol": symbol,
                "side": side,
                "quantity": quantity,
                "order_type": order_type
            }

            if price:
                if order_type in ["limit", "stop_limit"]:
                    order_params["limit_price"] = price
                elif order_type == "stop":
                    order_params["stop_price"] = price

            if trail_percent and order_type == "trailing_stop":
                order_params["trail_percent"] = trail_percent

            result = await place_stock_order(**order_params)
            return self._format_order_result(result, symbol, action, quantity)

        except Exception as e:
            error(f"MCP order execution failed: {e}")
            return self._fallback_order_execution(symbol, action, quantity, order_type, price, trail_percent)

    async def _call_mcp_watchlist_management(self, action: str, name: str, symbols: List[str], ttm_filter: bool) -> str:
        """Call MCP server for watchlist management."""
        try:
            import sys
            sys.path.append(str(self.mcp_server_path.parent))

            if action == "create":
                from alpaca_mcp_server import create_watchlist
                result = await create_watchlist(name, symbols or [])
                return self._format_watchlist_result(result, action, name)

            elif action == "analyze":
                from alpaca_mcp_server import get_watchlists
                watchlists = await get_watchlists()
                return self._format_watchlist_analysis(watchlists, ttm_filter)

            else:
                return f"📋 Watchlist {action} completed"

        except Exception as e:
            error(f"MCP watchlist management failed: {e}")
            return self._fallback_watchlist_management(action, name, symbols, ttm_filter)

    # Fallback Methods (use existing system capabilities)
    def _fallback_options_analysis(self, symbol: str, analysis_type: str) -> str:
        """Fallback options analysis using existing system."""
        try:
            if CORE_AVAILABLE:
                from trading.options_strategies import get_options_strategy_specialist
                specialist = get_options_strategy_specialist()

                if analysis_type == "greeks":
                    return f"📊 **Options Greeks Analysis for {symbol}**\n\n" \
                           f"Using existing TTM options analysis capabilities.\n" \
                           f"For detailed Greeks, use the Options Strategy tab in the GUI."

                elif analysis_type == "iv_analysis":
                    return f"📈 **Implied Volatility Analysis for {symbol}**\n\n" \
                           f"Current IV analysis available through existing options tools.\n" \
                           f"Check the Options Strategies module for detailed IV metrics."

                else:
                    return f"📊 **Options Analysis for {symbol}**\n\n" \
                           f"Advanced options analysis available through existing system.\n" \
                           f"Use TTM + Options combo for comprehensive analysis."
            else:
                return f"📊 Options analysis for {symbol} - Core modules not available"

        except Exception as e:
            return f"❌ Fallback options analysis failed: {str(e)}"

    def _fallback_portfolio_analysis(self, analysis_type: str, include_options: bool) -> str:
        """Fallback portfolio analysis using existing system."""
        try:
            if CORE_AVAILABLE:
                from trading.alpaca_trading import get_paper_trader
                trader = get_paper_trader()

                account_info = trader.get_account_info()
                if account_info:
                    return f"💼 **Portfolio Analysis**\n\n" \
                           f"Account Status: {account_info.get('status', 'Unknown')}\n" \
                           f"Equity: ${float(account_info.get('equity', 0)):,.2f}\n" \
                           f"Buying Power: ${float(account_info.get('buying_power', 0)):,.2f}\n" \
                           f"Cash: ${float(account_info.get('cash', 0)):,.2f}\n\n" \
                           f"For detailed analysis, use the Performance Dashboard."
                else:
                    return "📊 Portfolio analysis available through existing Alpaca integration"
            else:
                return "📊 Portfolio analysis - Core modules not available"

        except Exception as e:
            return f"❌ Fallback portfolio analysis failed: {str(e)}"

    def _fallback_market_intelligence(self, query_type: str, symbol: str, days_ahead: int) -> str:
        """Fallback market intelligence using existing system."""
        try:
            if query_type == "earnings":
                return f"📈 **Earnings Intelligence**\n\n" \
                       f"Earnings data available through existing market analysis.\n" \
                       f"Use the Market Intelligence features for earnings calendar."

            elif query_type == "market_calendar":
                return f"📅 **Market Calendar**\n\n" \
                       f"Market calendar available through existing trading system.\n" \
                       f"Check market hours in the trading interface."

            else:
                return f"📊 **Market Intelligence**\n\n" \
                       f"Advanced market data available through existing system.\n" \
                       f"Use TTM Scanner and Market Analysis features."

        except Exception as e:
            return f"❌ Fallback market intelligence failed: {str(e)}"

    def _fallback_order_execution(self, symbol: str, action: str, quantity: float,
                                order_type: str, price: float, trail_percent: float) -> str:
        """Fallback order execution using existing system."""
        try:
            if CORE_AVAILABLE:
                from trading.alpaca_trading import get_paper_trader
                trader = get_paper_trader()

                # Use existing order execution
                result = trader.place_order(
                    symbol=symbol,
                    quantity=int(quantity),
                    side=action,
                    order_type=order_type,
                    limit_price=price
                )

                if result.get('success'):
                    return f"✅ **Order Executed**\n\n" \
                           f"Symbol: {symbol}\n" \
                           f"Action: {action.upper()}\n" \
                           f"Quantity: {quantity}\n" \
                           f"Type: {order_type}\n" \
                           f"Status: Submitted successfully"
                else:
                    return f"❌ Order failed: {result.get('error', 'Unknown error')}"
            else:
                return f"📊 Order execution for {symbol} - Core modules not available"

        except Exception as e:
            return f"❌ Fallback order execution failed: {str(e)}"

    def _fallback_watchlist_management(self, action: str, name: str, symbols: List[str], ttm_filter: bool) -> str:
        """Fallback watchlist management using existing system."""
        try:
            if action == "create":
                return f"📋 **Watchlist Creation**\n\n" \
                       f"Watchlist '{name}' with symbols: {', '.join(symbols or [])}\n" \
                       f"Use the TTM Scanner to create and manage watchlists."

            elif action == "analyze":
                return f"📊 **Watchlist Analysis**\n\n" \
                       f"Watchlist analysis available through TTM Scanner.\n" \
                       f"Use the S&P 500 + PLTR scanner for comprehensive analysis."

            else:
                return f"📋 Watchlist {action} - Use existing TTM Scanner features"

        except Exception as e:
            return f"❌ Fallback watchlist management failed: {str(e)}"

    # Formatting Methods
    def _format_greeks_analysis(self, result: str) -> str:
        """Format Greeks analysis result."""
        return f"🎯 **Advanced Options Greeks Analysis**\n\n{result}\n\n" \
               f"💡 **Enhanced with MCP Server capabilities**\n" \
               f"• Real-time Greeks calculations\n" \
               f"• Implied volatility analysis\n" \
               f"• Risk/reward metrics\n" \
               f"• Time decay analysis"

    def _format_iv_analysis(self, contracts: str) -> str:
        """Format IV analysis result."""
        return f"📈 **Implied Volatility Analysis**\n\n{contracts}\n\n" \
               f"💡 **Enhanced IV Intelligence**\n" \
               f"• Real-time IV calculations\n" \
               f"• Historical IV comparison\n" \
               f"• Volatility skew analysis\n" \
               f"• Options flow insights"

    def _format_enhanced_portfolio(self, account_info: str, positions: str,
                                 analysis_type: str, include_options: bool) -> str:
        """Format enhanced portfolio analysis."""
        return f"💼 **Enhanced Portfolio Analysis**\n\n" \
               f"**Account Overview:**\n{account_info}\n\n" \
               f"**Current Positions:**\n{positions}\n\n" \
               f"💡 **MCP Enhanced Features:**\n" \
               f"• Real-time P&L calculations\n" \
               f"• Advanced risk metrics\n" \
               f"• Options Greeks integration\n" \
               f"• Portfolio optimization insights"

    def _format_earnings_intelligence(self, result: str) -> str:
        """Format earnings intelligence result."""
        return f"📊 **Earnings Intelligence**\n\n{result}\n\n" \
               f"💡 **Enhanced Corporate Actions Data**\n" \
               f"• Real-time earnings calendar\n" \
               f"• Dividend announcements\n" \
               f"• Stock splits tracking\n" \
               f"• Ex-dividend dates"

    def _format_market_calendar(self, result: str) -> str:
        """Format market calendar result."""
        return f"📅 **Market Calendar**\n\n{result}\n\n" \
               f"💡 **Enhanced Market Intelligence**\n" \
               f"• Trading hours optimization\n" \
               f"• Holiday schedule\n" \
               f"• Extended hours availability\n" \
               f"• Market session planning"

    def _format_order_result(self, result: str, symbol: str, action: str, quantity: float) -> str:
        """Format order execution result."""
        return f"✅ **Smart Order Execution**\n\n{result}\n\n" \
               f"📊 **Order Summary:**\n" \
               f"• Symbol: {symbol}\n" \
               f"• Action: {action.upper()}\n" \
               f"• Quantity: {quantity}\n\n" \
               f"💡 **MCP Enhanced Features:**\n" \
               f"• Advanced order types\n" \
               f"• Real-time execution\n" \
               f"• Risk management integration\n" \
               f"• TTM strategy alignment"

    def _format_watchlist_result(self, result: str, action: str, name: str) -> str:
        """Format watchlist management result."""
        return f"📋 **Watchlist {action.title()}**\n\n{result}\n\n" \
               f"💡 **Enhanced Watchlist Features:**\n" \
               f"• TTM integration\n" \
               f"• Real-time monitoring\n" \
               f"• Automated scanning\n" \
               f"• Performance tracking"

    def _format_watchlist_analysis(self, watchlists: str, ttm_filter: bool) -> str:
        """Format watchlist analysis result."""
        ttm_status = "✅ TTM Filter Active" if ttm_filter else "⚪ TTM Filter Inactive"
        return f"📊 **Watchlist Analysis**\n\n{watchlists}\n\n" \
               f"💡 **Analysis Features:**\n" \
               f"• {ttm_status}\n" \
               f"• Real-time scanning\n" \
               f"• Performance metrics\n" \
               f"• Opportunity detection"

# Global instance
_enhanced_mcp = None

def get_enhanced_mcp() -> EnhancedMCPIntegration:
    """Get the global enhanced MCP integration instance."""
    global _enhanced_mcp
    if _enhanced_mcp is None:
        _enhanced_mcp = EnhancedMCPIntegration()
    return _enhanced_mcp

def initialize_enhanced_mcp() -> bool:
    """Initialize enhanced MCP integration."""
    try:
        enhanced_mcp = get_enhanced_mcp()
        success = enhanced_mcp.enhance_existing_tools()

        if success:
            info("🚀 Enhanced MCP integration initialized successfully")
            info("💡 New capabilities added to existing chat system:")
            info("   • Advanced options Greeks analysis")
            info("   • Enhanced portfolio risk/reward metrics")
            info("   • Corporate actions and earnings intelligence")
            info("   • Smart order execution with advanced types")
            info("   • Intelligent watchlist management")
            return True
        else:
            warning("⚠️ Enhanced MCP integration failed to initialize")
            return False

    except Exception as e:
        error(f"Enhanced MCP initialization failed: {e}")
        return False

def is_enhanced_mcp_available() -> bool:
    """Check if enhanced MCP integration is available."""
    try:
        enhanced_mcp = get_enhanced_mcp()
        return enhanced_mcp.is_enhanced and enhanced_mcp.mcp_server_path is not None
    except:
        return False

def get_enhanced_capabilities() -> List[str]:
    """Get list of enhanced capabilities."""
    if is_enhanced_mcp_available():
        return [
            "Advanced Options Greeks Analysis",
            "Enhanced Portfolio Risk/Reward Metrics",
            "Corporate Actions Intelligence",
            "Smart Order Execution",
            "Intelligent Watchlist Management",
            "Real-time Market Data Integration",
            "Multi-leg Options Strategy Execution",
            "Advanced Order Types (Stop-Limit, Trailing)"
        ]
    else:
        return []
