#!/usr/bin/env python3
"""
Launch Incite AI Style Interface
Simple, fast, and powerful - just like Incite AI but better!
"""
import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def check_streamlit():
    """Check if Streamlit is installed"""
    try:
        import streamlit
        return True
    except ImportError:
        return False

def install_streamlit():
    """Install Streamlit if needed"""
    print("📦 Installing Streamlit...")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'streamlit', 'plotly', 'pandas'])
        print("✅ Streamlit installed successfully!")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install Streamlit")
        return False

def setup_directories():
    """Create necessary directories"""
    directories = ['data', 'logs', 'uploads', 'exports']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)

def launch_interface():
    """Launch the Incite AI style interface"""
    
    print("🚀 LAUNCHING INCITE AI STYLE INTERFACE")
    print("=" * 50)
    
    # Check Streamlit
    if not check_streamlit():
        print("⚠️  Streamlit not found. Installing...")
        if not install_streamlit():
            print("❌ Cannot proceed without Streamlit")
            return False
    
    # Setup directories
    setup_directories()
    
    # Get interface file
    interface_file = Path(__file__).parent / 'simple_web_interface.py'
    
    if not interface_file.exists():
        print(f"❌ Interface file not found: {interface_file}")
        return False
    
    print("🌐 Starting professional web interface...")
    print("🎯 Features: AI Chat, Chart Upload, Deep Search, TTM Scanner")
    
    try:
        # Launch Streamlit
        cmd = [
            sys.executable, '-m', 'streamlit', 'run',
            str(interface_file),
            '--server.port', '8501',
            '--server.address', 'localhost',
            '--browser.gatherUsageStats', 'false',
            '--theme.primaryColor', '#667eea',
            '--theme.backgroundColor', '#ffffff',
            '--theme.secondaryBackgroundColor', '#f8f9fa'
        ]
        
        print(f"🔧 Starting server...")
        
        # Start process
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait for server to start
        time.sleep(4)
        
        if process.poll() is None:
            print("✅ SUCCESS! Professional interface is running!")
            print()
            print("🌐 **ACCESS YOUR INCITE AI STYLE INTERFACE:**")
            print("   📍 URL: http://localhost:8501")
            print("   📱 Mobile friendly & responsive")
            print("   🎨 Professional design like Incite AI")
            print()
            print("🧠 **AI FEATURES AVAILABLE:**")
            print("   • 🤖 AI Chat with intent detection")
            print("   • 📈 Chart upload & AI vision analysis")
            print("   • 🔍 Deep Search through trading data")
            print("   • 📊 TTM Scanner with A+ grading")
            print("   • 💼 Portfolio management")
            print("   • 🎯 Real-time system awareness")
            print()
            print("⚡ **TRY THESE COMMANDS:**")
            print("   • 'What's happening in my system right now?'")
            print("   • 'Is buying AAPL a good idea right now?'")
            print("   • 'Show me the best TTM setups available'")
            print("   • Upload a Think or Swim chart for analysis")
            print("   • 'Search for NVDA trades last week'")
            print()
            print("🎨 **INTERFACE HIGHLIGHTS:**")
            print("   • Professional gradient design")
            print("   • Intent detection for smart responses")
            print("   • Chart upload zone for ToS screenshots")
            print("   • Real-time metrics and status")
            print("   • Quick action buttons")
            print("   • Tabbed interface for easy navigation")
            
            # Try to open browser
            try:
                webbrowser.open('http://localhost:8501')
                print("   🌐 Browser opened automatically")
            except:
                print("   ⚠️  Please open http://localhost:8501 manually")
            
            print()
            print("🛑 Press Ctrl+C to stop the server")
            
            try:
                # Wait for process
                process.wait()
            except KeyboardInterrupt:
                print("\n🛑 Shutting down interface...")
                process.terminate()
                process.wait()
                print("✅ Interface stopped")
            
            return True
        else:
            # Process failed
            stdout, stderr = process.communicate()
            print("❌ Failed to start interface")
            if stderr:
                print(f"Error: {stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error launching interface: {e}")
        return False

def show_help():
    """Show help information"""
    print("🚀 INCITE AI STYLE TRADING INTERFACE")
    print("=" * 40)
    print()
    print("🎯 **DESCRIPTION:**")
    print("   Professional web interface that looks and feels like Incite AI")
    print("   but with complete AI consciousness and advanced features.")
    print()
    print("✨ **KEY FEATURES:**")
    print("   🧠 AI Chat with intent detection")
    print("   📈 Chart upload & AI vision analysis")
    print("   🔍 Deep Search through trading data")
    print("   📊 TTM Scanner with confidence grading")
    print("   💼 Portfolio management")
    print("   🎯 Real-time system awareness")
    print()
    print("🎨 **DESIGN:**")
    print("   • Professional gradients and styling")
    print("   • Responsive mobile-friendly layout")
    print("   • Intent detection boxes")
    print("   • Chart upload zones")
    print("   • Real-time metrics cards")
    print()
    print("🚀 **USAGE:**")
    print("   python launch_incite_interface.py")
    print()
    print("🌐 **ACCESS:**")
    print("   http://localhost:8501")
    print()
    print("💡 **TIPS:**")
    print("   • Upload Think or Swim screenshots")
    print("   • Use natural language queries")
    print("   • Try the quick action buttons")
    print("   • Check the intent detection")

def main():
    """Main launcher"""
    
    if len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h', 'help']:
        show_help()
        return
    
    print("🎨 INCITE AI STYLE INTERFACE LAUNCHER")
    print("🚀 Professional Trading Platform")
    print("=" * 45)
    
    success = launch_interface()
    
    if success:
        print("\n🎉 **LAUNCH SUCCESSFUL!**")
        print("Your professional Incite AI style interface is ready!")
        print("Enjoy the most advanced trading platform ever built! 🏆")
    else:
        print("\n❌ **LAUNCH FAILED**")
        print("Please check the error messages above.")
        print("\nFor help: python launch_incite_interface.py --help")

if __name__ == "__main__":
    main()
