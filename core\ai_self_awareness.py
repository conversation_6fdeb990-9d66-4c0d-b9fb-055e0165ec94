#!/usr/bin/env python3
"""AI Self-Awareness Engine

The AI brain that knows everything happening in the trading system:
- Live system state tracking
- Real-time decision reasoning
- Memory of all trades, scans, and decisions
- Natural language explanations for everything
- Complete system consciousness
"""
import json
import sqlite3
import threading
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any
from pathlib import Path
import logging

# Import Deep Search RAG system
try:
    from deep_search_rag import get_deep_search, add_to_search_index
    DEEP_SEARCH_AVAILABLE = True
except ImportError:
    DEEP_SEARCH_AVAILABLE = False


class AIBrain:
    """The AI brain that maintains complete awareness of system state."""
    
    def __init__(self, db_path: str = "data/ai_brain.db"):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        self._lock = threading.Lock()
        
        # Live system state
        self.state = {
            # Trading state
            "active_positions": {},
            "watchlist": [],
            "last_scan_results": [],
            "last_trade": None,
            "pending_orders": {},
            
            # Scanner state
            "current_scan_symbols": [],
            "scan_grades": {},
            "scan_reasons": {},
            "squeeze_status": {},
            "momentum_signals": {},
            
            # Market state
            "market_environment": {},
            "strategy_rankings": [],
            "confidence_scores": {},
            "sentiment_data": {},
            
            # System state
            "automation_status": "stopped",
            "risk_exposure": 0.0,
            "daily_pnl": 0.0,
            "settings": {},
            "last_update": datetime.now().isoformat(),
            
            # Decision reasoning
            "decision_log": [],
            "trade_reasons": {},
            "rejection_reasons": {},
            "alerts": [],
            
            # Performance tracking
            "session_stats": {
                "trades_taken": 0,
                "trades_rejected": 0,
                "scans_completed": 0,
                "alerts_generated": 0
            }
        }
        
        # Initialize database
        self.init_database()
        
        # Load previous state
        self.load_state()
    
    def init_database(self):
        """Initialize AI brain database."""
        Path(self.db_path).parent.mkdir(exist_ok=True)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # State snapshots table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS state_snapshots (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT,
                    state_data TEXT,
                    event_type TEXT,
                    description TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Decision log table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS decision_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT,
                    decision_type TEXT,
                    symbol TEXT,
                    reasoning TEXT,
                    data TEXT,
                    outcome TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Memory table for AI responses
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS ai_memory (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    question TEXT,
                    answer TEXT,
                    context TEXT,
                    timestamp TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
    
    def update(self, key: str, value: Any, reason: str = None):
        """Update system state with reasoning."""
        with self._lock:
            old_value = self.state.get(key)
            self.state[key] = value
            self.state["last_update"] = datetime.now().isoformat()
            
            # Log the decision/update
            if reason:
                self.log_decision("state_update", key, reason, {"old": old_value, "new": value})
            
            # Save state snapshot for important updates
            if key in ["active_positions", "last_trade", "automation_status"]:
                self.save_state_snapshot(f"Updated {key}", reason or "System update")
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get current state value."""
        with self._lock:
            return self.state.get(key, default)
    
    def log_decision(self, decision_type: str, symbol: str, reasoning: str, data: Dict = None):
        """Log a decision with full reasoning."""
        with self._lock:
            decision = {
                "timestamp": datetime.now().isoformat(),
                "type": decision_type,
                "symbol": symbol,
                "reasoning": reasoning,
                "data": data or {}
            }
            
            # Add to in-memory log
            self.state["decision_log"].append(decision)
            
            # Keep only last 100 decisions in memory
            if len(self.state["decision_log"]) > 100:
                self.state["decision_log"] = self.state["decision_log"][-100:]
            
            # Store in database
            try:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute('''
                        INSERT INTO decision_log
                        (timestamp, decision_type, symbol, reasoning, data)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (
                        decision["timestamp"],
                        decision_type,
                        symbol,
                        reasoning,
                        json.dumps(data or {})
                    ))
                    conn.commit()

                # Add to Deep Search index
                if DEEP_SEARCH_AVAILABLE:
                    try:
                        content = f"{decision_type.title()}: {symbol}\nReasoning: {reasoning}"
                        metadata = {
                            "type": "ai_decision",
                            "decision_type": decision_type,
                            "symbol": symbol,
                            **data
                        }
                        add_to_search_index(content, metadata, "ai_decisions")
                    except Exception as e:
                        self.logger.error(f"Error adding to search index: {e}")

            except Exception as e:
                self.logger.error(f"Error storing decision log: {e}")
    
    def explain_current_state(self) -> str:
        """Generate natural language explanation of current system state."""
        with self._lock:
            explanation = "🧠 **CURRENT SYSTEM STATE**\n\n"
            
            # Active positions
            positions = self.state.get("active_positions", {})
            if positions:
                explanation += f"**📊 Active Positions ({len(positions)}):**\n"
                for symbol, pos in positions.items():
                    pnl = pos.get('unrealized_pnl', 0)
                    pnl_emoji = "🟢" if pnl > 0 else "🔴" if pnl < 0 else "⚪"
                    explanation += f"• {symbol}: {pnl_emoji} ${pnl:.2f} P&L\n"
                explanation += "\n"
            else:
                explanation += "**📊 Active Positions:** None\n\n"
            
            # Watchlist
            watchlist = self.state.get("watchlist", [])
            if watchlist:
                explanation += f"**👀 Watchlist ({len(watchlist)}):** {', '.join(watchlist)}\n\n"
            
            # Last scan results
            scan_results = self.state.get("last_scan_results", [])
            if scan_results:
                explanation += f"**🔍 Last Scan:** Found {len(scan_results)} opportunities\n"
                for result in scan_results[:3]:  # Top 3
                    symbol = result.get('symbol', 'Unknown')
                    grade = result.get('grade', 'N/A')
                    explanation += f"• {symbol}: Grade {grade}\n"
                explanation += "\n"
            
            # Automation status
            automation = self.state.get("automation_status", "stopped")
            explanation += f"**🤖 Automation:** {automation.title()}\n\n"
            
            # Daily performance
            daily_pnl = self.state.get("daily_pnl", 0)
            pnl_emoji = "🟢" if daily_pnl > 0 else "🔴" if daily_pnl < 0 else "⚪"
            explanation += f"**💰 Daily P&L:** {pnl_emoji} ${daily_pnl:.2f}\n\n"
            
            # Recent decisions
            recent_decisions = self.state.get("decision_log", [])[-3:]
            if recent_decisions:
                explanation += f"**🧠 Recent Decisions:**\n"
                for decision in recent_decisions:
                    time_str = decision['timestamp'][:16].replace('T', ' ')
                    explanation += f"• {time_str}: {decision['reasoning']}\n"
            
            return explanation
    
    def explain_symbol_analysis(self, symbol: str) -> str:
        """Explain everything the system knows about a specific symbol."""
        with self._lock:
            explanation = f"🎯 **ANALYSIS FOR {symbol}**\n\n"
            
            # Current grade and reasoning
            grades = self.state.get("scan_grades", {})
            reasons = self.state.get("scan_reasons", {})
            
            if symbol in grades:
                grade = grades[symbol]
                reason = reasons.get(symbol, "No specific reason recorded")
                explanation += f"**📊 Current Grade:** {grade}\n"
                explanation += f"**🧠 Reasoning:** {reason}\n\n"
            
            # Position status
            positions = self.state.get("active_positions", {})
            if symbol in positions:
                pos = positions[symbol]
                explanation += f"**📈 Position Status:**\n"
                explanation += f"• Entry: ${pos.get('entry_price', 0):.2f}\n"
                explanation += f"• Current P&L: ${pos.get('unrealized_pnl', 0):.2f}\n"
                explanation += f"• Stop Loss: ${pos.get('stop_loss', 0):.2f}\n"
                explanation += f"• Target: ${pos.get('target_price', 0):.2f}\n\n"
            
            # Confidence score
            confidence_scores = self.state.get("confidence_scores", {})
            if symbol in confidence_scores:
                score = confidence_scores[symbol]
                explanation += f"**🎯 Confidence Score:** {score:.1f}/100\n\n"
            
            # Squeeze status
            squeeze_status = self.state.get("squeeze_status", {})
            if symbol in squeeze_status:
                status = squeeze_status[symbol]
                explanation += f"**🔥 TTM Squeeze:** {status}\n\n"
            
            # Recent decisions about this symbol
            symbol_decisions = [
                d for d in self.state.get("decision_log", [])
                if d.get("symbol") == symbol
            ][-3:]  # Last 3 decisions
            
            if symbol_decisions:
                explanation += f"**🧠 Recent Decisions:**\n"
                for decision in symbol_decisions:
                    time_str = decision['timestamp'][:16].replace('T', ' ')
                    explanation += f"• {time_str}: {decision['reasoning']}\n"
            
            return explanation
    
    def explain_why_trade_taken(self, symbol: str) -> str:
        """Explain why a trade was taken."""
        trade_reasons = self.state.get("trade_reasons", {})
        if symbol in trade_reasons:
            return f"🎯 **Why {symbol} was traded:** {trade_reasons[symbol]}"
        else:
            return f"❓ No trade reasoning found for {symbol}"
    
    def explain_why_trade_rejected(self, symbol: str) -> str:
        """Explain why a trade was rejected."""
        rejection_reasons = self.state.get("rejection_reasons", {})
        if symbol in rejection_reasons:
            return f"❌ **Why {symbol} was rejected:** {rejection_reasons[symbol]}"
        else:
            return f"❓ No rejection reasoning found for {symbol}"
    
    def get_session_summary(self) -> str:
        """Get summary of current trading session."""
        with self._lock:
            stats = self.state.get("session_stats", {})
            
            summary = "📊 **SESSION SUMMARY**\n\n"
            summary += f"**Trades Taken:** {stats.get('trades_taken', 0)}\n"
            summary += f"**Trades Rejected:** {stats.get('trades_rejected', 0)}\n"
            summary += f"**Scans Completed:** {stats.get('scans_completed', 0)}\n"
            summary += f"**Alerts Generated:** {stats.get('alerts_generated', 0)}\n"
            summary += f"**Daily P&L:** ${self.state.get('daily_pnl', 0):.2f}\n"
            summary += f"**Risk Exposure:** {self.state.get('risk_exposure', 0):.1%}\n"
            
            return summary
    
    def save_state_snapshot(self, event_type: str, description: str):
        """Save a snapshot of current state."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO state_snapshots 
                    (timestamp, state_data, event_type, description)
                    VALUES (?, ?, ?, ?)
                ''', (
                    datetime.now().isoformat(),
                    json.dumps(self.state),
                    event_type,
                    description
                ))
                conn.commit()
        except Exception as e:
            self.logger.error(f"Error saving state snapshot: {e}")
    
    def load_state(self):
        """Load the most recent state from database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT state_data FROM state_snapshots 
                    ORDER BY created_at DESC LIMIT 1
                ''')
                
                result = cursor.fetchone()
                if result:
                    saved_state = json.loads(result[0])
                    # Merge with current state, keeping live data
                    for key, value in saved_state.items():
                        if key not in ["last_update", "session_stats"]:
                            self.state[key] = value
        except Exception as e:
            self.logger.error(f"Error loading state: {e}")
    
    def remember_conversation(self, question: str, answer: str, context: str = ""):
        """Remember AI conversations for learning."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO ai_memory
                    (question, answer, context, timestamp)
                    VALUES (?, ?, ?, ?)
                ''', (
                    question,
                    answer,
                    context,
                    datetime.now().isoformat()
                ))
                conn.commit()
        except Exception as e:
            self.logger.error(f"Error storing AI memory: {e}")

    def deep_search_query(self, query: str, top_k: int = 5) -> str:
        """Perform deep search query and return formatted results."""
        if not DEEP_SEARCH_AVAILABLE:
            return "🔍 Deep Search not available - install required packages"

        try:
            deep_search = get_deep_search()
            results = deep_search.search(query, top_k)

            if not results:
                return f"🔍 No results found for: '{query}'"

            response = f"🔍 **Deep Search Results for: '{query}'**\n\n"

            for i, result in enumerate(results, 1):
                response += f"**{i}. {result.source.replace('_', ' ').title()}** (Score: {result.score:.2f})\n"
                response += f"{result.content[:200]}{'...' if len(result.content) > 200 else ''}\n"
                response += f"*{result.timestamp.strftime('%Y-%m-%d %H:%M')}*\n\n"

            return response

        except Exception as e:
            self.logger.error(f"Deep search query failed: {e}")
            return f"🔍 Deep search error: {e}"

    def get_context_for_ai_response(self, query: str) -> str:
        """Get relevant context for AI response generation."""
        if not DEEP_SEARCH_AVAILABLE:
            return ""

        try:
            deep_search = get_deep_search()
            return deep_search.get_context_for_query(query, max_context_length=1500)
        except Exception as e:
            self.logger.error(f"Context retrieval failed: {e}")
            return ""


# Global AI brain instance
_ai_brain = None

def get_ai_brain() -> AIBrain:
    """Get the global AI brain instance."""
    global _ai_brain
    if _ai_brain is None:
        _ai_brain = AIBrain()
    return _ai_brain


# Convenience functions for easy integration
def brain_update(key: str, value: Any, reason: str = None):
    """Update AI brain state."""
    get_ai_brain().update(key, value, reason)

def brain_get(key: str, default: Any = None) -> Any:
    """Get value from AI brain."""
    return get_ai_brain().get(key, default)

def brain_log_decision(decision_type: str, symbol: str, reasoning: str, data: Dict = None):
    """Log a decision to AI brain."""
    get_ai_brain().log_decision(decision_type, symbol, reasoning, data)

def brain_explain_state() -> str:
    """Get explanation of current system state."""
    return get_ai_brain().explain_current_state()

def brain_explain_symbol(symbol: str) -> str:
    """Get explanation of symbol analysis."""
    return get_ai_brain().explain_symbol_analysis(symbol)


if __name__ == "__main__":
    # Test the AI brain
    brain = AIBrain()
    
    print("🧠 Testing AI Self-Awareness Engine")
    print("=" * 40)
    
    # Test state updates
    brain.update("active_positions", {"AAPL": {"entry_price": 150.0, "unrealized_pnl": 25.0}}, "Opened AAPL position")
    brain.update("watchlist", ["NVDA", "TSLA", "MSFT"], "Updated watchlist with high-momentum stocks")
    brain.update("daily_pnl", 125.50, "Updated daily P&L")
    
    # Test decision logging
    brain.log_decision("trade_entry", "AAPL", "Strong TTM squeeze with A+ grade and bullish sentiment", {"grade": "A+", "confidence": 85})
    
    # Test explanations
    print("✅ Current State:")
    print(brain.explain_current_state())
    
    print("\n✅ AAPL Analysis:")
    print(brain.explain_symbol_analysis("AAPL"))
    
    print("\n✅ Session Summary:")
    print(brain.get_session_summary())
    
    print("🧠 AI Self-Awareness Engine ready!")
