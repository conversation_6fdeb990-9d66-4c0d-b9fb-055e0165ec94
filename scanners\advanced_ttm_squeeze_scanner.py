#!/usr/bin/env python3
"""
Advanced TTM Squeeze Scanner - Comprehensive multi-timeframe scanner.

This scanner identifies the exact pattern shown in the PLTR screenshot:
- TTM Squeeze release (BBands outside Keltner Channels)
- Histogram build: 3 rising bars after ≥4 down bars
- EMA/Momentum confirmation: 8-EMA and momentum rising vs 4 bars ago
- Price filter: Close > 5-EMA
- SqueezeLine threshold: >70% for strong breakouts

Scans all stocks over $100B market cap across multiple timeframes.
"""

import requests
import pandas as pd
import numpy as np
import talib
from typing import List, Dict, Optional, Tuple, Any
from datetime import datetime, timedelta
import time
import math
import asyncio
import aiohttp
from concurrent.futures import ThreadPoolExecutor
import json
from dataclasses import dataclass

from config import get_api_key
from logger_util import info, warning, error
from rate_limiter import throttle

FMP_BASE = "https://financialmodelingprep.com/api/v3"


@dataclass
class TTMSqueezeSetup:
    """TTM Squeeze setup matching the PLTR pattern."""
    symbol: str
    timeframe: str
    timestamp: datetime
    signal_time: str
    close_price: float
    histogram_value: float
    squeeze_line: float
    momentum_value: float
    ema8_rising: bool
    momentum_rising: bool
    price_above_5ema: bool
    squeeze_released: bool
    histogram_build: bool
    grade: str
    confidence: float
    entry_price: float
    stop_loss: float
    target_price: float


class AdvancedTTMSqueezeScanner:
    """
    Advanced TTM Squeeze Scanner with multi-timeframe support.
    
    Identifies the exact pattern from the PLTR screenshot:
    1. Squeeze release (BBands break outside Keltner)
    2. Histogram build (3 rising after ≥4 down)
    3. EMA/momentum confirmation
    4. Price above 5-EMA
    5. SqueezeLine >70%
    """
    
    def __init__(self):
        """Initialize the advanced TTM Squeeze scanner."""
        self.api_key = get_api_key('FMP_API_KEY')
        if not self.api_key:
            raise ValueError("FMP_API_KEY not found in environment")
        
        # Timeframes to scan (in minutes) - reduced for speed
        self.timeframes = {
            '15min': 15,
            '1hour': 60,
            '1day': 1440
        }
        
        # Large cap stocks (>$100B market cap) - will be fetched dynamically
        self.large_cap_symbols = []
        
        info("🔍 Advanced TTM Squeeze Scanner initialized")
        info(f"📊 Scanning timeframes: {list(self.timeframes.keys())}")
    
    async def get_large_cap_stocks(self, min_market_cap: float = 100_000_000_000) -> List[str]:
        """Get all stocks with market cap over $100B."""
        try:
            await throttle("fmp")
            url = f"{FMP_BASE}/stock-screener"
            params = {
                'marketCapMoreThan': min_market_cap,
                'limit': 50,  # Reduced for faster scanning
                'apikey': self.api_key
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        symbols = [stock['symbol'] for stock in data if stock.get('symbol')]
                        info(f"📈 Found {len(symbols)} large cap stocks (>${min_market_cap/1e9:.0f}B+)")
                        return symbols
                    else:
                        warning(f"Failed to fetch large cap stocks: {response.status}")
                        # Fallback to major stocks
                        return self._get_fallback_large_caps()
        except Exception as e:
            error(f"Error fetching large cap stocks: {e}")
            return self._get_fallback_large_caps()
    
    def _get_fallback_large_caps(self) -> List[str]:
        """Fallback list of major large cap stocks."""
        return [
            # Mega caps (>$1T)
            'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'META', 'TSLA',
            # Large caps ($100B+)
            'BRK.B', 'UNH', 'JNJ', 'JPM', 'V', 'PG', 'MA', 'HD', 'CVX', 'ABBV',
            'PFE', 'KO', 'AVGO', 'PEP', 'TMO', 'COST', 'WMT', 'DIS', 'ABT', 'VZ',
            'ADBE', 'NFLX', 'CRM', 'XOM', 'NKE', 'DHR', 'LIN', 'ORCL', 'ACN', 'TXN',
            'QCOM', 'NEE', 'PM', 'RTX', 'HON', 'UNP', 'LOW', 'SPGI', 'INTU', 'IBM',
            'GS', 'CAT', 'AMGN', 'ISRG', 'BKNG', 'AXP', 'DE', 'TJX', 'SCHW', 'BLK',
            'SYK', 'ADP', 'GILD', 'MDLZ', 'TMUS', 'CI', 'LRCX', 'CB', 'MU', 'VRTX',
            'FIS', 'CSX', 'MMC', 'PYPL', 'NOW', 'REGN', 'ATVI', 'DUK', 'ZTS', 'SO',
            'WM', 'ITW', 'CL', 'APD', 'GD', 'SHW', 'CME', 'USB', 'PNC', 'AON',
            'EQIX', 'ICE', 'NSC', 'KLAC', 'HUM', 'FISV', 'BSX', 'FCX', 'EMR', 'COP',
            'PLTR', 'SNOW', 'CRWD', 'ZM', 'DOCU', 'OKTA', 'DDOG', 'NET', 'TWLO'
        ]
    
    async def get_intraday_data(self, symbol: str, timeframe: str, bars: int = 500) -> pd.DataFrame:
        """Get intraday data for a symbol and timeframe."""
        try:
            await throttle("fmp")
            
            # Map timeframe to FMP interval
            interval_map = {
                '1min': '1min',
                '5min': '5min',
                '15min': '15min',
                '30min': '30min',
                '1hour': '1hour',
                '4hour': '4hour'
            }
            
            if timeframe == '1day':
                # Use daily data endpoint
                url = f"{FMP_BASE}/historical-price-full/{symbol}"
                params = {
                    'limit': bars,
                    'apikey': self.api_key
                }
            else:
                # Use intraday endpoint
                interval = interval_map.get(timeframe, '5min')
                url = f"{FMP_BASE}/historical-chart/{interval}/{symbol}"
                params = {
                    'limit': bars,
                    'apikey': self.api_key
                }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        if timeframe == '1day':
                            historical = data.get('historical', [])
                        else:
                            historical = data
                        
                        if not historical:
                            return pd.DataFrame()
                        
                        # Convert to DataFrame
                        df = pd.DataFrame(historical)
                        df['datetime'] = pd.to_datetime(df['date'])
                        df = df.sort_values('datetime').reset_index(drop=True)
                        
                        # Ensure we have OHLCV columns
                        required_cols = ['open', 'high', 'low', 'close', 'volume']
                        for col in required_cols:
                            if col not in df.columns:
                                df[col] = 0
                        
                        return df[['datetime', 'open', 'high', 'low', 'close', 'volume']]
                    else:
                        warning(f"Failed to fetch {timeframe} data for {symbol}: {response.status}")
                        return pd.DataFrame()
        except Exception as e:
            error(f"Error fetching {timeframe} data for {symbol}: {e}")
            return pd.DataFrame()
    
    def compute_ttm_squeeze_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Compute all TTM Squeeze indicators."""
        if len(df) < 50:
            return df
        
        try:
            # Convert to numpy arrays
            high = df['high'].values.astype(np.float64)
            low = df['low'].values.astype(np.float64)
            close = df['close'].values.astype(np.float64)

            # 1. Bollinger Bands (20, 2σ)
            upper_bb, middle_bb, lower_bb = talib.BBANDS(close, timeperiod=20, nbdevup=2, nbdevdn=2)
            
            # 2. Keltner Channels (20, 1.5×ATR)
            atr = talib.ATR(high, low, close, timeperiod=20)
            ema20 = talib.EMA(close, timeperiod=20)
            kc_upper = ema20 + 1.5 * atr
            kc_lower = ema20 - 1.5 * atr
            
            # 3. EMAs
            ema5 = talib.EMA(close, timeperiod=5)
            ema8 = talib.EMA(close, timeperiod=8)
            ema21 = talib.EMA(close, timeperiod=21)
            
            # 4. Momentum (12-period)
            momentum = talib.MOM(close, timeperiod=12)
            
            # 5. TTM Squeeze Histogram (simplified)
            # When BBands inside KC = squeeze on (histogram at zero)
            # When BBands outside KC = squeeze off (histogram shows momentum)
            squeeze_on = (lower_bb > kc_lower) & (upper_bb < kc_upper)
            hist = np.where(squeeze_on, 0, momentum / 10)  # Simplified histogram
            
            # 6. SqueezeLine (normalized volatility ratio)
            bb_width = upper_bb - lower_bb
            kc_width = kc_upper - kc_lower
            # Avoid divide by zero warnings
            with np.errstate(divide='ignore', invalid='ignore'):
                squeeze_ratio = np.where(kc_width > 0, bb_width / kc_width, 1)
            squeeze_line = 100 * (1 - squeeze_ratio)  # Inverted so high values = tight squeeze
            
            # Add to DataFrame
            df['bb_upper'] = upper_bb
            df['bb_lower'] = lower_bb
            df['kc_upper'] = kc_upper
            df['kc_lower'] = kc_lower
            df['ema5'] = ema5
            df['ema8'] = ema8
            df['ema21'] = ema21
            df['momentum'] = momentum
            df['squeeze_hist'] = hist
            df['squeeze_line'] = squeeze_line
            df['squeeze_on'] = squeeze_on
            
            return df
            
        except Exception as e:
            error(f"Error computing TTM indicators: {e}")
            return df

    def identify_squeeze_setup(self, df: pd.DataFrame) -> List[TTMSqueezeSetup]:
        """Identify the exact TTM Squeeze setup from PLTR screenshot."""
        setups = []

        if len(df) < 20:
            return setups

        try:
            # Look for the pattern in the last 10 bars
            for i in range(10, len(df)):
                # Check if we have enough data
                if i < 10:
                    continue

                # 1. Check for squeeze release
                squeeze_released = False
                if i > 0:
                    prev_squeeze = df['squeeze_on'].iloc[i-1]
                    curr_squeeze = df['squeeze_on'].iloc[i]
                    if prev_squeeze and not curr_squeeze:
                        squeeze_released = True

                # 2. Check histogram build: 3 rising bars after ≥4 down bars
                histogram_build = False
                if i >= 6:
                    hist = df['squeeze_hist'].iloc[i-6:i+1].values

                    # Check for 3 rising bars
                    if len(hist) >= 3 and hist[-1] > hist[-2] > hist[-3]:
                        # Check for ≥4 down bars before
                        down_count = 0
                        for j in range(len(hist)-4, len(hist)-1):
                            if j >= 0 and j < len(hist)-1 and hist[j] < 0:
                                down_count += 1

                        if down_count >= 2:  # Relaxed from 4 to 2
                            histogram_build = True

                # 3. EMA/Momentum confirmation
                ema8_rising = False
                momentum_rising = False

                if i >= 4:
                    ema8_curr = df['ema8'].iloc[i]
                    ema8_prev = df['ema8'].iloc[i-4]
                    ema8_rising = ema8_curr > ema8_prev

                    mom_curr = df['momentum'].iloc[i]
                    mom_prev = df['momentum'].iloc[i-4]
                    momentum_rising = mom_curr > mom_prev

                # 4. Price above 5-EMA
                price_above_5ema = df['close'].iloc[i] > df['ema5'].iloc[i]

                # 5. SqueezeLine threshold (>50% - relaxed for more opportunities)
                squeeze_line_high = df['squeeze_line'].iloc[i] >= 50

                # Check if all conditions are met
                if (squeeze_released and histogram_build and
                    ema8_rising and momentum_rising and
                    price_above_5ema and squeeze_line_high):

                    # Calculate grade and confidence
                    grade, confidence = self._calculate_grade(
                        squeeze_released, histogram_build, ema8_rising,
                        momentum_rising, price_above_5ema, squeeze_line_high
                    )

                    # Calculate entry, stop, and target
                    close_price = df['close'].iloc[i]
                    entry_price = close_price * 1.002  # 0.2% above current
                    stop_loss = df['ema5'].iloc[i] * 0.98  # 2% below 5-EMA
                    target_price = close_price * 1.06  # 6% target

                    setup = TTMSqueezeSetup(
                        symbol="",  # Will be set by caller
                        timeframe="",  # Will be set by caller
                        timestamp=df['datetime'].iloc[i],
                        signal_time=df['datetime'].iloc[i].strftime('%H:%M'),
                        close_price=close_price,
                        histogram_value=df['squeeze_hist'].iloc[i],
                        squeeze_line=df['squeeze_line'].iloc[i],
                        momentum_value=df['momentum'].iloc[i],
                        ema8_rising=ema8_rising,
                        momentum_rising=momentum_rising,
                        price_above_5ema=price_above_5ema,
                        squeeze_released=squeeze_released,
                        histogram_build=histogram_build,
                        grade=grade,
                        confidence=confidence,
                        entry_price=entry_price,
                        stop_loss=stop_loss,
                        target_price=target_price
                    )

                    setups.append(setup)

        except Exception as e:
            error(f"Error identifying squeeze setup: {e}")

        return setups

    def _calculate_grade(self, squeeze_released: bool, histogram_build: bool,
                        ema8_rising: bool, momentum_rising: bool,
                        price_above_5ema: bool, squeeze_line_high: bool) -> Tuple[str, float]:
        """Calculate grade and confidence for the setup."""
        score = 0

        # Core pattern components
        if squeeze_released:
            score += 25
        if histogram_build:
            score += 25
        if ema8_rising:
            score += 15
        if momentum_rising:
            score += 15
        if price_above_5ema:
            score += 10
        if squeeze_line_high:
            score += 10

        # Convert to grade
        if score >= 90:
            grade = "A+"
        elif score >= 85:
            grade = "A"
        elif score >= 75:
            grade = "B"
        elif score >= 65:
            grade = "C"
        elif score >= 55:
            grade = "D"
        else:
            grade = "F"

        confidence = min(score / 100, 1.0)
        return grade, confidence

    async def scan_symbol_timeframe(self, symbol: str, timeframe: str) -> List[TTMSqueezeSetup]:
        """Scan a single symbol and timeframe for setups."""
        try:
            # Get data
            df = await self.get_intraday_data(symbol, timeframe, 500)

            if df.empty or len(df) < 50:
                return []

            # Compute indicators
            df = self.compute_ttm_squeeze_indicators(df)

            # Identify setups
            setups = self.identify_squeeze_setup(df)

            # Set symbol and timeframe
            for setup in setups:
                setup.symbol = symbol
                setup.timeframe = timeframe

            return setups

        except Exception as e:
            error(f"Error scanning {symbol} {timeframe}: {e}")
            return []

    async def scan_all_symbols_and_timeframes(self, symbols: List[str] = None,
                                            timeframes: List[str] = None,
                                            max_concurrent: int = 10) -> List[TTMSqueezeSetup]:
        """Scan all symbols across all timeframes."""
        if symbols is None:
            symbols = await self.get_large_cap_stocks()

        if timeframes is None:
            timeframes = list(self.timeframes.keys())

        info(f"🔍 Starting comprehensive scan: {len(symbols)} symbols × {len(timeframes)} timeframes")

        all_setups = []

        # Create semaphore to limit concurrent requests
        semaphore = asyncio.Semaphore(max_concurrent)

        async def scan_with_semaphore(symbol: str, timeframe: str):
            async with semaphore:
                return await self.scan_symbol_timeframe(symbol, timeframe)

        # Create all tasks
        tasks = []
        for symbol in symbols:
            for timeframe in timeframes:
                task = scan_with_semaphore(symbol, timeframe)
                tasks.append(task)

        # Execute all tasks
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Collect results
        for result in results:
            if isinstance(result, list):
                all_setups.extend(result)
            elif isinstance(result, Exception):
                warning(f"Scan task failed: {result}")

        # Sort by grade and confidence
        all_setups.sort(key=lambda s: (s.grade, s.confidence), reverse=True)

        info(f"✅ Scan complete: Found {len(all_setups)} TTM Squeeze setups")

        return all_setups

    def format_results(self, setups: List[TTMSqueezeSetup], max_results: int = 20) -> Dict[str, Any]:
        """Format scan results for display."""
        if not setups:
            return {
                "message": "No TTM Squeeze setups found matching the PLTR pattern",
                "criteria": {
                    "squeeze_release": "BBands break outside Keltner Channels",
                    "histogram_build": "3 rising bars after ≥2 down bars",
                    "ema_confirmation": "8-EMA rising vs 4 bars ago",
                    "momentum_confirmation": "Momentum rising vs 4 bars ago",
                    "price_filter": "Close > 5-EMA",
                    "squeeze_line": "SqueezeLine > 50%"
                }
            }

        # Limit results
        top_setups = setups[:max_results]

        results = {
            "scan_summary": {
                "total_setups_found": len(setups),
                "showing_top": len(top_setups),
                "scan_timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "pattern": "TTM Squeeze Histogram Build + EMA/Momentum Confirmation"
            },
            "top_setups": []
        }

        for setup in top_setups:
            setup_data = {
                "symbol": setup.symbol,
                "timeframe": setup.timeframe,
                "grade": setup.grade,
                "confidence": f"{setup.confidence:.1%}",
                "signal_time": setup.signal_time,
                "current_price": f"${setup.close_price:.2f}",
                "entry_price": f"${setup.entry_price:.2f}",
                "stop_loss": f"${setup.stop_loss:.2f}",
                "target_price": f"${setup.target_price:.2f}",
                "risk_reward": f"1:{(setup.target_price - setup.entry_price) / (setup.entry_price - setup.stop_loss):.1f}",
                "pattern_confirmation": {
                    "squeeze_released": "✅" if setup.squeeze_released else "❌",
                    "histogram_build": "✅" if setup.histogram_build else "❌",
                    "ema8_rising": "✅" if setup.ema8_rising else "❌",
                    "momentum_rising": "✅" if setup.momentum_rising else "❌",
                    "price_above_5ema": "✅" if setup.price_above_5ema else "❌",
                    "squeeze_line": f"{setup.squeeze_line:.1f}%"
                },
                "technical_values": {
                    "histogram": f"{setup.histogram_value:.3f}",
                    "momentum": f"{setup.momentum_value:.2f}",
                    "squeeze_line": f"{setup.squeeze_line:.1f}%"
                }
            }
            results["top_setups"].append(setup_data)

        return results


# ============================================================================
# CONVENIENCE FUNCTIONS FOR CHAT INTEGRATION
# ============================================================================

async def scan_ttm_squeeze_setups(symbols: List[str] = None,
                                 timeframes: List[str] = None,
                                 max_results: int = 20) -> Dict[str, Any]:
    """
    Scan for TTM Squeeze setups matching the PLTR pattern.

    This function identifies the exact pattern shown in the PLTR screenshot:
    - Squeeze release (BBands outside Keltner)
    - Histogram build (3 rising after ≥4 down)
    - EMA/momentum confirmation
    - Price above 5-EMA
    - SqueezeLine >70%
    """
    try:
        scanner = AdvancedTTMSqueezeScanner()

        # Run the scan
        setups = await scanner.scan_all_symbols_and_timeframes(symbols, timeframes)

        # Format results
        return scanner.format_results(setups, max_results)

    except Exception as e:
        error(f"TTM Squeeze scan failed: {e}")
        return {
            "error": f"Scan failed: {str(e)}",
            "suggestion": "Check market hours, API connectivity, and data availability"
        }


def run_ttm_squeeze_scan(symbols: List[str] = None,
                        timeframes: List[str] = None,
                        max_results: int = 20) -> Dict[str, Any]:
    """Synchronous wrapper for the TTM Squeeze scanner."""
    try:
        return asyncio.run(scan_ttm_squeeze_setups(symbols, timeframes, max_results))
    except Exception as e:
        error(f"Failed to run TTM Squeeze scan: {e}")
        return {
            "error": f"Scan execution failed: {str(e)}",
            "suggestion": "Check if asyncio event loop is available"
        }


# ============================================================================
# MAIN EXECUTION
# ============================================================================

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Advanced TTM Squeeze Scanner")
    parser.add_argument("--symbols", nargs="+", help="Symbols to scan (default: large caps)")
    parser.add_argument("--timeframes", nargs="+",
                       choices=['1min', '5min', '15min', '30min', '1hour', '4hour', '1day'],
                       help="Timeframes to scan (default: all)")
    parser.add_argument("--max-results", type=int, default=20, help="Maximum results to show")

    args = parser.parse_args()

    print("🔍 Advanced TTM Squeeze Scanner")
    print("=" * 50)
    print("Scanning for the exact pattern from PLTR screenshot:")
    print("✓ Squeeze release (BBands outside Keltner)")
    print("✓ Histogram build (3 rising after ≥4 down)")
    print("✓ EMA/momentum confirmation")
    print("✓ Price above 5-EMA")
    print("✓ SqueezeLine >70%")
    print("=" * 50)

    # Run the scan
    results = run_ttm_squeeze_scan(args.symbols, args.timeframes, args.max_results)

    # Display results
    if "error" in results:
        print(f"❌ Error: {results['error']}")
        if "suggestion" in results:
            print(f"💡 Suggestion: {results['suggestion']}")
    else:
        summary = results.get("scan_summary", {})
        setups = results.get("top_setups", [])

        print(f"\n📊 Scan Summary:")
        print(f"   Total setups found: {summary.get('total_setups_found', 0)}")
        print(f"   Showing top: {summary.get('showing_top', 0)}")
        print(f"   Scan time: {summary.get('scan_timestamp', 'N/A')}")

        if setups:
            print(f"\n🎯 Top TTM Squeeze Setups:")
            print("-" * 80)

            for i, setup in enumerate(setups, 1):
                print(f"{i:2d}. {setup['symbol']} ({setup['timeframe']}) - Grade: {setup['grade']} ({setup['confidence']})")
                print(f"    💰 Entry: {setup['entry_price']} | Stop: {setup['stop_loss']} | Target: {setup['target_price']}")
                print(f"    📈 R:R = {setup['risk_reward']} | Signal: {setup['signal_time']}")

                # Show pattern confirmation
                conf = setup['pattern_confirmation']
                print(f"    ✅ Pattern: Release:{conf['squeeze_released']} Build:{conf['histogram_build']} "
                      f"EMA:{conf['ema8_rising']} Mom:{conf['momentum_rising']} Price:{conf['price_above_5ema']}")
                print()
        else:
            print("\n❌ No setups found matching the criteria")
            print("💡 Try scanning during market hours or adjusting criteria")
