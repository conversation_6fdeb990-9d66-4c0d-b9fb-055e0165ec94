#!/usr/bin/env python3
"""
Integrated MCP Setup for TTM Trading System

This script integrates the Alpaca MCP Server with our existing TTM trading system,
enhancing all components with direct AI trading capabilities.
"""

import os
import sys
from pathlib import Path

def main():
    print("🚀 TTM TRADING SYSTEM - INTEGRATED MCP SETUP")
    print("=" * 55)
    print()
    print("This will integrate Alpaca MCP Server with your existing TTM system:")
    print("✅ Enhance AI chat with direct trading capabilities")
    print("✅ Upgrade automation with natural language control")
    print("✅ Add real-time position analysis via chat")
    print("✅ Enable advanced options trading through conversation")
    print()

    # Check if user wants to proceed
    proceed = input("Do you want to proceed with the integration? (y/n): ").lower()
    if proceed != 'y':
        print("Setup cancelled.")
        return

    print("\n🔧 Starting integrated MCP setup...")

    try:
        # Initialize MCP manager
        from core.mcp_manager import get_mcp_manager, start_mcp_integration

        print("1. Initializing MCP manager...")
        manager = get_mcp_manager()

        print("2. Setting up MCP integration...")
        if start_mcp_integration():
            print("✅ MCP integration started successfully!")

            # Show integration status
            status = manager.get_status()
            print(f"\n📊 INTEGRATION STATUS:")
            print(f"• Initialized: {'✅' if status['initialized'] else '❌'}")
            print(f"• Running: {'✅' if status['running'] else '❌'}")
            print(f"• Server Path: {status['server_path']}")

            print(f"\n🚀 ENHANCED CAPABILITIES:")
            for capability in status['capabilities']:
                print(f"• {capability}")

            # Generate Claude config
            print("\n3. Generating Claude Desktop configuration...")
            api_key = os.getenv('ALPACA_API_KEY', 'your_alpaca_api_key')
            secret_key = os.getenv('ALPACA_SECRET_KEY', 'your_alpaca_secret_key')

            if manager.mcp_integration:
                config = manager.mcp_integration.create_claude_config(api_key, secret_key, paper_trading=True)
                if config:
                    print("✅ Claude Desktop config generated!")

            print("\n🎉 INTEGRATION COMPLETE!")
            print("\n📋 NEXT STEPS:")
            print("1. Open Claude Desktop")
            print("2. Go to Settings → Developer → Edit Config")
            print("3. Copy contents from: integrations/claude_desktop_config.json")
            print("4. Restart Claude Desktop")
            print("5. Test integration with: 'What's my account balance?'")

            print("\n💡 ENHANCED TTM WORKFLOW:")
            print("• Scanner finds A+ opportunity → 'Analyze this PLTR setup'")
            print("• AI provides detailed analysis → 'Execute this trade'")
            print("• Monitor via chat → 'How is my PLTR position performing?'")
            print("• Advanced strategies → 'Place bull call spread on TSLA'")

            print("\n🔄 KEEPING MCP SERVER RUNNING...")
            print("Keep this terminal open to maintain MCP integration")

            # Keep running and show periodic status
            try:
                import time
                while True:
                    time.sleep(30)  # Check every 30 seconds
                    if not manager.is_running:
                        print("⚠️ MCP Server stopped unexpectedly")
                        break
                    print("🔄 MCP Server running... (Ctrl+C to stop)")

            except KeyboardInterrupt:
                print("\n⏹️ Stopping MCP integration...")
                manager.stop()
                print("✅ MCP integration stopped")
        else:
            print("❌ MCP integration failed to start")
            print("\n🔧 TROUBLESHOOTING:")
            print("• Check that ALPACA_API_KEY and ALPACA_SECRET_KEY are set")
            print("• Ensure you have internet connection for MCP server download")
            print("• Verify Python dependencies are installed")

    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("\nPlease ensure you're running this from the TTM system root directory")
    except Exception as e:
        print(f"❌ Setup failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
