#!/usr/bin/env python3
"""
TotalRecall Enhanced - Complete System Backup to Desktop

This script creates a complete backup of your TotalRecall Enhanced trading system
to your desktop, preserving all functionality and configurations.
"""

import os
import shutil
import zipfile
from pathlib import Path
from datetime import datetime

def backup_totalrecall_to_desktop():
    """Create complete backup of TotalRecall Enhanced to desktop."""
    
    print("🚀 TOTALRECALL ENHANCED - COMPLETE SYSTEM BACKUP")
    print("=" * 60)
    
    # Source and destination paths
    source_dir = Path.cwd()  # Current TotalRecall directory
    desktop_path = Path(r"C:\Users\<USER>\OneDrive\Desktop")
    backup_name = f"TotalRecall_Enhanced_Backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    backup_dir = desktop_path / backup_name
    
    print(f"📂 Source: {source_dir}")
    print(f"🎯 Destination: {backup_dir}")
    
    try:
        # Create backup directory
        backup_dir.mkdir(parents=True, exist_ok=True)
        print(f"✅ Created backup directory: {backup_dir}")
        
        # Essential directories to backup
        essential_dirs = [
            "core",
            "trading", 
            "scanners",
            "data",
            "config",
            "docs",
            "integrations",
            "utils",
            "tests",
            "archive"
        ]
        
        # Essential files to backup
        essential_files = [
            "main.py",
            "requirements.txt",
            ".env",
            "README.md",
            "ENHANCED_MCP_INTEGRATION_README.md",
            "LAUNCH_READINESS_REPORT.md",
            "COMPLETE_FUNCTION_INVENTORY.md",
            "TOP_TIER_COMPARISON_ANALYSIS.md",
            "TRADING_CONFLICT_ANALYSIS.md",
            "MCP_DOCUMENTATION_SUMMARY.md",
            "DOCUMENTATION_ENHANCEMENT_PLAN.md",
            "setup_enhanced_mcp_integration.py",
            "setup_totalrecall_claude_integration.py",
            "test_enhanced_mcp_integration.py",
            "comprehensive_launch_test.py",
            "launch_enhanced_totalrecall.py",
            "backup_totalrecall_to_desktop.py"
        ]
        
        # Copy essential directories
        print("\n📁 COPYING DIRECTORIES:")
        for dir_name in essential_dirs:
            source_path = source_dir / dir_name
            if source_path.exists():
                dest_path = backup_dir / dir_name
                print(f"   📂 {dir_name}...")
                shutil.copytree(source_path, dest_path, ignore=shutil.ignore_patterns('__pycache__', '*.pyc'))
                print(f"   ✅ {dir_name} copied successfully")
            else:
                print(f"   ⚠️ {dir_name} not found - skipping")
        
        # Copy essential files
        print("\n📄 COPYING FILES:")
        for file_name in essential_files:
            source_path = source_dir / file_name
            if source_path.exists():
                dest_path = backup_dir / file_name
                print(f"   📄 {file_name}...")
                shutil.copy2(source_path, dest_path)
                print(f"   ✅ {file_name} copied successfully")
            else:
                print(f"   ⚠️ {file_name} not found - skipping")
        
        # Create setup instructions
        setup_instructions = f"""# 🚀 TotalRecall Enhanced - Setup Instructions

## 📊 SYSTEM OVERVIEW
This is your complete TotalRecall Enhanced trading system backup created on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}.

## 🛠️ SETUP INSTRUCTIONS

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Configure Environment
- Edit `.env` file with your API keys
- Ensure Alpaca API keys are correct
- Verify other API configurations

### 3. Test System
```bash
python main.py
```

### 4. Verify MCP Integration
```bash
python test_enhanced_mcp_integration.py
```

### 5. Run Comprehensive Test
```bash
python comprehensive_launch_test.py
```

## 🎯 SYSTEM CAPABILITIES

### Core Features:
- ✅ 90+ trading tools and functions
- ✅ TTM Squeeze analysis with A+ to F grading
- ✅ Professional options strategies (Iron Condors, Butterflies)
- ✅ Institutional algorithmic trading
- ✅ Real-time account integration via MCP
- ✅ AI-powered investment judgment
- ✅ Natural language trading interface
- ✅ Advanced risk management
- ✅ Performance analytics and tracking

### MCP Integration:
- ✅ 26 MCP functions from alpaca-mcp-server
- ✅ Real-time account data
- ✅ Enhanced order execution
- ✅ Options Greeks analysis
- ✅ Market data integration

### Safety Features:
- ✅ Paper trading by default
- ✅ Position size limits (10% max)
- ✅ Daily loss limits
- ✅ Conflict detection between systems
- ✅ Emergency stop capabilities

## 💡 QUICK START COMMANDS

### Basic Testing:
```
"What's my account balance?"
"Get quote for AAPL"
"System status"
"Scan for TTM opportunities"
```

### Advanced Features:
```
"Make me $50 today"
"Create Iron Condor for AAPL"
"Run momentum algorithm on AAPL"
"Check conflicts"
```

## 🛡️ SAFETY REMINDERS

1. **Always check system status** before trading
2. **Use paper trading** until comfortable
3. **Monitor for conflicts** between systems
4. **Follow risk management** rules
5. **Keep API keys secure**

## 📚 DOCUMENTATION

- `docs/` - Complete documentation
- `COMPLETE_FUNCTION_INVENTORY.md` - All 90+ functions
- `TOP_TIER_COMPARISON_ANALYSIS.md` - Competitive analysis
- `TRADING_CONFLICT_ANALYSIS.md` - Safety mechanisms

## 🚀 SUPPORT

Your TotalRecall Enhanced system is ready for professional trading!

**Total Tools:** 90+ functions
**System Status:** Fully operational
**MCP Integration:** Active
**Safety Level:** Professional grade

Happy trading! 🎯
"""
        
        # Save setup instructions
        setup_file = backup_dir / "SETUP_INSTRUCTIONS.md"
        setup_file.write_text(setup_instructions, encoding='utf-8')
        print(f"\n📋 Setup instructions created: {setup_file}")
        
        # Create a summary file
        summary = f"""# 🎯 TotalRecall Enhanced Backup Summary

**Backup Created:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**Source Directory:** {source_dir}
**Backup Location:** {backup_dir}

## 📊 BACKUP CONTENTS

### Directories Copied:
{chr(10).join(f'✅ {d}' for d in essential_dirs)}

### Files Copied:
{chr(10).join(f'✅ {f}' for f in essential_files)}

## 🚀 SYSTEM CAPABILITIES
- **Total Functions:** 90+ trading tools
- **MCP Integration:** 26 enhanced functions
- **TTM Analysis:** A+ to F grading system
- **Options Strategies:** Professional grade
- **Algorithmic Trading:** Institutional level
- **Risk Management:** Advanced safety systems

## 🛡️ SAFETY FEATURES
- Paper trading by default
- Position size limits
- Conflict detection
- Emergency controls
- Real-time monitoring

## 💡 NEXT STEPS
1. Read SETUP_INSTRUCTIONS.md
2. Install dependencies: pip install -r requirements.txt
3. Configure .env file
4. Test system: python main.py
5. Verify MCP: python test_enhanced_mcp_integration.py

Your complete TotalRecall Enhanced system is ready! 🎉
"""
        
        summary_file = backup_dir / "BACKUP_SUMMARY.md"
        summary_file.write_text(summary, encoding='utf-8')
        print(f"📋 Backup summary created: {summary_file}")
        
        # Create ZIP archive for easy transport
        zip_path = desktop_path / f"{backup_name}.zip"
        print(f"\n📦 Creating ZIP archive: {zip_path}")
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(backup_dir):
                for file in files:
                    file_path = Path(root) / file
                    arc_name = file_path.relative_to(backup_dir)
                    zipf.write(file_path, arc_name)
        
        print(f"✅ ZIP archive created: {zip_path}")
        
        # Calculate sizes
        def get_size(path):
            if path.is_file():
                return path.stat().st_size
            return sum(f.stat().st_size for f in path.rglob('*') if f.is_file())
        
        backup_size = get_size(backup_dir)
        zip_size = get_size(zip_path)
        
        print(f"\n📊 BACKUP COMPLETE!")
        print("=" * 40)
        print(f"📂 Backup Directory: {backup_dir}")
        print(f"📦 ZIP Archive: {zip_path}")
        print(f"💾 Backup Size: {backup_size / (1024*1024):.1f} MB")
        print(f"🗜️ ZIP Size: {zip_size / (1024*1024):.1f} MB")
        print(f"📁 Total Files: {len(list(backup_dir.rglob('*')))}")
        
        print(f"\n🎉 SUCCESS!")
        print("Your complete TotalRecall Enhanced system has been backed up to your desktop!")
        print(f"\n📍 Location: {backup_dir}")
        print(f"📦 ZIP File: {zip_path}")
        print(f"\n💡 Next Steps:")
        print("1. Read SETUP_INSTRUCTIONS.md in the backup folder")
        print("2. The ZIP file can be easily moved or shared")
        print("3. Your system is ready to run from the backup location")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during backup: {e}")
        return False

if __name__ == "__main__":
    success = backup_totalrecall_to_desktop()
    if success:
        print("\n🚀 TotalRecall Enhanced backup completed successfully!")
    else:
        print("\n❌ Backup failed. Please check the error messages above.")
