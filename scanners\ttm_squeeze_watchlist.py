"""TTM Squeeze Watchlist - Find Stocks BEFORE Breakout

This system monitors stocks that are currently IN SQUEEZE (building pressure)
so we can catch them RIGHT BEFORE the breakout happens. These are the real
opportunities - not the ones that have already broken out.

Features:
- Watchlist of stocks currently in squeeze
- Pressure building detection
- Breakout prediction alerts
- Real-time monitoring
- Historical squeeze tracking
"""
from __future__ import annotations

import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from pathlib import Path

from proper_ttm_squeeze_scanner import ProperTTMSqueezeScanner
from logger_util import info, warning


class TTMSqueezeWatchlist:
    """Watchlist system for TTM Squeeze opportunities BEFORE breakout."""
    
    def __init__(self):
        self.scanner = ProperTTMSqueezeScanner()
        self.watchlist_file = Path("ttm_squeeze_watchlist.json")
        self.watchlist = self.load_watchlist()
        
    def load_watchlist(self) -> Dict:
        """Load existing watchlist from file."""
        if self.watchlist_file.exists():
            try:
                with open(self.watchlist_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                warning(f"Failed to load watchlist: {e}")
        
        return {
            "in_squeeze": {},  # Stocks currently in squeeze
            "building_pressure": {},  # Stocks with increasing pressure
            "breakout_candidates": {},  # Stocks ready to break out
            "recent_breakouts": {},  # Recently broken out (to avoid)
            "last_updated": None
        }
    
    def save_watchlist(self):
        """Save watchlist to file."""
        try:
            self.watchlist["last_updated"] = datetime.now().isoformat()
            with open(self.watchlist_file, 'w') as f:
                json.dump(self.watchlist, f, indent=2)
        except Exception as e:
            warning(f"Failed to save watchlist: {e}")
    
    def analyze_squeeze_pressure(self, symbol: str, timeframe: str = '15min') -> Optional[Dict]:
        """Analyze squeeze pressure for a single symbol."""
        try:
            df = self.scanner.get_historical_data(symbol, timeframe, days=10)
            if df is None or len(df) < 50:
                return None
            
            # Calculate indicators
            df = self.scanner.calculate_bollinger_bands(df)
            df = self.scanner.calculate_keltner_channels(df)
            df = self.scanner.calculate_momentum(df)
            
            # Get recent data points
            latest = df.iloc[-1]
            prev_5 = df.iloc[-5] if len(df) > 5 else latest
            prev_10 = df.iloc[-10] if len(df) > 10 else latest
            
            # Calculate squeeze metrics
            bb_width = (latest['bb_upper'] - latest['bb_lower']) / latest['close']
            kc_width = (latest['kc_upper'] - latest['kc_lower']) / latest['close']
            
            # Squeeze condition: BB inside KC
            in_squeeze = (latest['bb_upper'] <= latest['kc_upper'] and 
                         latest['bb_lower'] >= latest['kc_lower'])
            
            # Pressure building: BB getting tighter relative to KC
            bb_width_5_ago = (prev_5['bb_upper'] - prev_5['bb_lower']) / prev_5['close']
            bb_width_10_ago = (prev_10['bb_upper'] - prev_10['bb_lower']) / prev_10['close']
            
            pressure_building = bb_width < bb_width_5_ago < bb_width_10_ago
            
            # Momentum building
            momentum_building = (latest['momentum'] > prev_5['momentum'] and 
                               abs(latest['momentum']) > abs(prev_10['momentum']))
            
            # Volume increasing
            volume_increasing = latest['volume'] > df['volume'].rolling(10).mean().iloc[-1]
            
            # Calculate squeeze duration (how long it's been in squeeze)
            squeeze_duration = 0
            for i in range(len(df)-1, max(0, len(df)-20), -1):
                row = df.iloc[i]
                if row['bb_upper'] <= row['kc_upper'] and row['bb_lower'] >= row['kc_lower']:
                    squeeze_duration += 1
                else:
                    break
            
            # Breakout readiness score
            breakout_score = 0
            if in_squeeze:
                breakout_score += 30
            if pressure_building:
                breakout_score += 25
            if momentum_building:
                breakout_score += 20
            if volume_increasing:
                breakout_score += 15
            if squeeze_duration >= 5:  # At least 5 periods in squeeze
                breakout_score += 10
            
            return {
                'symbol': symbol,
                'timeframe': timeframe,
                'in_squeeze': bool(in_squeeze),
                'pressure_building': bool(pressure_building),
                'momentum_building': bool(momentum_building),
                'volume_increasing': bool(volume_increasing),
                'squeeze_duration': int(squeeze_duration),
                'breakout_score': int(breakout_score),
                'bb_width': round(bb_width * 100, 2),  # As percentage
                'kc_width': round(kc_width * 100, 2),
                'momentum': round(latest['momentum'], 2),
                'price': round(latest['close'], 2),
                'volume': int(latest['volume']),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            warning(f"Error analyzing {symbol}: {e}")
            return None
    
    def scan_for_squeeze_candidates(self, symbols: List[str] = None) -> Dict:
        """Scan for stocks currently in squeeze (before breakout)."""
        if symbols is None:
            # Use large cap stocks
            from proper_ttm_squeeze_scanner import LARGE_CAP_STOCKS
            symbols = LARGE_CAP_STOCKS
        
        info(f"🔍 Scanning {len(symbols)} symbols for squeeze candidates...")
        
        results = {
            'in_squeeze': [],
            'building_pressure': [],
            'breakout_candidates': [],
            'scan_time': datetime.now().isoformat()
        }
        
        for symbol in symbols:
            for timeframe in ['5min', '15min', '1hour']:
                analysis = self.analyze_squeeze_pressure(symbol, timeframe)
                if analysis:
                    # Categorize based on analysis
                    if analysis['in_squeeze']:
                        if analysis['breakout_score'] >= 80:
                            results['breakout_candidates'].append(analysis)
                        elif analysis['pressure_building']:
                            results['building_pressure'].append(analysis)
                        else:
                            results['in_squeeze'].append(analysis)
        
        # Sort by breakout score
        for category in results:
            if isinstance(results[category], list):
                results[category].sort(key=lambda x: x.get('breakout_score', 0), reverse=True)
        
        return results
    
    def update_watchlist(self):
        """Update the watchlist with current market data."""
        info("🔄 Updating TTM Squeeze watchlist...")
        
        # Scan for current squeeze candidates
        scan_results = self.scan_for_squeeze_candidates()
        
        # Update watchlist categories
        self.watchlist['in_squeeze'] = {
            item['symbol'] + '_' + item['timeframe']: item 
            for item in scan_results['in_squeeze']
        }
        
        self.watchlist['building_pressure'] = {
            item['symbol'] + '_' + item['timeframe']: item 
            for item in scan_results['building_pressure']
        }
        
        self.watchlist['breakout_candidates'] = {
            item['symbol'] + '_' + item['timeframe']: item 
            for item in scan_results['breakout_candidates']
        }
        
        # Clean up old entries (older than 24 hours)
        cutoff_time = datetime.now() - timedelta(hours=24)
        for category in ['in_squeeze', 'building_pressure', 'breakout_candidates', 'recent_breakouts']:
            if category in self.watchlist:
                self.watchlist[category] = {
                    key: value for key, value in self.watchlist[category].items()
                    if datetime.fromisoformat(value.get('timestamp', '2020-01-01')) > cutoff_time
                }
        
        # Save updated watchlist
        self.save_watchlist()
        
        return scan_results
    
    def get_top_breakout_candidates(self, limit: int = 10) -> List[Dict]:
        """Get top breakout candidates from watchlist."""
        candidates = []
        
        # Get breakout candidates
        for item in self.watchlist.get('breakout_candidates', {}).values():
            candidates.append(item)
        
        # Get high-scoring building pressure stocks
        for item in self.watchlist.get('building_pressure', {}).values():
            if item.get('breakout_score', 0) >= 70:
                candidates.append(item)
        
        # Sort by breakout score and return top candidates
        candidates.sort(key=lambda x: x.get('breakout_score', 0), reverse=True)
        return candidates[:limit]
    
    def format_watchlist_report(self) -> str:
        """Format watchlist as readable report."""
        report = "🎯 TTM SQUEEZE WATCHLIST - STOCKS BEFORE BREAKOUT\n"
        report += "=" * 60 + "\n\n"
        
        # Top breakout candidates
        candidates = self.get_top_breakout_candidates(10)
        if candidates:
            report += "🚨 TOP BREAKOUT CANDIDATES (Ready to Break Out):\n"
            report += "-" * 50 + "\n"
            for item in candidates:
                status_icons = []
                if item['pressure_building']:
                    status_icons.append("🔥 PRESSURE")
                if item['momentum_building']:
                    status_icons.append("📈 MOMENTUM")
                if item['volume_increasing']:
                    status_icons.append("📊 VOLUME")
                
                status = " | ".join(status_icons) if status_icons else "⏳ BUILDING"
                
                report += f"📈 {item['symbol']} ({item['timeframe']}) - Score: {item['breakout_score']}\n"
                report += f"   Price: ${item['price']:.2f} | {status}\n"
                report += f"   Squeeze Duration: {item['squeeze_duration']} periods\n"
                report += f"   BB Width: {item['bb_width']:.2f}% | Momentum: {item['momentum']:.2f}\n\n"
        
        # Building pressure stocks
        pressure_stocks = list(self.watchlist.get('building_pressure', {}).values())
        if pressure_stocks:
            pressure_stocks.sort(key=lambda x: x.get('breakout_score', 0), reverse=True)
            report += "⚡ BUILDING PRESSURE (Watch Closely):\n"
            report += "-" * 40 + "\n"
            for item in pressure_stocks[:5]:
                report += f"📊 {item['symbol']} ({item['timeframe']}) - Score: {item['breakout_score']}\n"
                report += f"   Price: ${item['price']:.2f} | Duration: {item['squeeze_duration']} periods\n"
        
        # In squeeze stocks
        squeeze_stocks = list(self.watchlist.get('in_squeeze', {}).values())
        if squeeze_stocks:
            squeeze_stocks.sort(key=lambda x: x.get('squeeze_duration', 0), reverse=True)
            report += "\n⏳ IN SQUEEZE (Early Stage):\n"
            report += "-" * 30 + "\n"
            for item in squeeze_stocks[:5]:
                report += f"📋 {item['symbol']} ({item['timeframe']}) - Duration: {item['squeeze_duration']} periods\n"
        
        if not candidates and not pressure_stocks and not squeeze_stocks:
            report += "📊 No squeeze opportunities found at this time.\n"
            report += "This could mean the market is in a trending phase.\n"
        
        report += f"\n🕒 Last Updated: {self.watchlist.get('last_updated', 'Never')}\n"
        
        return report
    
    def monitor_breakouts(self) -> List[Dict]:
        """Monitor for actual breakouts from watchlist."""
        breakouts = []
        
        # Check breakout candidates for actual breakouts
        for key, item in list(self.watchlist.get('breakout_candidates', {}).items()):
            current_analysis = self.analyze_squeeze_pressure(item['symbol'], item['timeframe'])
            
            if current_analysis and not current_analysis['in_squeeze']:
                # Breakout detected!
                breakout_info = {
                    'symbol': item['symbol'],
                    'timeframe': item['timeframe'],
                    'breakout_time': datetime.now().isoformat(),
                    'previous_score': item['breakout_score'],
                    'price': current_analysis['price'],
                    'momentum': current_analysis['momentum']
                }
                breakouts.append(breakout_info)
                
                # Move to recent breakouts
                if 'recent_breakouts' not in self.watchlist:
                    self.watchlist['recent_breakouts'] = {}
                self.watchlist['recent_breakouts'][key] = breakout_info
                
                # Remove from candidates
                del self.watchlist['breakout_candidates'][key]
        
        if breakouts:
            self.save_watchlist()
        
        return breakouts


def run_watchlist_scan() -> str:
    """Run the watchlist scan and return formatted results."""
    try:
        watchlist = TTMSqueezeWatchlist()
        watchlist.update_watchlist()
        return watchlist.format_watchlist_report()
    except Exception as e:
        return f"❌ Error running watchlist scan: {str(e)}"


def monitor_watchlist_breakouts() -> str:
    """Monitor watchlist for breakouts."""
    try:
        watchlist = TTMSqueezeWatchlist()
        breakouts = watchlist.monitor_breakouts()
        
        if breakouts:
            result = "🚨 BREAKOUT ALERTS!\n\n"
            for breakout in breakouts:
                result += f"🔥 {breakout['symbol']} ({breakout['timeframe']}) BREAKING OUT!\n"
                result += f"   Price: ${breakout['price']:.2f}\n"
                result += f"   Momentum: {breakout['momentum']:.2f}\n"
                result += f"   Previous Score: {breakout['previous_score']}\n\n"
            return result
        else:
            return "📊 No breakouts detected from watchlist at this time."
            
    except Exception as e:
        return f"❌ Error monitoring breakouts: {str(e)}"


if __name__ == "__main__":
    print("🎯 TTM Squeeze Watchlist - Finding Stocks BEFORE Breakout")
    print("=" * 60)
    
    # Run watchlist scan
    result = run_watchlist_scan()
    print(result)
    
    print("\n" + "=" * 60)
    print("💡 This watchlist shows stocks IN SQUEEZE (before breakout)")
    print("💡 Monitor 'Top Breakout Candidates' for imminent breakouts")
    print("💡 Set alerts on high-scoring candidates")
    print("💡 Run this scan every 15-30 minutes during market hours")
