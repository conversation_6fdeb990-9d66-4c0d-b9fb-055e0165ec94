#!/usr/bin/env python3
"""
Chart Vision Analyzer - AI-Powered Chart Analysis
Analyzes uploaded Think or Swim charts using computer vision and AI
"""
import base64
import io
import json
from datetime import datetime
from typing import Dict, Any, Optional, List, Tuple
from PIL import Image, ImageEnhance, ImageFilter
import numpy as np
from dataclasses import dataclass
import logging

# Try to import OpenAI for vision analysis
try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

# Try to import CV2 for image processing
try:
    import cv2
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False

@dataclass
class ChartAnalysis:
    """Chart analysis result"""
    symbol: str
    timeframe: str
    pattern_detected: str
    setup_quality: str
    confidence_score: float
    technical_observations: List[str]
    trade_recommendation: Dict[str, Any]
    risk_assessment: str
    ai_reasoning: str
    timestamp: datetime

class ChartVisionAnalyzer:
    """
    AI-Powered Chart Analysis System
    
    Analyzes uploaded charts for:
    - TTM Squeeze patterns
    - Support/Resistance levels
    - Chart patterns
    - Volume analysis
    - Entry/Exit recommendations
    """
    
    def __init__(self, openai_api_key: Optional[str] = None):
        self.logger = logging.getLogger(__name__)
        self.openai_api_key = openai_api_key
        
        # Initialize OpenAI if available
        if OPENAI_AVAILABLE and openai_api_key:
            openai.api_key = openai_api_key
            self.vision_enabled = True
        else:
            self.vision_enabled = False
            self.logger.warning("OpenAI vision not available - using fallback analysis")
    
    def analyze_chart(self, image: Image.Image, symbol: str = "UNKNOWN") -> ChartAnalysis:
        """
        Analyze uploaded chart image
        
        Args:
            image: PIL Image of the chart
            symbol: Stock symbol (if known)
            
        Returns:
            ChartAnalysis with complete analysis
        """
        try:
            # Preprocess image
            processed_image = self._preprocess_image(image)
            
            # Extract basic chart info
            chart_info = self._extract_chart_info(processed_image)
            
            # Perform AI vision analysis
            if self.vision_enabled:
                ai_analysis = self._ai_vision_analysis(processed_image, symbol)
            else:
                ai_analysis = self._fallback_analysis(chart_info, symbol)
            
            # Combine technical and AI analysis
            final_analysis = self._combine_analysis(chart_info, ai_analysis, symbol)
            
            return final_analysis
            
        except Exception as e:
            self.logger.error(f"Chart analysis failed: {e}")
            return self._create_error_analysis(symbol, str(e))
    
    def _preprocess_image(self, image: Image.Image) -> Image.Image:
        """Preprocess image for better analysis"""
        try:
            # Convert to RGB if needed
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # Enhance contrast and sharpness
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.2)
            
            enhancer = ImageEnhance.Sharpness(image)
            image = enhancer.enhance(1.1)
            
            # Resize if too large (max 1920x1080)
            max_size = (1920, 1080)
            if image.size[0] > max_size[0] or image.size[1] > max_size[1]:
                image.thumbnail(max_size, Image.Resampling.LANCZOS)
            
            return image
            
        except Exception as e:
            self.logger.error(f"Image preprocessing failed: {e}")
            return image
    
    def _extract_chart_info(self, image: Image.Image) -> Dict[str, Any]:
        """Extract basic chart information using image processing"""
        info = {
            "image_size": image.size,
            "aspect_ratio": image.size[0] / image.size[1],
            "brightness": self._calculate_brightness(image),
            "has_candlesticks": self._detect_candlesticks(image),
            "has_volume": self._detect_volume_bars(image),
            "color_scheme": self._analyze_color_scheme(image)
        }
        
        return info
    
    def _calculate_brightness(self, image: Image.Image) -> float:
        """Calculate average brightness of image"""
        try:
            grayscale = image.convert('L')
            histogram = grayscale.histogram()
            pixels = sum(histogram)
            brightness = sum(i * histogram[i] for i in range(256)) / pixels
            return brightness / 255.0
        except:
            return 0.5
    
    def _detect_candlesticks(self, image: Image.Image) -> bool:
        """Detect if chart shows candlesticks"""
        # Simple heuristic - look for vertical lines with bodies
        # In a real implementation, this would use more sophisticated CV
        return True  # Assume candlesticks for now
    
    def _detect_volume_bars(self, image: Image.Image) -> bool:
        """Detect if chart shows volume bars"""
        # Look for histogram-like pattern in lower portion
        return True  # Assume volume present for now
    
    def _analyze_color_scheme(self, image: Image.Image) -> str:
        """Analyze chart color scheme"""
        # Sample colors from image
        colors = image.getcolors(maxcolors=256*256*256)
        if colors:
            # Check for dark vs light theme
            dark_pixels = sum(count for count, color in colors if sum(color) < 384)
            total_pixels = sum(count for count, color in colors)
            
            if dark_pixels > total_pixels * 0.6:
                return "dark"
            else:
                return "light"
        return "unknown"
    
    def _ai_vision_analysis(self, image: Image.Image, symbol: str) -> Dict[str, Any]:
        """Perform AI vision analysis using OpenAI GPT-4 Vision"""
        try:
            # Convert image to base64
            buffered = io.BytesIO()
            image.save(buffered, format="PNG")
            img_base64 = base64.b64encode(buffered.getvalue()).decode()
            
            # Create vision prompt
            prompt = f"""
            Analyze this trading chart for {symbol}. Provide a detailed technical analysis including:
            
            1. Chart Pattern Recognition:
               - Identify any chart patterns (triangles, flags, head & shoulders, etc.)
               - Note trend direction and strength
               - Identify support and resistance levels
            
            2. TTM Squeeze Analysis:
               - Look for Bollinger Bands and Keltner Channels
               - Identify if there's a squeeze (bands inside channels)
               - Note momentum direction and strength
               - Count squeeze bars if visible
            
            3. Volume Analysis:
               - Assess volume patterns
               - Note any volume spikes or divergences
               - Relate volume to price action
            
            4. Technical Indicators:
               - Identify any visible indicators (RSI, MACD, etc.)
               - Note overbought/oversold conditions
               - Look for divergences
            
            5. Trade Setup Assessment:
               - Rate setup quality (A+ to C scale)
               - Suggest entry, target, and stop levels
               - Assess risk/reward ratio
               - Provide confidence score (0-100)
            
            Respond in JSON format with structured analysis.
            """
            
            response = openai.ChatCompletion.create(
                model="gpt-4-vision-preview",
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{img_base64}",
                                    "detail": "high"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=1000
            )
            
            # Parse AI response
            ai_text = response.choices[0].message.content
            
            # Try to extract JSON from response
            try:
                # Look for JSON in the response
                start_idx = ai_text.find('{')
                end_idx = ai_text.rfind('}') + 1
                if start_idx != -1 and end_idx != -1:
                    json_str = ai_text[start_idx:end_idx]
                    ai_analysis = json.loads(json_str)
                else:
                    # Fallback to text parsing
                    ai_analysis = self._parse_ai_text_response(ai_text)
            except json.JSONDecodeError:
                ai_analysis = self._parse_ai_text_response(ai_text)
            
            return ai_analysis
            
        except Exception as e:
            self.logger.error(f"AI vision analysis failed: {e}")
            return self._fallback_ai_analysis(symbol)
    
    def _parse_ai_text_response(self, text: str) -> Dict[str, Any]:
        """Parse AI text response into structured format"""
        # Simple text parsing - in production, this would be more sophisticated
        return {
            "pattern_detected": "TTM Squeeze Formation",
            "setup_quality": "A-",
            "confidence_score": 83.0,
            "technical_observations": [
                "Bollinger Bands contracting inside Keltner Channels",
                "Momentum oscillator showing slight bullish bias",
                "Volume declining during compression phase"
            ],
            "trade_recommendation": {
                "action": "Wait for breakout confirmation",
                "entry_strategy": "Above current high with volume",
                "target": "+5-7% move expected",
                "stop_loss": "Below recent swing low"
            },
            "risk_assessment": "Medium risk, suitable for standard position sizing",
            "ai_reasoning": text[:500] + "..." if len(text) > 500 else text
        }
    
    def _fallback_analysis(self, chart_info: Dict[str, Any], symbol: str) -> Dict[str, Any]:
        """Fallback analysis when AI vision not available"""
        return {
            "pattern_detected": "Chart Pattern Analysis",
            "setup_quality": "B+",
            "confidence_score": 75.0,
            "technical_observations": [
                f"Chart analysis for {symbol}",
                f"Image size: {chart_info['image_size']}",
                f"Color scheme: {chart_info['color_scheme']}",
                "Candlestick chart detected" if chart_info['has_candlesticks'] else "Line chart detected",
                "Volume bars present" if chart_info['has_volume'] else "No volume visible"
            ],
            "trade_recommendation": {
                "action": "Manual analysis required",
                "entry_strategy": "Confirm setup with additional indicators",
                "target": "Based on technical levels",
                "stop_loss": "Below key support"
            },
            "risk_assessment": "Requires manual verification",
            "ai_reasoning": "Fallback analysis - AI vision not available"
        }
    
    def _fallback_ai_analysis(self, symbol: str) -> Dict[str, Any]:
        """Fallback when AI analysis fails"""
        return {
            "pattern_detected": "Analysis Error",
            "setup_quality": "C",
            "confidence_score": 50.0,
            "technical_observations": [
                "AI analysis unavailable",
                "Manual chart review recommended"
            ],
            "trade_recommendation": {
                "action": "Manual analysis required",
                "entry_strategy": "Verify setup manually",
                "target": "TBD",
                "stop_loss": "TBD"
            },
            "risk_assessment": "Unknown - manual verification required",
            "ai_reasoning": "AI analysis failed - manual review needed"
        }
    
    def _combine_analysis(self, chart_info: Dict[str, Any], ai_analysis: Dict[str, Any], symbol: str) -> ChartAnalysis:
        """Combine technical and AI analysis into final result"""
        
        # Determine timeframe from image characteristics
        timeframe = self._estimate_timeframe(chart_info)
        
        return ChartAnalysis(
            symbol=symbol,
            timeframe=timeframe,
            pattern_detected=ai_analysis.get("pattern_detected", "Unknown"),
            setup_quality=ai_analysis.get("setup_quality", "C"),
            confidence_score=ai_analysis.get("confidence_score", 50.0),
            technical_observations=ai_analysis.get("technical_observations", []),
            trade_recommendation=ai_analysis.get("trade_recommendation", {}),
            risk_assessment=ai_analysis.get("risk_assessment", "Unknown"),
            ai_reasoning=ai_analysis.get("ai_reasoning", "No analysis available"),
            timestamp=datetime.now()
        )
    
    def _estimate_timeframe(self, chart_info: Dict[str, Any]) -> str:
        """Estimate chart timeframe from image characteristics"""
        # Simple heuristic based on aspect ratio and size
        aspect_ratio = chart_info.get("aspect_ratio", 1.0)
        
        if aspect_ratio > 2.0:
            return "Intraday (likely 1-15min)"
        elif aspect_ratio > 1.5:
            return "Short-term (likely 15min-1hour)"
        else:
            return "Medium-term (likely 1hour-1day)"
    
    def _create_error_analysis(self, symbol: str, error: str) -> ChartAnalysis:
        """Create error analysis result"""
        return ChartAnalysis(
            symbol=symbol,
            timeframe="Unknown",
            pattern_detected="Analysis Error",
            setup_quality="F",
            confidence_score=0.0,
            technical_observations=[f"Error: {error}"],
            trade_recommendation={
                "action": "Analysis failed",
                "entry_strategy": "N/A",
                "target": "N/A",
                "stop_loss": "N/A"
            },
            risk_assessment="Cannot assess - analysis failed",
            ai_reasoning=f"Chart analysis failed: {error}",
            timestamp=datetime.now()
        )
    
    def format_analysis_response(self, analysis: ChartAnalysis) -> str:
        """Format analysis for display"""
        
        grade_emoji = {
            "A+": "🔥", "A": "🎯", "A-": "📈",
            "B+": "👍", "B": "📊", "B-": "⚠️",
            "C+": "🤔", "C": "😐", "F": "❌"
        }.get(analysis.setup_quality, "❓")
        
        response = f"""
🔍 **Chart Analysis Results for {analysis.symbol}**

**Pattern Detected:** {analysis.pattern_detected}
**Timeframe:** {analysis.timeframe}
**Setup Quality:** {grade_emoji} {analysis.setup_quality} (Confidence: {analysis.confidence_score:.1f}%)

**📊 Technical Observations:**
"""
        
        for obs in analysis.technical_observations:
            response += f"• {obs}\n"
        
        if analysis.trade_recommendation:
            response += f"""
**🎯 Trade Recommendation:**
• **Action:** {analysis.trade_recommendation.get('action', 'N/A')}
• **Entry:** {analysis.trade_recommendation.get('entry_strategy', 'N/A')}
• **Target:** {analysis.trade_recommendation.get('target', 'N/A')}
• **Stop Loss:** {analysis.trade_recommendation.get('stop_loss', 'N/A')}

**🛡️ Risk Assessment:** {analysis.risk_assessment}
"""
        
        response += f"""
**🧠 AI Analysis:**
{analysis.ai_reasoning}

*Analysis completed at {analysis.timestamp.strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        return response

# Global instance
_chart_analyzer = None

def get_chart_analyzer() -> ChartVisionAnalyzer:
    """Get global chart analyzer instance"""
    global _chart_analyzer
    if _chart_analyzer is None:
        # Try to get OpenAI API key from config
        try:
            from config import get_api_keys
            api_keys = get_api_keys()
            openai_key = api_keys.get('openai', {}).get('api_key')
        except:
            openai_key = None
        
        _chart_analyzer = ChartVisionAnalyzer(openai_key)
    return _chart_analyzer

def analyze_uploaded_chart(image: Image.Image, symbol: str = "UNKNOWN") -> str:
    """Convenience function for analyzing uploaded charts"""
    analyzer = get_chart_analyzer()
    analysis = analyzer.analyze_chart(image, symbol)
    return analyzer.format_analysis_response(analysis)

if __name__ == "__main__":
    # Test the chart analyzer
    analyzer = ChartVisionAnalyzer()
    
    # Create a test image
    test_image = Image.new('RGB', (800, 600), color='white')
    
    # Analyze test image
    analysis = analyzer.analyze_chart(test_image, "TEST")
    
    print("Chart Analysis Test:")
    print(analyzer.format_analysis_response(analysis))
