#!/usr/bin/env python3
"""Adaptive Learning Engine

Machine learning system that continuously improves TTM trading:
- Learns from successful and failed trades
- Adjusts confidence scoring based on outcomes
- Identifies winning patterns and market conditions
- Optimizes strategy selection
- Provides performance predictions
"""
import json
import sqlite3
import numpy as np
import pandas as pd
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
import logging


class TradingPattern:
    """Represents a learned trading pattern."""
    
    def __init__(self, pattern_id: str, conditions: Dict):
        self.pattern_id = pattern_id
        self.conditions = conditions
        
        # Performance metrics
        self.total_trades = 0
        self.winning_trades = 0
        self.total_pnl = 0.0
        self.avg_pnl = 0.0
        self.win_rate = 0.0
        self.avg_hold_time = 0.0
        self.confidence_adjustment = 0.0
        
        # Market conditions when pattern works best
        self.optimal_conditions = {}
        
        # Last updated
        self.last_updated = datetime.now()
    
    def update_performance(self, trade_outcome: Dict):
        """Update pattern performance with new trade outcome."""
        self.total_trades += 1
        pnl = trade_outcome.get('pnl', 0.0)
        self.total_pnl += pnl
        
        if pnl > 0:
            self.winning_trades += 1
        
        # Recalculate metrics
        self.win_rate = self.winning_trades / self.total_trades if self.total_trades > 0 else 0
        self.avg_pnl = self.total_pnl / self.total_trades if self.total_trades > 0 else 0
        
        # Update confidence adjustment
        self._calculate_confidence_adjustment()
        
        self.last_updated = datetime.now()
    
    def _calculate_confidence_adjustment(self):
        """Calculate confidence adjustment based on performance."""
        if self.total_trades < 5:  # Need minimum sample size
            self.confidence_adjustment = 0.0
            return
        
        # Base adjustment on win rate vs expected (60% baseline)
        expected_win_rate = 0.6
        win_rate_diff = self.win_rate - expected_win_rate
        
        # Base adjustment on average P&L
        pnl_factor = 1.0 if self.avg_pnl > 0 else 0.5
        
        # Calculate adjustment (-20% to +20%)
        self.confidence_adjustment = max(-20, min(20, win_rate_diff * 100 * pnl_factor))
    
    def matches_conditions(self, trade_data: Dict) -> bool:
        """Check if trade data matches this pattern's conditions."""
        for key, expected_value in self.conditions.items():
            if key not in trade_data:
                continue
            
            actual_value = trade_data[key]
            
            # Handle different value types
            if isinstance(expected_value, str):
                if actual_value != expected_value:
                    return False
            elif isinstance(expected_value, (int, float)):
                # Allow 10% tolerance for numeric values
                tolerance = abs(expected_value * 0.1)
                if abs(actual_value - expected_value) > tolerance:
                    return False
            elif isinstance(expected_value, dict) and 'min' in expected_value:
                # Range matching
                if not (expected_value['min'] <= actual_value <= expected_value['max']):
                    return False
        
        return True
    
    def to_dict(self) -> Dict:
        """Convert pattern to dictionary."""
        return {
            'pattern_id': self.pattern_id,
            'conditions': self.conditions,
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'win_rate': self.win_rate,
            'avg_pnl': self.avg_pnl,
            'confidence_adjustment': self.confidence_adjustment,
            'last_updated': self.last_updated.isoformat()
        }


class AdaptiveLearningEngine:
    """Adaptive learning system for TTM trading."""
    
    def __init__(self, db_path: str = "data/learning_engine.db"):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        
        # Learned patterns
        self.patterns = {}
        
        # Market condition tracking
        self.market_conditions = {}
        
        # Performance tracking
        self.performance_history = []
        
        # Initialize
        self.init_database()
        self.load_patterns()
    
    def init_database(self):
        """Initialize learning database."""
        Path(self.db_path).parent.mkdir(exist_ok=True)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Patterns table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS patterns (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    pattern_id TEXT UNIQUE,
                    conditions TEXT,
                    total_trades INTEGER DEFAULT 0,
                    winning_trades INTEGER DEFAULT 0,
                    total_pnl REAL DEFAULT 0,
                    confidence_adjustment REAL DEFAULT 0,
                    last_updated TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Trade outcomes table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS trade_outcomes (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    trade_id TEXT,
                    symbol TEXT,
                    entry_date TEXT,
                    exit_date TEXT,
                    pnl REAL,
                    setup_data TEXT,
                    market_conditions TEXT,
                    pattern_matches TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Market conditions table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS market_conditions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date TEXT UNIQUE,
                    volatility REAL,
                    trend_strength REAL,
                    volume_ratio REAL,
                    market_sentiment REAL,
                    conditions_data TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
    
    def load_patterns(self):
        """Load learned patterns from database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM patterns')
                
                for row in cursor.fetchall():
                    _, pattern_id, conditions_json, total_trades, winning_trades, total_pnl, confidence_adj, last_updated = row
                    
                    conditions = json.loads(conditions_json)
                    pattern = TradingPattern(pattern_id, conditions)
                    
                    pattern.total_trades = total_trades
                    pattern.winning_trades = winning_trades
                    pattern.total_pnl = total_pnl
                    pattern.confidence_adjustment = confidence_adj
                    pattern.last_updated = datetime.fromisoformat(last_updated)
                    
                    # Recalculate derived metrics
                    pattern.win_rate = winning_trades / total_trades if total_trades > 0 else 0
                    pattern.avg_pnl = total_pnl / total_trades if total_trades > 0 else 0
                    
                    self.patterns[pattern_id] = pattern
                    
                self.logger.info(f"Loaded {len(self.patterns)} learned patterns")
                
        except Exception as e:
            self.logger.error(f"Error loading patterns: {e}")
    
    def record_trade_outcome(self, trade_data: Dict):
        """Record a trade outcome for learning."""
        try:
            # Extract key information
            trade_id = trade_data.get('trade_id', '')
            symbol = trade_data.get('symbol', '')
            pnl = trade_data.get('pnl', 0.0)
            setup_data = trade_data.get('setup_data', {})
            
            # Store trade outcome
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO trade_outcomes (trade_id, symbol, entry_date, exit_date, pnl, setup_data)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    trade_id, symbol,
                    trade_data.get('entry_date', ''),
                    trade_data.get('exit_date', ''),
                    pnl,
                    json.dumps(setup_data)
                ))
                conn.commit()
            
            # Update matching patterns
            self._update_matching_patterns(trade_data)
            
            # Create new patterns if needed
            self._discover_new_patterns(trade_data)
            
            # Update performance history
            self.performance_history.append({
                'timestamp': datetime.now(),
                'pnl': pnl,
                'symbol': symbol,
                'setup_grade': setup_data.get('grade', 'Unknown')
            })
            
            self.logger.info(f"Recorded trade outcome: {symbol} ${pnl:.2f}")
            
        except Exception as e:
            self.logger.error(f"Error recording trade outcome: {e}")
    
    def adjust_setup_confidence(self, setup_data: Dict) -> float:
        """Adjust setup confidence based on learned patterns."""
        original_confidence = setup_data.get('confidence_score', 50.0)
        total_adjustment = 0.0
        matching_patterns = 0
        
        # Check all patterns for matches
        for pattern in self.patterns.values():
            if pattern.matches_conditions(setup_data) and pattern.total_trades >= 5:
                total_adjustment += pattern.confidence_adjustment
                matching_patterns += 1
        
        # Apply average adjustment
        if matching_patterns > 0:
            avg_adjustment = total_adjustment / matching_patterns
            adjusted_confidence = original_confidence + avg_adjustment
            
            # Keep within bounds
            adjusted_confidence = max(0, min(100, adjusted_confidence))
            
            self.logger.debug(f"Confidence adjusted: {original_confidence:.1f} -> {adjusted_confidence:.1f} "
                            f"(+{avg_adjustment:.1f} from {matching_patterns} patterns)")
            
            return adjusted_confidence
        
        return original_confidence
    
    def get_strategy_recommendations(self, market_conditions: Dict) -> List[Dict]:
        """Get strategy recommendations based on current market conditions."""
        recommendations = []
        
        # Analyze patterns that work well in current conditions
        for pattern in self.patterns.values():
            if (pattern.total_trades >= 10 and 
                pattern.win_rate > 0.6 and 
                pattern.avg_pnl > 0):
                
                # Calculate relevance to current conditions
                relevance = self._calculate_pattern_relevance(pattern, market_conditions)
                
                if relevance > 0.7:  # 70% relevance threshold
                    recommendations.append({
                        'pattern_id': pattern.pattern_id,
                        'conditions': pattern.conditions,
                        'win_rate': pattern.win_rate,
                        'avg_pnl': pattern.avg_pnl,
                        'relevance': relevance,
                        'confidence_boost': pattern.confidence_adjustment
                    })
        
        # Sort by relevance and performance
        recommendations.sort(key=lambda x: x['relevance'] * x['win_rate'], reverse=True)
        
        return recommendations[:5]  # Top 5 recommendations
    
    def get_learning_insights(self) -> Dict:
        """Get insights from learning data."""
        if not self.patterns:
            return {"message": "No learning data available yet"}
        
        # Calculate overall statistics
        total_patterns = len(self.patterns)
        active_patterns = len([p for p in self.patterns.values() if p.total_trades >= 5])
        
        # Find best performing patterns
        best_patterns = sorted(
            [p for p in self.patterns.values() if p.total_trades >= 10],
            key=lambda x: x.win_rate * x.avg_pnl,
            reverse=True
        )[:3]
        
        # Analyze grade performance
        grade_performance = self._analyze_grade_performance()
        
        # Market condition insights
        condition_insights = self._analyze_market_conditions()
        
        return {
            'total_patterns': total_patterns,
            'active_patterns': active_patterns,
            'best_patterns': [p.to_dict() for p in best_patterns],
            'grade_performance': grade_performance,
            'market_insights': condition_insights,
            'total_trades_learned': sum(p.total_trades for p in self.patterns.values())
        }
    
    def _update_matching_patterns(self, trade_data: Dict):
        """Update patterns that match the trade data."""
        setup_data = trade_data.get('setup_data', {})
        
        for pattern in self.patterns.values():
            if pattern.matches_conditions(setup_data):
                pattern.update_performance(trade_data)
                self._save_pattern(pattern)
    
    def _discover_new_patterns(self, trade_data: Dict):
        """Discover new patterns from trade data."""
        setup_data = trade_data.get('setup_data', {})
        pnl = trade_data.get('pnl', 0.0)
        
        # Only create patterns for profitable trades
        if pnl <= 0:
            return
        
        # Create pattern based on key characteristics
        key_conditions = {
            'grade': setup_data.get('grade'),
            'timeframe': setup_data.get('timeframe'),
            'momentum_direction': setup_data.get('momentum_direction'),
            'volume_ratio': {'min': setup_data.get('volume_ratio', 1.0) * 0.9,
                           'max': setup_data.get('volume_ratio', 1.0) * 1.1}
        }
        
        # Remove None values
        key_conditions = {k: v for k, v in key_conditions.items() if v is not None}
        
        # Create pattern ID
        pattern_id = f"{key_conditions.get('grade', 'X')}_{key_conditions.get('timeframe', '1D')}_{key_conditions.get('momentum_direction', 'neutral')}"
        
        # Create pattern if it doesn't exist
        if pattern_id not in self.patterns:
            pattern = TradingPattern(pattern_id, key_conditions)
            pattern.update_performance(trade_data)
            self.patterns[pattern_id] = pattern
            self._save_pattern(pattern)
            
            self.logger.info(f"Discovered new pattern: {pattern_id}")
    
    def _save_pattern(self, pattern: TradingPattern):
        """Save pattern to database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO patterns 
                    (pattern_id, conditions, total_trades, winning_trades, total_pnl, confidence_adjustment)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    pattern.pattern_id,
                    json.dumps(pattern.conditions),
                    pattern.total_trades,
                    pattern.winning_trades,
                    pattern.total_pnl,
                    pattern.confidence_adjustment
                ))
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"Error saving pattern: {e}")
    
    def _calculate_pattern_relevance(self, pattern: TradingPattern, market_conditions: Dict) -> float:
        """Calculate how relevant a pattern is to current market conditions."""
        # Simple relevance calculation - can be enhanced
        relevance = 1.0
        
        # Check if pattern conditions align with market conditions
        for condition_key, condition_value in pattern.conditions.items():
            if condition_key in market_conditions:
                market_value = market_conditions[condition_key]
                
                if isinstance(condition_value, str):
                    if condition_value != market_value:
                        relevance *= 0.8
                elif isinstance(condition_value, dict) and 'min' in condition_value:
                    if not (condition_value['min'] <= market_value <= condition_value['max']):
                        relevance *= 0.7
        
        return relevance
    
    def _analyze_grade_performance(self) -> Dict:
        """Analyze performance by TTM grade."""
        grade_stats = {}
        
        for pattern in self.patterns.values():
            grade = pattern.conditions.get('grade')
            if grade and pattern.total_trades >= 5:
                if grade not in grade_stats:
                    grade_stats[grade] = {'trades': 0, 'wins': 0, 'total_pnl': 0}
                
                grade_stats[grade]['trades'] += pattern.total_trades
                grade_stats[grade]['wins'] += pattern.winning_trades
                grade_stats[grade]['total_pnl'] += pattern.total_pnl
        
        # Calculate win rates
        for grade, stats in grade_stats.items():
            stats['win_rate'] = stats['wins'] / stats['trades'] if stats['trades'] > 0 else 0
            stats['avg_pnl'] = stats['total_pnl'] / stats['trades'] if stats['trades'] > 0 else 0
        
        return grade_stats
    
    def _analyze_market_conditions(self) -> Dict:
        """Analyze which market conditions produce best results."""
        # Placeholder for market condition analysis
        return {
            'best_volatility': 'normal',
            'best_trend': 'bullish',
            'best_volume': 'high',
            'insights': 'Patterns perform best in normal volatility with bullish trends'
        }


# Global learning engine instance
_learning_engine = None

def get_learning_engine() -> AdaptiveLearningEngine:
    """Get the global learning engine instance."""
    global _learning_engine
    if _learning_engine is None:
        _learning_engine = AdaptiveLearningEngine()
    return _learning_engine


if __name__ == "__main__":
    # Test the adaptive learning engine
    learning = AdaptiveLearningEngine()
    
    print("🧠 Testing Adaptive Learning Engine")
    print("=" * 45)
    
    # Test recording trade outcome
    sample_trade = {
        'trade_id': 'test_001',
        'symbol': 'AAPL',
        'pnl': 150.0,
        'setup_data': {
            'grade': 'A',
            'timeframe': '1D',
            'momentum_direction': 'bullish',
            'volume_ratio': 1.5,
            'confidence_score': 85.0
        }
    }
    
    learning.record_trade_outcome(sample_trade)
    print("✅ Recorded sample trade outcome")
    
    # Test confidence adjustment
    adjusted_confidence = learning.adjust_setup_confidence(sample_trade['setup_data'])
    print(f"✅ Confidence adjustment: {sample_trade['setup_data']['confidence_score']:.1f} -> {adjusted_confidence:.1f}")
    
    # Test insights
    insights = learning.get_learning_insights()
    print(f"✅ Learning insights: {insights['total_patterns']} patterns, {insights['total_trades_learned']} trades")
    
    print("🧠 Adaptive learning ready!")
