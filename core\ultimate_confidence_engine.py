#!/usr/bin/env python3
"""Ultimate Confidence Scoring Engine

Advanced multi-dimensional confidence scoring system that combines:
- Technical analysis signals (40% weight)
- Market sentiment analysis (25% weight)
- Options flow intelligence (20% weight)
- Economic/macro factors (10% weight)
- Historical pattern matching (5% weight)

Produces unified confidence scores (0-100) with detailed breakdowns.
"""
import json
import sqlite3
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
import logging


class ConfidenceComponent:
    """Represents a single component of confidence scoring."""
    
    def __init__(self, name: str, weight: float, description: str):
        self.name = name
        self.weight = weight
        self.description = description
        self.score = 0.0
        self.sub_scores = {}
        self.signals = []
        self.confidence_level = "Unknown"
    
    def calculate_score(self, data: Dict) -> float:
        """Calculate component score from input data."""
        # Override in subclasses
        return 50.0
    
    def get_detailed_breakdown(self) -> Dict:
        """Get detailed breakdown of component scoring."""
        return {
            "name": self.name,
            "weight": self.weight,
            "score": self.score,
            "weighted_score": self.score * self.weight,
            "confidence_level": self.confidence_level,
            "sub_scores": self.sub_scores,
            "signals": self.signals,
            "description": self.description
        }


class TechnicalAnalysisComponent(ConfidenceComponent):
    """Technical analysis confidence component."""
    
    def __init__(self):
        super().__init__(
            "Technical Analysis",
            0.40,
            "TTM squeeze, momentum, volume, and price action signals"
        )
    
    def calculate_score(self, data: Dict) -> float:
        """Calculate technical analysis confidence score."""
        score = 0.0
        
        # TTM Squeeze signals (30% of technical)
        ttm_score = self._calculate_ttm_score(data)
        self.sub_scores["ttm_squeeze"] = ttm_score
        score += ttm_score * 0.30
        
        # Momentum signals (25% of technical)
        momentum_score = self._calculate_momentum_score(data)
        self.sub_scores["momentum"] = momentum_score
        score += momentum_score * 0.25
        
        # Volume analysis (20% of technical)
        volume_score = self._calculate_volume_score(data)
        self.sub_scores["volume"] = volume_score
        score += volume_score * 0.20
        
        # Price action (15% of technical)
        price_score = self._calculate_price_action_score(data)
        self.sub_scores["price_action"] = price_score
        score += price_score * 0.15
        
        # Support/Resistance (10% of technical)
        sr_score = self._calculate_support_resistance_score(data)
        self.sub_scores["support_resistance"] = sr_score
        score += sr_score * 0.10
        
        self.score = score
        self._determine_confidence_level()
        self._generate_signals()
        
        return score
    
    def _calculate_ttm_score(self, data: Dict) -> float:
        """Calculate TTM squeeze specific score."""
        score = 50.0  # Base score
        
        # Squeeze status
        if data.get('bb_squeeze') and data.get('kc_squeeze'):
            score += 25  # Both squeezes active
            self.signals.append("🔥 Dual squeeze active (BB + KC)")
        elif data.get('bb_squeeze') or data.get('kc_squeeze'):
            score += 15  # One squeeze active
            self.signals.append("⚡ Single squeeze active")
        
        # Momentum histogram
        momentum_hist = data.get('momentum_histogram', [])
        if len(momentum_hist) >= 3:
            if all(h > 0 for h in momentum_hist[-3:]):
                score += 15
                self.signals.append("📈 Bullish momentum building")
            elif all(h < 0 for h in momentum_hist[-3:]):
                score += 15
                self.signals.append("📉 Bearish momentum building")
        
        # Squeeze release
        if data.get('squeeze_release'):
            score += 20
            self.signals.append("🚀 Squeeze release detected")
        
        return min(100, max(0, score))
    
    def _calculate_momentum_score(self, data: Dict) -> float:
        """Calculate momentum score."""
        score = 50.0
        
        # RSI positioning
        rsi = data.get('rsi', 50)
        if 30 <= rsi <= 70:
            score += 15  # Good RSI range
        elif rsi < 30:
            score += 10  # Oversold (potential reversal)
            self.signals.append("📊 RSI oversold")
        elif rsi > 70:
            score += 10  # Overbought (potential reversal)
            self.signals.append("📊 RSI overbought")
        
        # MACD signals
        macd_signal = data.get('macd_signal', 'neutral')
        if macd_signal == 'bullish_crossover':
            score += 20
            self.signals.append("🔄 MACD bullish crossover")
        elif macd_signal == 'bearish_crossover':
            score += 20
            self.signals.append("🔄 MACD bearish crossover")
        
        # Momentum direction clarity
        momentum_direction = data.get('momentum_direction', 'neutral')
        if momentum_direction in ['bullish', 'bearish']:
            score += 15
            self.signals.append(f"🎯 Clear {momentum_direction} momentum")
        
        return min(100, max(0, score))
    
    def _calculate_volume_score(self, data: Dict) -> float:
        """Calculate volume analysis score."""
        score = 50.0
        
        volume_ratio = data.get('volume_ratio', 1.0)
        
        if volume_ratio > 2.0:
            score += 25  # Very high volume
            self.signals.append("🔊 Exceptional volume surge")
        elif volume_ratio > 1.5:
            score += 20  # High volume
            self.signals.append("📢 High volume confirmation")
        elif volume_ratio > 1.2:
            score += 10  # Above average volume
            self.signals.append("📈 Above average volume")
        elif volume_ratio < 0.8:
            score -= 10  # Low volume
            self.signals.append("📉 Below average volume")
        
        # Volume trend
        volume_trend = data.get('volume_trend', 'neutral')
        if volume_trend == 'increasing':
            score += 15
            self.signals.append("📊 Volume trend increasing")
        
        return min(100, max(0, score))
    
    def _calculate_price_action_score(self, data: Dict) -> float:
        """Calculate price action score."""
        score = 50.0
        
        # Candlestick patterns
        candle_pattern = data.get('candlestick_pattern', 'none')
        pattern_scores = {
            'hammer': 15, 'doji': 10, 'engulfing': 20,
            'shooting_star': 15, 'spinning_top': 5
        }
        
        if candle_pattern in pattern_scores:
            score += pattern_scores[candle_pattern]
            self.signals.append(f"🕯️ {candle_pattern.title()} pattern")
        
        # Gap analysis
        gap_type = data.get('gap_type', 'none')
        if gap_type == 'breakaway_gap':
            score += 20
            self.signals.append("🚀 Breakaway gap")
        elif gap_type == 'continuation_gap':
            score += 15
            self.signals.append("➡️ Continuation gap")
        
        return min(100, max(0, score))
    
    def _calculate_support_resistance_score(self, data: Dict) -> float:
        """Calculate support/resistance score."""
        score = 50.0
        
        # Distance from key levels
        distance_from_support = data.get('distance_from_support', 0.05)
        distance_from_resistance = data.get('distance_from_resistance', 0.05)
        
        if distance_from_support < 0.02:  # Within 2% of support
            score += 15
            self.signals.append("🛡️ Near key support")
        
        if distance_from_resistance < 0.02:  # Within 2% of resistance
            score += 15
            self.signals.append("🚧 Near key resistance")
        
        # Breakout potential
        if data.get('breakout_setup'):
            score += 20
            self.signals.append("💥 Breakout setup identified")
        
        return min(100, max(0, score))
    
    def _determine_confidence_level(self):
        """Determine confidence level based on score."""
        if self.score >= 80:
            self.confidence_level = "Very High"
        elif self.score >= 70:
            self.confidence_level = "High"
        elif self.score >= 60:
            self.confidence_level = "Medium"
        elif self.score >= 50:
            self.confidence_level = "Low"
        else:
            self.confidence_level = "Very Low"
    
    def _generate_signals(self):
        """Generate summary signals."""
        if not self.signals:
            self.signals.append("📊 Standard technical setup")


class SentimentAnalysisComponent(ConfidenceComponent):
    """Market sentiment confidence component."""
    
    def __init__(self):
        super().__init__(
            "Market Sentiment",
            0.25,
            "Social sentiment, news analysis, and market psychology"
        )
    
    def calculate_score(self, data: Dict) -> float:
        """Calculate sentiment analysis confidence score."""
        score = 50.0  # Neutral baseline
        
        # Social sentiment (40% of sentiment)
        social_score = self._calculate_social_sentiment_score(data)
        self.sub_scores["social_sentiment"] = social_score
        score += (social_score - 50) * 0.40
        
        # News sentiment (35% of sentiment)
        news_score = self._calculate_news_sentiment_score(data)
        self.sub_scores["news_sentiment"] = news_score
        score += (news_score - 50) * 0.35
        
        # Market psychology (25% of sentiment)
        psychology_score = self._calculate_market_psychology_score(data)
        self.sub_scores["market_psychology"] = psychology_score
        score += (psychology_score - 50) * 0.25
        
        self.score = min(100, max(0, score))
        self._determine_confidence_level()
        self._generate_signals(data)
        
        return self.score
    
    def _calculate_social_sentiment_score(self, data: Dict) -> float:
        """Calculate social media sentiment score."""
        score = 50.0
        
        # Reddit sentiment
        reddit_sentiment = data.get('reddit_sentiment', 0.0)
        if reddit_sentiment > 0.3:
            score += 20
            self.signals.append("🔥 Strong Reddit bullishness")
        elif reddit_sentiment > 0.1:
            score += 10
            self.signals.append("📈 Positive Reddit sentiment")
        elif reddit_sentiment < -0.3:
            score += 15  # Contrarian signal
            self.signals.append("🔄 Extreme Reddit bearishness (contrarian)")
        
        # Twitter/X sentiment
        twitter_sentiment = data.get('twitter_sentiment', 0.0)
        if abs(twitter_sentiment) > 0.2:
            score += 10
            direction = "bullish" if twitter_sentiment > 0 else "bearish"
            self.signals.append(f"🐦 Strong Twitter {direction} sentiment")
        
        # Social buzz volume
        social_buzz = data.get('social_buzz', 0.0)
        if social_buzz > 0.7:
            score += 15
            self.signals.append("📢 High social media buzz")
        
        return min(100, max(0, score))
    
    def _calculate_news_sentiment_score(self, data: Dict) -> float:
        """Calculate news sentiment score."""
        score = 50.0
        
        # News sentiment polarity
        news_sentiment = data.get('news_sentiment', 0.0)
        if news_sentiment > 0.2:
            score += 15
            self.signals.append("📰 Positive news sentiment")
        elif news_sentiment < -0.2:
            score += 10  # Bad news can create opportunities
            self.signals.append("📰 Negative news (opportunity)")
        
        # News volume/frequency
        news_volume = data.get('news_volume', 0.0)
        if news_volume > 0.5:
            score += 10
            self.signals.append("📺 High news coverage")
        
        # Analyst upgrades/downgrades
        analyst_sentiment = data.get('analyst_sentiment', 'neutral')
        if analyst_sentiment == 'upgrade':
            score += 15
            self.signals.append("⬆️ Analyst upgrade")
        elif analyst_sentiment == 'downgrade':
            score += 5  # Contrarian opportunity
            self.signals.append("⬇️ Analyst downgrade (contrarian)")
        
        return min(100, max(0, score))
    
    def _calculate_market_psychology_score(self, data: Dict) -> float:
        """Calculate market psychology score."""
        score = 50.0
        
        # Fear & Greed Index
        fear_greed = data.get('fear_greed_index', 50)
        if fear_greed < 25:  # Extreme fear
            score += 20  # Contrarian opportunity
            self.signals.append("😨 Extreme fear (contrarian opportunity)")
        elif fear_greed > 75:  # Extreme greed
            score += 10  # Momentum opportunity
            self.signals.append("🤑 Extreme greed (momentum play)")
        
        # Put/Call ratio
        put_call_ratio = data.get('put_call_ratio', 1.0)
        if put_call_ratio > 1.2:  # High put activity
            score += 15  # Contrarian bullish
            self.signals.append("🔄 High put/call ratio (contrarian bullish)")
        elif put_call_ratio < 0.8:  # High call activity
            score += 10  # Momentum bullish
            self.signals.append("📞 Low put/call ratio (momentum bullish)")
        
        return min(100, max(0, score))
    
    def _determine_confidence_level(self):
        """Determine confidence level based on score."""
        if self.score >= 75:
            self.confidence_level = "Very High"
        elif self.score >= 65:
            self.confidence_level = "High"
        elif self.score >= 55:
            self.confidence_level = "Medium"
        elif self.score >= 45:
            self.confidence_level = "Low"
        else:
            self.confidence_level = "Very Low"
    
    def _generate_signals(self, data: Dict):
        """Generate sentiment summary signals."""
        if not self.signals:
            self.signals.append("😐 Neutral market sentiment")


class OptionsFlowComponent(ConfidenceComponent):
    """Options flow intelligence component."""
    
    def __init__(self):
        super().__init__(
            "Options Flow",
            0.20,
            "Smart money options activity and unusual flow detection"
        )
    
    def calculate_score(self, data: Dict) -> float:
        """Calculate options flow confidence score."""
        score = 50.0
        
        # Unusual options activity
        unusual_activity = data.get('unusual_options_activity', False)
        if unusual_activity:
            score += 25
            self.signals.append("🚨 Unusual options activity detected")
        
        # Options flow direction
        flow_direction = data.get('options_flow_direction', 'neutral')
        if flow_direction == 'bullish':
            score += 20
            self.signals.append("📈 Bullish options flow")
        elif flow_direction == 'bearish':
            score += 20
            self.signals.append("📉 Bearish options flow")
        
        # Smart money indicators
        smart_money_flow = data.get('smart_money_flow', 0.0)
        if smart_money_flow > 0.3:
            score += 15
            self.signals.append("🧠 Smart money accumulation")
        elif smart_money_flow < -0.3:
            score += 15
            self.signals.append("🧠 Smart money distribution")
        
        # Options volume vs average
        options_volume_ratio = data.get('options_volume_ratio', 1.0)
        if options_volume_ratio > 2.0:
            score += 15
            self.signals.append("🔊 Exceptional options volume")
        elif options_volume_ratio > 1.5:
            score += 10
            self.signals.append("📢 High options volume")
        
        self.score = min(100, max(0, score))
        self._determine_confidence_level()
        
        return self.score
    
    def _determine_confidence_level(self):
        """Determine confidence level based on score."""
        if self.score >= 80:
            self.confidence_level = "Very High"
        elif self.score >= 70:
            self.confidence_level = "High"
        elif self.score >= 60:
            self.confidence_level = "Medium"
        elif self.score >= 50:
            self.confidence_level = "Low"
        else:
            self.confidence_level = "Very Low"


class UltimateConfidenceEngine:
    """Ultimate confidence scoring engine combining all components."""
    
    def __init__(self, db_path: str = "data/confidence_engine.db"):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        
        # Initialize components
        self.components = {
            'technical': TechnicalAnalysisComponent(),
            'sentiment': SentimentAnalysisComponent(),
            'options': OptionsFlowComponent(),
            # Add more components as needed
        }
        
        # Confidence thresholds
        self.confidence_thresholds = {
            'A+': 90,
            'A': 80,
            'B+': 70,
            'B': 60,
            'C+': 50,
            'C': 40
        }
        
        # Initialize database
        self.init_database()
    
    def init_database(self):
        """Initialize confidence scoring database."""
        Path(self.db_path).parent.mkdir(exist_ok=True)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Confidence scores table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS confidence_scores (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT,
                    timestamp TEXT,
                    overall_score REAL,
                    grade TEXT,
                    component_scores TEXT,
                    signals TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
    
    def calculate_confidence(self, symbol: str, data: Dict) -> Dict:
        """Calculate ultimate confidence score for a trading setup."""
        try:
            # Calculate component scores
            component_results = {}
            total_weighted_score = 0.0
            all_signals = []
            
            for name, component in self.components.items():
                component_score = component.calculate_score(data)
                component_results[name] = component.get_detailed_breakdown()
                total_weighted_score += component_score * component.weight
                all_signals.extend(component.signals)
            
            # Calculate overall confidence score
            overall_score = total_weighted_score
            
            # Assign grade
            grade = self._assign_grade(overall_score)
            
            # Create confidence result
            result = {
                'symbol': symbol,
                'timestamp': datetime.now().isoformat(),
                'overall_score': overall_score,
                'grade': grade,
                'confidence_level': self._get_confidence_level(overall_score),
                'component_breakdown': component_results,
                'key_signals': all_signals[:10],  # Top 10 signals
                'recommendation': self._get_recommendation(overall_score),
                'risk_assessment': self._assess_risk(overall_score, component_results)
            }
            
            # Store result
            self._store_confidence_score(result)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error calculating confidence for {symbol}: {e}")
            return {
                'symbol': symbol,
                'error': str(e),
                'overall_score': 0.0,
                'grade': 'F'
            }
    
    def _assign_grade(self, score: float) -> str:
        """Assign letter grade based on confidence score."""
        for grade, threshold in self.confidence_thresholds.items():
            if score >= threshold:
                return grade
        return 'F'
    
    def _get_confidence_level(self, score: float) -> str:
        """Get confidence level description."""
        if score >= 90:
            return "Extremely High"
        elif score >= 80:
            return "Very High"
        elif score >= 70:
            return "High"
        elif score >= 60:
            return "Medium"
        elif score >= 50:
            return "Low"
        else:
            return "Very Low"
    
    def _get_recommendation(self, score: float) -> str:
        """Get trading recommendation based on confidence score."""
        if score >= 85:
            return "Strong Buy/Sell Signal"
        elif score >= 75:
            return "Buy/Sell Signal"
        elif score >= 65:
            return "Consider Position"
        elif score >= 55:
            return "Watch Closely"
        elif score >= 45:
            return "Proceed with Caution"
        else:
            return "Avoid Trade"
    
    def _assess_risk(self, score: float, components: Dict) -> Dict:
        """Assess risk based on confidence score and components."""
        # Calculate risk level
        if score >= 80:
            risk_level = "Low"
            suggested_position_size = "Full Position"
        elif score >= 70:
            risk_level = "Medium"
            suggested_position_size = "75% Position"
        elif score >= 60:
            risk_level = "Medium-High"
            suggested_position_size = "50% Position"
        else:
            risk_level = "High"
            suggested_position_size = "25% Position or Avoid"
        
        # Check for conflicting signals
        technical_score = components.get('technical', {}).get('score', 50)
        sentiment_score = components.get('sentiment', {}).get('score', 50)
        
        conflicting_signals = abs(technical_score - sentiment_score) > 30
        
        return {
            'risk_level': risk_level,
            'suggested_position_size': suggested_position_size,
            'conflicting_signals': conflicting_signals,
            'confidence_consistency': not conflicting_signals
        }
    
    def _store_confidence_score(self, result: Dict):
        """Store confidence score in database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO confidence_scores 
                    (symbol, timestamp, overall_score, grade, component_scores, signals)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    result['symbol'],
                    result['timestamp'],
                    result['overall_score'],
                    result['grade'],
                    json.dumps(result['component_breakdown']),
                    json.dumps(result['key_signals'])
                ))
                conn.commit()
        except Exception as e:
            self.logger.error(f"Error storing confidence score: {e}")
    
    def get_confidence_history(self, symbol: str, days: int = 30) -> List[Dict]:
        """Get confidence score history for a symbol."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                start_date = (datetime.now() - timedelta(days=days)).isoformat()
                
                cursor.execute('''
                    SELECT timestamp, overall_score, grade, signals
                    FROM confidence_scores 
                    WHERE symbol = ? AND timestamp >= ?
                    ORDER BY timestamp DESC
                ''', (symbol, start_date))
                
                results = []
                for row in cursor.fetchall():
                    timestamp, score, grade, signals_json = row
                    results.append({
                        'timestamp': timestamp,
                        'score': score,
                        'grade': grade,
                        'signals': json.loads(signals_json)
                    })
                
                return results
                
        except Exception as e:
            self.logger.error(f"Error getting confidence history: {e}")
            return []


# Global confidence engine instance
_confidence_engine = None

def get_confidence_engine() -> UltimateConfidenceEngine:
    """Get the global confidence engine instance."""
    global _confidence_engine
    if _confidence_engine is None:
        _confidence_engine = UltimateConfidenceEngine()
    return _confidence_engine


if __name__ == "__main__":
    # Test the ultimate confidence engine
    engine = UltimateConfidenceEngine()
    
    print("🎯 Testing Ultimate Confidence Engine")
    print("=" * 45)
    
    # Test confidence calculation
    sample_data = {
        'bb_squeeze': True,
        'kc_squeeze': True,
        'momentum_direction': 'bullish',
        'volume_ratio': 1.8,
        'rsi': 65,
        'reddit_sentiment': 0.4,
        'news_sentiment': 0.2,
        'options_flow_direction': 'bullish',
        'unusual_options_activity': True
    }
    
    result = engine.calculate_confidence('AAPL', sample_data)
    
    print(f"✅ Confidence Analysis for AAPL:")
    print(f"   Overall Score: {result['overall_score']:.1f}/100")
    print(f"   Grade: {result['grade']}")
    print(f"   Confidence Level: {result['confidence_level']}")
    print(f"   Recommendation: {result['recommendation']}")
    print(f"   Risk Level: {result['risk_assessment']['risk_level']}")
    print(f"   Key Signals: {len(result['key_signals'])}")
    
    # Test component breakdown
    for component_name, component_data in result['component_breakdown'].items():
        print(f"   {component_data['name']}: {component_data['score']:.1f} ({component_data['confidence_level']})")
    
    print("🎯 Ultimate confidence engine ready!")
