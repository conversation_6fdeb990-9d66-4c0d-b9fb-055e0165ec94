"""Comprehensive Stock Analysis Tool – combines multiple FMP + Alpaca endpoints.

Exposes `analyze_stock_comprehensive` which will be registered as a function
callable by GPT.  It returns a dict with quote, fundamentals, technicals, news
sentiment and a simple recommendation field.
"""
from __future__ import annotations

import statistics
from typing import Dict, Any

import requests
from datetime import datetime, timedelta, timezone

from config import get_api_key
from logger_util import info, warning
from datetime import datetime

FMP_BASE = "https://financialmodelingprep.com/api/v3"


def _fmp(endpoint: str) -> str:
    return f"{FMP_BASE}{endpoint}&apikey={get_api_key('FMP_API_KEY')}"


def _get_json(url: str) -> Any:
    resp = requests.get(url, timeout=15)
    return resp.json()


def _format_news_cleanly(news_articles: list) -> str:
    """Format news articles in a clean, readable format."""
    if not news_articles:
        return "📰 **Recent News:** No recent news available"

    formatted = "📰 **Recent News:**\n\n"

    for i, article in enumerate(news_articles, 1):
        title = article.get('title', 'No title')
        published_date = article.get('publishedDate', '')
        site = article.get('site', 'Unknown')

        # Clean up the date
        try:
            if published_date:
                date_obj = datetime.fromisoformat(published_date.replace('Z', '+00:00'))
                clean_date = date_obj.strftime('%B %d, %Y')
            else:
                clean_date = 'Recent'
        except:
            clean_date = 'Recent'

        # Format each article cleanly
        formatted += f"**{i}. {title}**\n"
        formatted += f"   📅 {clean_date} • 🌐 {site}\n\n"

    return formatted


def analyze_stock_comprehensive(symbol: str) -> Dict[str, Any]:
    symbol = symbol.upper()
    info("📊 Deep analysis", symbol=symbol)

    # 1. Quote
    quote = _get_json(_fmp(f"/quote/{symbol}?"))[0]

    # 2. Ratios
    ratios = _get_json(_fmp(f"/ratios-ttm/{symbol}?"))[0]

    # 3. Insider trading (last 30 days)
    insider = _get_json(_fmp(f"/insider-trading?symbol={symbol}&page=0&limit=30"))
    insider_purchases = [i for i in insider if i.get("transactionType") == "P-Purchase"]
    insider_sales = [i for i in insider if i.get("transactionType") == "S-Sale"]
    net_insider = sum(float(i.get("securitiesTransacted", 0)) * float(i.get("price", 0)) for i in insider_purchases) - sum(
        float(i.get("securitiesTransacted", 0)) * float(i.get("price", 0)) for i in insider_sales
    )

    # 4. News sentiment (last 5 articles) - Clean formatting
    news = _get_json(_fmp(f"/stock_news?tickers={symbol}&limit=5"))
    sentiment_scores = [a.get("sentiment", 0) for a in news if a.get("sentiment") is not None]
    avg_sentiment = statistics.mean(sentiment_scores) if sentiment_scores else 0

    # Format news cleanly
    formatted_news = _format_news_cleanly(news[:3])  # Top 3 articles only

    # 5. Simple recommendation logic
    recommendation = "BUY" if quote["changesPercentage"] > 0 and avg_sentiment > 0 else "HOLD"

    return {
        "quote": quote,
        "ratios": ratios,
        "insider_summary": {
            "purchases": len(insider_purchases),
            "sales": len(insider_sales),
            "net_value": round(net_insider, 2),
        },
        "news": news,
        "formatted_news": formatted_news,
        "avg_news_sentiment": avg_sentiment,
        "recommendation": recommendation,
    }