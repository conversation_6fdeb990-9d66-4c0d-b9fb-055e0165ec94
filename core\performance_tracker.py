#!/usr/bin/env python3
"""Performance Analytics System

Comprehensive trading performance tracking and analytics:
- Trade history & P&L tracking
- Success rate analytics
- Performance vs benchmarks
- Risk-adjusted returns
- Detailed reporting
"""
import json
import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import os
from pathlib import Path


class PerformanceTracker:
    """Comprehensive trading performance tracker."""
    
    def __init__(self, db_path: str = "data/trading_performance.db"):
        self.db_path = db_path
        self.ensure_data_directory()
        self.init_database()
        
    def ensure_data_directory(self):
        """Ensure data directory exists."""
        data_dir = Path(self.db_path).parent
        data_dir.mkdir(exist_ok=True)
    
    def init_database(self):
        """Initialize the performance tracking database."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Trades table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS trades (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    trade_id TEXT UNIQUE,
                    symbol TEXT NOT NULL,
                    strategy TEXT,
                    entry_date TEXT,
                    exit_date TEXT,
                    entry_price REAL,
                    exit_price REAL,
                    quantity INTEGER,
                    side TEXT,
                    pnl REAL,
                    pnl_percent REAL,
                    commission REAL,
                    hold_time_hours REAL,
                    ttm_grade TEXT,
                    setup_confidence REAL,
                    stop_loss REAL,
                    take_profit REAL,
                    exit_reason TEXT,
                    notes TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Daily performance table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS daily_performance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date TEXT UNIQUE,
                    total_pnl REAL,
                    total_trades INTEGER,
                    winning_trades INTEGER,
                    losing_trades INTEGER,
                    largest_win REAL,
                    largest_loss REAL,
                    account_balance REAL,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Strategy performance table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS strategy_performance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    strategy TEXT,
                    total_trades INTEGER,
                    winning_trades INTEGER,
                    total_pnl REAL,
                    avg_pnl REAL,
                    max_win REAL,
                    max_loss REAL,
                    avg_hold_time REAL,
                    sharpe_ratio REAL,
                    last_updated TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
    
    def add_trade(self, trade_data: Dict) -> str:
        """Add a completed trade to the database."""
        trade_id = trade_data.get('trade_id', f"trade_{int(datetime.now().timestamp())}")
        
        # Calculate P&L
        entry_price = trade_data['entry_price']
        exit_price = trade_data['exit_price']
        quantity = trade_data['quantity']
        side = trade_data.get('side', 'long')
        commission = trade_data.get('commission', 0)
        
        if side.lower() == 'long':
            pnl = (exit_price - entry_price) * quantity - commission
        else:  # short
            pnl = (entry_price - exit_price) * quantity - commission
        
        pnl_percent = (pnl / (entry_price * quantity)) * 100
        
        # Calculate hold time
        entry_date = datetime.fromisoformat(trade_data['entry_date'])
        exit_date = datetime.fromisoformat(trade_data['exit_date'])
        hold_time_hours = (exit_date - entry_date).total_seconds() / 3600
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO trades (
                    trade_id, symbol, strategy, entry_date, exit_date,
                    entry_price, exit_price, quantity, side, pnl, pnl_percent,
                    commission, hold_time_hours, ttm_grade, setup_confidence,
                    stop_loss, take_profit, exit_reason, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                trade_id, trade_data['symbol'], trade_data.get('strategy', 'TTM'),
                trade_data['entry_date'], trade_data['exit_date'],
                entry_price, exit_price, quantity, side, pnl, pnl_percent,
                commission, hold_time_hours, trade_data.get('ttm_grade'),
                trade_data.get('setup_confidence'), trade_data.get('stop_loss'),
                trade_data.get('take_profit'), trade_data.get('exit_reason'),
                trade_data.get('notes')
            ))
            conn.commit()
        
        # Update daily performance
        self.update_daily_performance(exit_date.date().isoformat())
        
        # Update strategy performance
        self.update_strategy_performance(trade_data.get('strategy', 'TTM'))
        
        return trade_id
    
    def update_daily_performance(self, date: str):
        """Update daily performance metrics."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Get trades for the date
            cursor.execute('''
                SELECT pnl FROM trades 
                WHERE DATE(exit_date) = ?
            ''', (date,))
            
            trades = cursor.fetchall()
            if not trades:
                return
            
            pnls = [trade[0] for trade in trades]
            total_pnl = sum(pnls)
            total_trades = len(pnls)
            winning_trades = len([p for p in pnls if p > 0])
            losing_trades = len([p for p in pnls if p < 0])
            largest_win = max(pnls) if pnls else 0
            largest_loss = min(pnls) if pnls else 0
            
            cursor.execute('''
                INSERT OR REPLACE INTO daily_performance (
                    date, total_pnl, total_trades, winning_trades, losing_trades,
                    largest_win, largest_loss
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (date, total_pnl, total_trades, winning_trades, losing_trades,
                  largest_win, largest_loss))
            
            conn.commit()
    
    def update_strategy_performance(self, strategy: str):
        """Update strategy performance metrics."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Get all trades for the strategy
            cursor.execute('''
                SELECT pnl, hold_time_hours FROM trades 
                WHERE strategy = ?
            ''', (strategy,))
            
            trades = cursor.fetchall()
            if not trades:
                return
            
            pnls = [trade[0] for trade in trades]
            hold_times = [trade[1] for trade in trades if trade[1] is not None]
            
            total_trades = len(pnls)
            winning_trades = len([p for p in pnls if p > 0])
            total_pnl = sum(pnls)
            avg_pnl = total_pnl / total_trades if total_trades > 0 else 0
            max_win = max(pnls) if pnls else 0
            max_loss = min(pnls) if pnls else 0
            avg_hold_time = sum(hold_times) / len(hold_times) if hold_times else 0
            
            # Calculate Sharpe ratio (simplified)
            if len(pnls) > 1:
                returns_std = np.std(pnls)
                sharpe_ratio = (avg_pnl / returns_std) if returns_std > 0 else 0
            else:
                sharpe_ratio = 0
            
            cursor.execute('''
                INSERT OR REPLACE INTO strategy_performance (
                    strategy, total_trades, winning_trades, total_pnl, avg_pnl,
                    max_win, max_loss, avg_hold_time, sharpe_ratio
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (strategy, total_trades, winning_trades, total_pnl, avg_pnl,
                  max_win, max_loss, avg_hold_time, sharpe_ratio))
            
            conn.commit()
    
    def get_performance_summary(self, days: int = 30) -> Dict:
        """Get comprehensive performance summary."""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Get trades in period
            cursor.execute('''
                SELECT * FROM trades 
                WHERE exit_date >= ? AND exit_date <= ?
                ORDER BY exit_date DESC
            ''', (start_date.isoformat(), end_date.isoformat()))
            
            trades = cursor.fetchall()
            
            if not trades:
                return {"error": "No trades found in the specified period"}
            
            # Calculate metrics
            pnls = [trade[9] for trade in trades]  # pnl column
            total_pnl = sum(pnls)
            total_trades = len(trades)
            winning_trades = len([p for p in pnls if p > 0])
            losing_trades = len([p for p in pnls if p < 0])
            win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0
            
            avg_win = np.mean([p for p in pnls if p > 0]) if winning_trades > 0 else 0
            avg_loss = np.mean([p for p in pnls if p < 0]) if losing_trades > 0 else 0
            profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else float('inf')
            
            largest_win = max(pnls) if pnls else 0
            largest_loss = min(pnls) if pnls else 0
            
            # Get strategy breakdown
            cursor.execute('''
                SELECT strategy, COUNT(*), SUM(pnl), AVG(pnl)
                FROM trades 
                WHERE exit_date >= ? AND exit_date <= ?
                GROUP BY strategy
            ''', (start_date.isoformat(), end_date.isoformat()))
            
            strategy_breakdown = cursor.fetchall()
            
            return {
                "period_days": days,
                "total_trades": total_trades,
                "winning_trades": winning_trades,
                "losing_trades": losing_trades,
                "win_rate": win_rate,
                "total_pnl": total_pnl,
                "avg_pnl_per_trade": total_pnl / total_trades if total_trades > 0 else 0,
                "largest_win": largest_win,
                "largest_loss": largest_loss,
                "avg_win": avg_win,
                "avg_loss": avg_loss,
                "profit_factor": profit_factor,
                "strategy_breakdown": [
                    {
                        "strategy": row[0],
                        "trades": row[1],
                        "total_pnl": row[2],
                        "avg_pnl": row[3]
                    } for row in strategy_breakdown
                ]
            }
    
    def get_ttm_grade_performance(self) -> Dict:
        """Analyze performance by TTM grade."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT ttm_grade, COUNT(*), AVG(pnl), SUM(CASE WHEN pnl > 0 THEN 1 ELSE 0 END)
                FROM trades 
                WHERE ttm_grade IS NOT NULL
                GROUP BY ttm_grade
                ORDER BY ttm_grade
            ''')
            
            results = cursor.fetchall()
            
            grade_performance = {}
            for row in results:
                grade, total_trades, avg_pnl, winning_trades = row
                win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0
                
                grade_performance[grade] = {
                    "total_trades": total_trades,
                    "avg_pnl": avg_pnl,
                    "winning_trades": winning_trades,
                    "win_rate": win_rate
                }
            
            return grade_performance


if __name__ == "__main__":
    # Test the performance tracker
    tracker = PerformanceTracker()
    
    print("🧪 Testing Performance Tracker")
    print("=" * 40)
    
    # Add a sample trade
    sample_trade = {
        "trade_id": "test_001",
        "symbol": "AAPL",
        "strategy": "TTM",
        "entry_date": "2024-01-15T09:30:00",
        "exit_date": "2024-01-15T15:30:00",
        "entry_price": 150.00,
        "exit_price": 153.00,
        "quantity": 100,
        "side": "long",
        "ttm_grade": "A",
        "setup_confidence": 85.0,
        "exit_reason": "Target reached"
    }
    
    trade_id = tracker.add_trade(sample_trade)
    print(f"✅ Added trade: {trade_id}")
    
    # Get performance summary
    summary = tracker.get_performance_summary(30)
    print(f"✅ Performance summary: {summary['total_trades']} trades")
    print(f"   Win rate: {summary['win_rate']:.1f}%")
    print(f"   Total P&L: ${summary['total_pnl']:.2f}")
    
    # Get TTM grade performance
    grade_perf = tracker.get_ttm_grade_performance()
    print(f"✅ TTM grade performance: {len(grade_perf)} grades tracked")
