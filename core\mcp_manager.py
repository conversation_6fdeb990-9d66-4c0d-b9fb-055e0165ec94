#!/usr/bin/env python3
"""
MCP Manager for TTM Trading System

This module manages the integration between the Alpaca MCP Server
and our existing TTM trading system components.
"""

import os
import threading
import time
from typing import Optional, Dict, Any

class MCPManager:
    """Manages MCP integration with the TTM trading system."""
    
    def __init__(self):
        self.mcp_integration = None
        self.is_initialized = False
        self.is_running = False
        self._monitor_thread = None
        
    def initialize(self) -> bool:
        """Initialize MCP integration."""
        try:
            from integrations.alpaca_mcp_integration import AlpacaMCPIntegration
            
            self.mcp_integration = AlpacaMCPIntegration()
            
            # Check if already setup
            if not self.mcp_integration.mcp_server_path:
                print("🔧 MCP: Setting up Alpaca MCP Server...")
                if not self.mcp_integration.setup_mcp_server():
                    print("❌ MCP: Setup failed")
                    return False
            
            # Configure credentials if available
            api_key = os.getenv('ALPACA_API_KEY')
            secret_key = os.getenv('ALPACA_SECRET_KEY')
            
            if api_key and secret_key:
                print("🔑 MCP: Configuring credentials...")
                self.mcp_integration.configure_credentials(api_key, secret_key, paper_trading=True)
            else:
                print("⚠️ MCP: No credentials found - set ALPACA_API_KEY and ALPACA_SECRET_KEY")
            
            self.is_initialized = True
            print("✅ MCP: Initialization complete")
            return True
            
        except ImportError:
            print("⚠️ MCP: Integration module not available")
            return False
        except Exception as e:
            print(f"❌ MCP: Initialization failed: {e}")
            return False
    
    def start(self) -> bool:
        """Start MCP server."""
        if not self.is_initialized:
            if not self.initialize():
                return False
        
        try:
            if self.mcp_integration.start_mcp_server():
                self.is_running = True
                self._start_monitoring()
                print("🚀 MCP: Server started and monitoring active")
                return True
            else:
                print("❌ MCP: Failed to start server")
                return False
                
        except Exception as e:
            print(f"❌ MCP: Start failed: {e}")
            return False
    
    def stop(self) -> bool:
        """Stop MCP server."""
        try:
            if self.mcp_integration:
                self.mcp_integration.stop_mcp_server()
            
            self.is_running = False
            self._stop_monitoring()
            print("⏹️ MCP: Server stopped")
            return True
            
        except Exception as e:
            print(f"❌ MCP: Stop failed: {e}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """Get MCP integration status."""
        return {
            'initialized': self.is_initialized,
            'running': self.is_running,
            'server_path': str(self.mcp_integration.mcp_server_path) if self.mcp_integration else None,
            'capabilities': self._get_capabilities() if self.is_running else []
        }
    
    def _get_capabilities(self) -> list:
        """Get list of MCP capabilities."""
        return [
            "Direct trade execution via AI chat",
            "Real-time position analysis",
            "Live market data access",
            "Advanced options trading",
            "Portfolio management",
            "Risk/reward calculations"
        ]
    
    def _start_monitoring(self):
        """Start monitoring thread."""
        if not self._monitor_thread or not self._monitor_thread.is_alive():
            self._monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self._monitor_thread.start()
    
    def _stop_monitoring(self):
        """Stop monitoring thread."""
        # Thread will stop when is_running becomes False
        pass
    
    def _monitor_loop(self):
        """Monitor MCP server health."""
        while self.is_running:
            try:
                # Check if MCP server is still running
                if self.mcp_integration and self.mcp_integration.process:
                    if self.mcp_integration.process.poll() is not None:
                        print("⚠️ MCP: Server process stopped unexpectedly")
                        self.is_running = False
                        break
                
                time.sleep(5)  # Check every 5 seconds
                
            except Exception as e:
                print(f"⚠️ MCP: Monitor error: {e}")
                time.sleep(10)
    
    def integrate_with_ttm_components(self):
        """Integrate MCP with existing TTM components."""
        try:
            # Register with automation system
            self._register_with_automation()
            
            # Register with dashboard
            self._register_with_dashboard()
            
            # Register with chat system
            self._register_with_chat()
            
            print("🔗 MCP: Integrated with TTM components")
            
        except Exception as e:
            print(f"⚠️ MCP: Component integration error: {e}")
    
    def _register_with_automation(self):
        """Register MCP capabilities with automation system."""
        try:
            from core.automation_control import get_automation_engine
            automation = get_automation_engine()
            
            # Add MCP status to automation callbacks
            automation.register_callback('trade_executed', self._on_automation_trade)
            print("🔗 MCP: Registered with automation system")
            
        except Exception as e:
            print(f"⚠️ MCP: Automation registration failed: {e}")
    
    def _register_with_dashboard(self):
        """Register MCP capabilities with dashboard."""
        try:
            from core.live_dashboard import get_dashboard
            dashboard = get_dashboard()
            
            # Add MCP status to dashboard
            print("🔗 MCP: Registered with dashboard")
            
        except Exception as e:
            print(f"⚠️ MCP: Dashboard registration failed: {e}")
    
    def _register_with_chat(self):
        """Register MCP capabilities with chat system."""
        try:
            # Chat system already has MCP integration hooks
            print("🔗 MCP: Chat system integration ready")
            
        except Exception as e:
            print(f"⚠️ MCP: Chat registration failed: {e}")
    
    def _on_automation_trade(self, trade_message):
        """Handle automation trade events for MCP integration."""
        try:
            # This could send trade notifications to MCP/Claude
            print(f"📊 MCP: Automation trade event: {trade_message}")
            
        except Exception as e:
            print(f"⚠️ MCP: Trade event handling error: {e}")

# Global MCP manager instance
_mcp_manager = None

def get_mcp_manager() -> MCPManager:
    """Get the global MCP manager instance."""
    global _mcp_manager
    if _mcp_manager is None:
        _mcp_manager = MCPManager()
    return _mcp_manager

def start_mcp_integration() -> bool:
    """Start MCP integration with TTM system."""
    manager = get_mcp_manager()
    
    if manager.start():
        manager.integrate_with_ttm_components()
        return True
    return False

def stop_mcp_integration() -> bool:
    """Stop MCP integration."""
    manager = get_mcp_manager()
    return manager.stop()

def get_mcp_status() -> Dict[str, Any]:
    """Get MCP integration status."""
    manager = get_mcp_manager()
    return manager.get_status()

def is_mcp_available() -> bool:
    """Check if MCP integration is available and running."""
    manager = get_mcp_manager()
    return manager.is_running

# Integration functions for existing components
def enhance_chat_with_mcp():
    """Enhance chat system with MCP capabilities."""
    if is_mcp_available():
        return {
            'mcp_available': True,
            'capabilities': [
                "Direct trade execution",
                "Real-time position analysis", 
                "Live market data",
                "Advanced options trading"
            ],
            'usage_examples': [
                "Buy 10 shares of AAPL",
                "What's my PLTR position risk/reward?",
                "Show current portfolio performance",
                "Place bull call spread on TSLA"
            ]
        }
    return {'mcp_available': False}

def enhance_automation_with_mcp():
    """Enhance automation system with MCP capabilities."""
    if is_mcp_available():
        return {
            'mcp_integration': True,
            'enhanced_features': [
                "AI chat can monitor automation",
                "Natural language trade commands",
                "Real-time position updates via chat",
                "Advanced strategy execution"
            ]
        }
    return {'mcp_integration': False}

def enhance_dashboard_with_mcp():
    """Enhance dashboard with MCP capabilities."""
    if is_mcp_available():
        return {
            'mcp_enhanced': True,
            'new_features': [
                "Chat-based position analysis",
                "Natural language market queries",
                "Direct trade execution from chat",
                "Real-time portfolio conversations"
            ]
        }
    return {'mcp_enhanced': False}
