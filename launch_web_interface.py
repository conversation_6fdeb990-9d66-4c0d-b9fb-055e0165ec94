#!/usr/bin/env python3
"""
Launch Ultimate TTM Trading System Web Interface
Professional web interface like Incite AI with chart upload and Deep Search
"""
import os
import sys
import subprocess
import webbrowser
from pathlib import Path
import time

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'streamlit',
        'plotly',
        'pandas',
        'numpy',
        'pillow'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   • {package}")
        
        print("\n📦 Install missing packages with:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_optional_dependencies():
    """Check optional dependencies for enhanced features"""
    optional_packages = {
        'openai': 'AI vision analysis for chart uploads',
        'sentence_transformers': 'Deep Search RAG system',
        'faiss-cpu': 'Vector search for Deep Search',
        'chromadb': 'Alternative vector database'
    }
    
    print("🔍 Checking optional dependencies:")
    
    for package, description in optional_packages.items():
        try:
            __import__(package.replace('-', '_'))
            print(f"   ✅ {package} - {description}")
        except ImportError:
            print(f"   ⚠️  {package} - {description} (optional)")
    
    print("\n💡 Install all optional features with:")
    print("pip install openai sentence-transformers faiss-cpu chromadb")

def setup_environment():
    """Setup environment for web interface"""
    
    # Create necessary directories
    directories = [
        'data',
        'logs',
        'uploads',
        'exports'
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    
    # Set Streamlit configuration
    streamlit_config = """
[server]
port = 8501
enableCORS = false
enableXsrfProtection = false

[browser]
gatherUsageStats = false

[theme]
primaryColor = "#2a5298"
backgroundColor = "#ffffff"
secondaryBackgroundColor = "#f8f9fa"
textColor = "#262730"
"""
    
    config_dir = Path.home() / '.streamlit'
    config_dir.mkdir(exist_ok=True)
    
    config_file = config_dir / 'config.toml'
    with open(config_file, 'w') as f:
        f.write(streamlit_config)

def launch_interface():
    """Launch the web interface"""
    
    print("🚀 Launching Ultimate TTM Trading System Web Interface...")
    print("=" * 60)
    
    # Check dependencies
    if not check_dependencies():
        print("\n❌ Cannot launch - missing required dependencies")
        return False
    
    check_optional_dependencies()
    
    # Setup environment
    setup_environment()
    
    # Get the web interface file path
    interface_file = Path(__file__).parent / 'web_interface.py'
    
    if not interface_file.exists():
        print(f"❌ Web interface file not found: {interface_file}")
        return False
    
    print(f"\n🌐 Starting web server...")
    print(f"📁 Interface file: {interface_file}")
    
    try:
        # Launch Streamlit
        cmd = [
            sys.executable, '-m', 'streamlit', 'run', 
            str(interface_file),
            '--server.port', '8501',
            '--server.address', 'localhost',
            '--browser.gatherUsageStats', 'false'
        ]
        
        print(f"🔧 Command: {' '.join(cmd)}")
        
        # Start the process
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait a moment for server to start
        time.sleep(3)
        
        # Check if process is still running
        if process.poll() is None:
            print("✅ Web server started successfully!")
            print("\n🌐 **ACCESS YOUR TRADING SYSTEM:**")
            print("   URL: http://localhost:8501")
            print("   📱 Mobile friendly interface")
            print("   🧠 AI Chat & Deep Search")
            print("   📊 TTM Scanner & Analysis")
            print("   📈 Chart Upload & Vision Analysis")
            print("   💼 Portfolio Management")
            
            # Try to open browser
            try:
                webbrowser.open('http://localhost:8501')
                print("   🌐 Browser opened automatically")
            except:
                print("   ⚠️  Please open http://localhost:8501 in your browser")
            
            print("\n🎯 **FEATURES AVAILABLE:**")
            print("   • Professional dashboard like Incite AI")
            print("   • Upload Think or Swim charts for AI analysis")
            print("   • Deep Search through all trading data")
            print("   • Real-time TTM scanner with A+ grading")
            print("   • AI chat with complete system awareness")
            print("   • Portfolio tracking and performance analytics")
            
            print("\n⚡ **AI COMMANDS TO TRY:**")
            print("   • 'What's happening in my system right now?'")
            print("   • 'Is buying AAPL a good idea right now?'")
            print("   • 'Show me the best TTM setups available'")
            print("   • Upload a chart and ask 'Analyze this setup'")
            
            print("\n🛑 Press Ctrl+C to stop the server")
            
            try:
                # Wait for process to complete
                process.wait()
            except KeyboardInterrupt:
                print("\n🛑 Shutting down web server...")
                process.terminate()
                process.wait()
                print("✅ Web server stopped")
            
            return True
        else:
            # Process failed to start
            stdout, stderr = process.communicate()
            print("❌ Failed to start web server")
            print(f"Error: {stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error launching web interface: {e}")
        return False

def show_help():
    """Show help information"""
    print("🚀 Ultimate TTM Trading System - Web Interface Launcher")
    print("=" * 60)
    print()
    print("📖 **DESCRIPTION:**")
    print("   Launch a professional web interface for your TTM trading system.")
    print("   Features include AI chat, chart analysis, Deep Search, and more.")
    print()
    print("🎯 **FEATURES:**")
    print("   • Professional dashboard like Incite AI")
    print("   • Upload Think or Swim charts for AI analysis")
    print("   • Deep Search RAG system for trading data")
    print("   • Real-time TTM scanner with confidence grading")
    print("   • AI chat with complete system awareness")
    print("   • Portfolio management and analytics")
    print()
    print("🔧 **REQUIREMENTS:**")
    print("   • Python 3.8+")
    print("   • streamlit, plotly, pandas, numpy, pillow")
    print("   • Optional: openai, sentence-transformers, faiss-cpu")
    print()
    print("🚀 **USAGE:**")
    print("   python launch_web_interface.py")
    print()
    print("🌐 **ACCESS:**")
    print("   http://localhost:8501")
    print()
    print("💡 **TIPS:**")
    print("   • Install optional packages for full AI features")
    print("   • Configure OpenAI API key for chart vision analysis")
    print("   • Use mobile browser for responsive interface")

def main():
    """Main launcher function"""
    
    if len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h', 'help']:
        show_help()
        return
    
    print("🚀 Ultimate TTM Trading System")
    print("🌐 Professional Web Interface Launcher")
    print("=" * 50)
    
    success = launch_interface()
    
    if success:
        print("\n🎉 **LAUNCH SUCCESSFUL!**")
        print("Your professional trading interface is now running!")
        print("Enjoy the most advanced TTM system ever built! 🏆")
    else:
        print("\n❌ **LAUNCH FAILED**")
        print("Please check the error messages above and try again.")
        print("\nFor help, run: python launch_web_interface.py --help")

if __name__ == "__main__":
    main()
