"""Alpaca Setup Helper

This script helps you set up your Alpaca API credentials for trading.
"""
import os
from pathlib import Path


def setup_alpaca_credentials():
    """Interactive setup for Alpaca credentials."""
    
    print("🚀 ALPACA TRADING SETUP")
    print("=" * 50)
    print()
    print("To enable real trading with Alpaca, you need API credentials.")
    print("Get them from: https://app.alpaca.markets/")
    print()
    
    # Check if already configured
    existing_key = os.getenv('ALPACA_API_KEY')
    existing_secret = os.getenv('ALPACA_SECRET_KEY')
    
    if existing_key and existing_secret:
        print("✅ Alpaca credentials already found in environment!")
        print(f"API Key: {existing_key[:8]}...")
        print(f"Secret Key: {existing_secret[:8]}...")
        
        use_existing = input("\nUse existing credentials? (y/n): ").lower().strip()
        if use_existing == 'y':
            return test_alpaca_connection()
    
    print("\n📝 ENTER YOUR ALPACA CREDENTIALS:")
    print("(You can find these in your Alpaca dashboard)")
    print()
    
    # Get API key
    api_key = input("Alpaca API Key: ").strip()
    if not api_key:
        print("❌ API Key is required")
        return False
    
    # Get secret key
    secret_key = input("Alpaca Secret Key: ").strip()
    if not secret_key:
        print("❌ Secret Key is required")
        return False
    
    # Choose setup method
    print("\n🔧 SETUP METHOD:")
    print("1. Environment variables (recommended)")
    print("2. Config file")
    
    method = input("Choose method (1 or 2): ").strip()
    
    if method == "1":
        setup_environment_variables(api_key, secret_key)
    elif method == "2":
        setup_config_file(api_key, secret_key)
    else:
        print("❌ Invalid choice")
        return False
    
    # Test connection
    return test_alpaca_connection()


def setup_environment_variables(api_key: str, secret_key: str):
    """Set up environment variables."""
    
    print("\n🔧 Setting up environment variables...")
    
    # Set for current session
    os.environ['ALPACA_API_KEY'] = api_key
    os.environ['ALPACA_SECRET_KEY'] = secret_key
    
    print("✅ Environment variables set for current session")
    print()
    print("💡 TO MAKE PERMANENT:")
    print("Add these lines to your system environment variables:")
    print(f"ALPACA_API_KEY={api_key}")
    print(f"ALPACA_SECRET_KEY={secret_key}")
    print()
    print("Or add to your .env file:")
    
    # Create .env file
    env_content = f"""# Alpaca Trading API Credentials
ALPACA_API_KEY={api_key}
ALPACA_SECRET_KEY={secret_key}
"""
    
    try:
        with open('.env', 'w') as f:
            f.write(env_content)
        print("✅ Created .env file with credentials")
    except Exception as e:
        print(f"⚠️ Could not create .env file: {e}")


def setup_config_file(api_key: str, secret_key: str):
    """Add credentials to config.py file."""
    
    print("\n🔧 Adding to config.py...")
    
    config_addition = f"""
# Alpaca Trading API Credentials
API_KEYS['ALPACA_API_KEY'] = '{api_key}'
API_KEYS['ALPACA_SECRET_KEY'] = '{secret_key}'
"""
    
    try:
        # Read existing config
        config_path = Path('config.py')
        if config_path.exists():
            with open(config_path, 'r') as f:
                content = f.read()
            
            # Add credentials if not already present
            if 'ALPACA_API_KEY' not in content:
                with open(config_path, 'a') as f:
                    f.write(config_addition)
                print("✅ Added Alpaca credentials to config.py")
            else:
                print("⚠️ Alpaca credentials already in config.py")
        else:
            print("❌ config.py not found")
            return False
            
    except Exception as e:
        print(f"❌ Error updating config.py: {e}")
        return False
    
    return True


def test_alpaca_connection():
    """Test the Alpaca connection."""
    
    print("\n🧪 TESTING ALPACA CONNECTION...")
    
    try:
        from alpaca_trading import AlpacaTrader
        
        # Test paper trading connection
        trader = AlpacaTrader(paper_trading=True)
        account = trader.get_account_info()
        
        if account:
            print("✅ Successfully connected to Alpaca!")
            print(f"Account: {account.get('account_number', 'N/A')}")
            print(f"Equity: ${float(account.get('equity', 0)):,.2f}")
            print(f"Buying Power: ${float(account.get('buying_power', 0)):,.2f}")
            print(f"Paper Trading: {trader.paper_trading}")
            
            print("\n🎯 READY FOR TRADING!")
            print("• Paper trading is enabled by default")
            print("• Use the GUI to execute TTM squeeze trades")
            print("• Trades will appear in your Alpaca dashboard")
            
            return True
        else:
            print("❌ Connection failed - check your credentials")
            return False
            
    except Exception as e:
        print(f"❌ Connection error: {e}")
        print("\n💡 TROUBLESHOOTING:")
        print("1. Verify your API keys are correct")
        print("2. Check your internet connection")
        print("3. Ensure your Alpaca account is active")
        print("4. Try regenerating your API keys")
        return False


def show_trading_instructions():
    """Show instructions for using the trading system."""
    
    print("\n🎯 HOW TO USE THE TRADING SYSTEM:")
    print("=" * 50)
    print()
    print("1. 🔍 FIND OPPORTUNITIES:")
    print("   • Open the GUI (py launch_gui.py)")
    print("   • Go to TTM Scanner tab")
    print("   • Click 'Run Scan' to find TTM squeeze setups")
    print()
    print("2. 📊 SELECT A TRADE:")
    print("   • Click on a trade in the results table")
    print("   • Adjust quantity and risk parameters")
    print("   • Review entry, stop, and target prices")
    print()
    print("3. 🧪 EXECUTE PAPER TRADE:")
    print("   • Click 'ALPACA PAPER' button")
    print("   • Trade executes in Alpaca paper trading")
    print("   • Check your Alpaca dashboard to see the order")
    print()
    print("4. 🚀 EXECUTE LIVE TRADE (when ready):")
    print("   • Click 'ALPACA LIVE' button")
    print("   • Confirm the warning dialog")
    print("   • Trade executes with real money")
    print()
    print("⚠️ IMPORTANT SAFETY NOTES:")
    print("• Always test with paper trading first")
    print("• Start with small position sizes")
    print("• Monitor your trades closely")
    print("• Use proper risk management")
    print("• Never risk more than you can afford to lose")


if __name__ == "__main__":
    success = setup_alpaca_credentials()
    
    if success:
        show_trading_instructions()
    else:
        print("\n❌ Setup failed. Please try again or check the documentation.")
        print("\n💡 MANUAL SETUP:")
        print("1. Set environment variables:")
        print("   ALPACA_API_KEY=your_key")
        print("   ALPACA_SECRET_KEY=your_secret")
        print("2. Or add to config.py:")
        print("   API_KEYS['ALPACA_API_KEY'] = 'your_key'")
        print("   API_KEYS['ALPACA_SECRET_KEY'] = 'your_secret'")
