"""Order Execution Engine – handles live and paper orders via Alpaca.

For safety, default mode is *paper* (paper trading). Real orders are only
submitted if the environment variable `UTE_LIVE_TRADING` is set to `true`.
"""
from __future__ import annotations

import os
from typing import Dict

from alpaca.trading.client import TradingClient
from alpaca.trading.requests import MarketOrderRequest
from alpaca.trading.enums import OrderSide, TimeInForce

from logger_util import info, warning
from config import get_api_key

# ---------------------------------------------------------------------------

ALPACA_BASE_URL_PAPER = "https://paper-api.alpaca.markets"
ALPACA_BASE_URL_LIVE = "https://api.alpaca.markets"


class OrderExecutionEngine:
    """Wrapper around the Alpaca API with added safety checks."""

    def __init__(self) -> None:
        key = get_api_key("ALPACA_API_KEY")
        secret = get_api_key("ALPACA_API_SECRET")
        self.live = os.getenv("UTE_LIVE_TRADING", "false").lower() == "true"
        self.api = TradingClient(key, secret, paper=not self.live)
        info("🚀 Order engine initialised", live=self.live)

    # ---------------------------------------------------------------------
    def submit_market_order(self, symbol: str, qty: int, side: str, tif: str = "day") -> Dict:
        """Submit a simple market order after running risk checks."""
        assert side in {"buy", "sell"}, "side must be 'buy' or 'sell'"

        # Basic pre-trade risk check: position limits etc. (placeholder)
        if qty <= 0:
            raise ValueError("Quantity must be positive")

        if not self.live:
            warning("Paper trade order submitted", symbol=symbol, qty=qty, side=side, tif=tif)

        side_enum = OrderSide.BUY if side == "buy" else OrderSide.SELL
        order_request = MarketOrderRequest(
            symbol=symbol,
            qty=qty,
            side=side_enum,
            time_in_force=TimeInForce.DAY,
        )
        order = self.api.submit_order(order_request)
        info("✅ Order submitted", id=order.id)
        return order.model_dump(mode="json") 