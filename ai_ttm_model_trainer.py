#!/usr/bin/env python3
"""
AI TTM Model Trainer - Phase 2
Trains XGBoost classifier to predict TTM setup success probability
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Try to import ML libraries with fallbacks
try:
    from sklearn.model_selection import train_test_split, cross_val_score
    from sklearn.preprocessing import StandardScaler
    from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False

try:
    import joblib
    JOBLIB_AVAILABLE = True
except ImportError:
    JOBLIB_AVAILABLE = False

class TTMModelTrainer:
    """Trains ML model to predict TTM setup success."""

    def __init__(self, dataset_path="ttm_training_dataset.csv"):
        self.dataset_path = dataset_path
        self.model = None
        self.scaler = None
        self.feature_importance = None

        # Check dependencies
        if not SKLEARN_AVAILABLE:
            raise ImportError("scikit-learn not available. Install with: pip install scikit-learn")
        if not XGBOOST_AVAILABLE:
            raise ImportError("XGBoost not available. Install with: pip install xgboost")
        if not JOBLIB_AVAILABLE:
            raise ImportError("joblib not available. Install with: pip install joblib")
        
    def load_and_prepare_data(self):
        """Load dataset and prepare for training."""
        print("📊 Loading training dataset...")
        
        # Load data
        df = pd.read_csv(self.dataset_path)
        print(f"✅ Loaded {len(df):,} samples")
        
        # Define features (exclude label, symbol, timestamp, future_return)
        feature_cols = [col for col in df.columns if col not in 
                       ['label', 'symbol', 'timestamp', 'future_return']]
        
        X = df[feature_cols]
        y = df['label']
        
        print(f"🎯 Features: {len(feature_cols)}")
        print(f"📈 WIN rate: {y.mean()*100:.1f}%")
        
        return X, y, feature_cols
    
    def train_model(self, X, y):
        """Train XGBoost classifier."""
        print("\n🚀 Training XGBoost model...")
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # Scale features
        self.scaler = StandardScaler()
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # Train XGBoost
        self.model = xgb.XGBClassifier(
            n_estimators=200,
            max_depth=6,
            learning_rate=0.1,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42,
            eval_metric='logloss'
        )
        
        self.model.fit(
            X_train_scaled, y_train,
            eval_set=[(X_test_scaled, y_test)],
            verbose=False
        )
        
        # Predictions
        y_pred = self.model.predict(X_test_scaled)
        y_pred_proba = self.model.predict_proba(X_test_scaled)[:, 1]
        
        # Evaluation
        print("\n📊 MODEL PERFORMANCE:")
        print("=" * 50)
        print(classification_report(y_test, y_pred))
        
        auc_score = roc_auc_score(y_test, y_pred_proba)
        print(f"🎯 AUC Score: {auc_score:.3f}")
        
        # Cross-validation
        cv_scores = cross_val_score(self.model, X_train_scaled, y_train, cv=5, scoring='roc_auc')
        print(f"🔄 CV AUC: {cv_scores.mean():.3f} (+/- {cv_scores.std() * 2:.3f})")
        
        # Feature importance
        self.feature_importance = pd.DataFrame({
            'feature': X.columns,
            'importance': self.model.feature_importances_
        }).sort_values('importance', ascending=False)
        
        print("\n🔥 TOP 10 MOST IMPORTANT FEATURES:")
        print(self.feature_importance.head(10).to_string(index=False))
        
        return X_test_scaled, y_test, y_pred_proba
    
    def save_model(self, model_path="ttm_ai_model.pkl", scaler_path="ttm_scaler.pkl"):
        """Save trained model and scaler."""
        joblib.dump(self.model, model_path)
        joblib.dump(self.scaler, scaler_path)
        
        # Save feature importance
        self.feature_importance.to_csv("ttm_feature_importance.csv", index=False)
        
        print(f"\n💾 Model saved to: {model_path}")
        print(f"💾 Scaler saved to: {scaler_path}")
        print(f"💾 Feature importance saved to: ttm_feature_importance.csv")
    
    def create_probability_thresholds(self, X_test, y_test, y_pred_proba):
        """Analyze probability thresholds for different confidence levels."""
        print("\n🎯 PROBABILITY THRESHOLD ANALYSIS:")
        print("=" * 60)
        
        thresholds = [0.5, 0.6, 0.7, 0.8, 0.9]
        
        for threshold in thresholds:
            predictions = (y_pred_proba >= threshold).astype(int)
            
            if predictions.sum() > 0:  # If any predictions above threshold
                precision = (predictions & y_test).sum() / predictions.sum()
                recall = (predictions & y_test).sum() / y_test.sum()
                count = predictions.sum()
                
                print(f"Threshold {threshold:.1f}: "
                      f"Precision {precision:.1%}, "
                      f"Recall {recall:.1%}, "
                      f"Count {count:,}")
            else:
                print(f"Threshold {threshold:.1f}: No predictions above threshold")
    
    def test_model_predictions(self):
        """Test model with sample predictions."""
        print("\n🧪 TESTING MODEL PREDICTIONS:")
        print("=" * 50)
        
        # Load a small sample for testing
        df = pd.read_csv(self.dataset_path).head(10)
        feature_cols = [col for col in df.columns if col not in 
                       ['label', 'symbol', 'timestamp', 'future_return']]
        
        X_sample = df[feature_cols]
        X_sample_scaled = self.scaler.transform(X_sample)
        
        predictions = self.model.predict_proba(X_sample_scaled)[:, 1]
        
        for i, (idx, row) in enumerate(df.iterrows()):
            prob = predictions[i]
            actual = row['label']
            symbol = row['symbol']
            
            confidence = "🔥 HIGH" if prob > 0.8 else "⭐ MEDIUM" if prob > 0.6 else "📊 LOW"
            result = "✅ WIN" if actual == 1 else "❌ LOSS"
            
            print(f"{symbol}: {prob:.1%} probability {confidence} | Actual: {result}")


def main():
    """Main training execution."""
    print("🚀 TTM AI MODEL TRAINER - PHASE 2")
    print("=" * 60)
    
    # Initialize trainer
    trainer = TTMModelTrainer()
    
    try:
        # Load and prepare data
        X, y, feature_cols = trainer.load_and_prepare_data()
        
        # Train model
        X_test, y_test, y_pred_proba = trainer.train_model(X, y)
        
        # Analyze thresholds
        trainer.create_probability_thresholds(X_test, y_test, y_pred_proba)
        
        # Save model
        trainer.save_model()
        
        # Test predictions
        trainer.test_model_predictions()
        
        print(f"\n🎉 MODEL TRAINING COMPLETE!")
        print(f"🚀 Ready for Phase 3 - Real-time Screener!")
        print(f"📁 Model files ready for deployment")
        
    except FileNotFoundError:
        print("❌ Dataset file not found!")
        print("🔧 Run ai_ttm_dataset_builder.py first to create the training data")
    except Exception as e:
        print(f"❌ Training failed: {e}")


if __name__ == "__main__":
    main()
