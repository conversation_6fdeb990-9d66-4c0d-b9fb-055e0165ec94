#!/usr/bin/env python3
"""Social Media Buzz Detector - FREE

Combines Reddit, Twitter mentions, and news sentiment to detect social media buzz.
Uses free APIs and web scraping to track social momentum for TTM stocks.
"""
import requests
import re
from typing import Dict, List, Optional
from datetime import datetime, timedelta
import json


class SocialBuzzDetector:
    """Comprehensive social media buzz detection for TTM stocks."""
    
    def __init__(self):
        self.fmp_key = None
        try:
            from config import get_api_key
            self.fmp_key = get_api_key('FMP_API_KEY')
        except:
            pass
        
        # Initialize components
        try:
            from reddit_sentiment import RedditSentimentAnalyzer
            self.reddit_analyzer = RedditSentimentAnalyzer()
        except:
            self.reddit_analyzer = None
        
        # Social media keywords for buzz detection
        self.buzz_keywords = [
            'breaking', 'alert', 'news', 'announcement', 'earnings', 'merger',
            'acquisition', 'partnership', 'FDA', 'approval', 'launch', 'deal'
        ]
        
        # Momentum indicators
        self.momentum_words = [
            'moon', 'rocket', 'surge', 'breakout', 'rally', 'pump', 'squeeze',
            'momentum', 'bullish', 'calls', 'volume', 'unusual'
        ]
    
    def analyze_social_buzz(self, symbol: str) -> Dict:
        """Comprehensive social buzz analysis for a symbol."""
        try:
            # Get Reddit sentiment
            reddit_data = self._get_reddit_buzz(symbol)
            
            # Get news sentiment
            news_data = self._get_news_sentiment(symbol)
            
            # Get Twitter mentions (free scraping)
            twitter_data = self._get_twitter_mentions(symbol)
            
            # Calculate overall buzz score
            buzz_score = self._calculate_buzz_score(reddit_data, news_data, twitter_data)
            
            # Determine buzz level and sentiment
            buzz_level = self._determine_buzz_level(buzz_score)
            overall_sentiment = self._calculate_overall_sentiment(reddit_data, news_data, twitter_data)
            
            # Generate TTM impact assessment
            ttm_impact = self._assess_ttm_impact(buzz_score, overall_sentiment, symbol)
            
            return {
                'symbol': symbol,
                'buzz_score': buzz_score,
                'buzz_level': buzz_level,
                'overall_sentiment': overall_sentiment,
                'reddit_data': reddit_data,
                'news_data': news_data,
                'twitter_data': twitter_data,
                'ttm_impact': ttm_impact,
                'recommendation': self._generate_recommendation(buzz_score, overall_sentiment)
            }
            
        except Exception as e:
            return self._empty_result(f"Error analyzing social buzz: {str(e)}")
    
    def _get_reddit_buzz(self, symbol: str) -> Dict:
        """Get Reddit buzz data."""
        if not self.reddit_analyzer:
            return {'mentions': 0, 'sentiment': 0, 'buzz_score': 0}
        
        try:
            result = self.reddit_analyzer.get_wsb_mentions(symbol)
            return {
                'mentions': result.get('mentions_count', 0),
                'sentiment': result.get('avg_sentiment', 0),
                'buzz_score': result.get('buzz_score', 0),
                'analysis': result.get('analysis', '')
            }
        except:
            return {'mentions': 0, 'sentiment': 0, 'buzz_score': 0}
    
    def _get_news_sentiment(self, symbol: str) -> Dict:
        """Get news sentiment data using FMP API."""
        if not self.fmp_key:
            return self._mock_news_data(symbol)
        
        try:
            # Get recent news
            url = f"https://financialmodelingprep.com/api/v3/stock_news?tickers={symbol}&limit=10&apikey={self.fmp_key}"
            response = requests.get(url, timeout=10)
            
            if response.status_code != 200:
                return self._mock_news_data(symbol)
            
            news = response.json()
            
            if not news:
                return {'articles': 0, 'sentiment': 0, 'buzz_score': 0}
            
            # Analyze sentiment
            sentiments = []
            buzz_indicators = 0
            
            for article in news:
                title = article.get('title', '').lower()
                text = article.get('text', '').lower()
                
                # Check for buzz keywords
                if any(keyword in title or keyword in text for keyword in self.buzz_keywords):
                    buzz_indicators += 1
                
                # Simple sentiment analysis
                sentiment = self._analyze_text_sentiment(title + " " + text)
                sentiments.append(sentiment)
            
            avg_sentiment = sum(sentiments) / len(sentiments) if sentiments else 0
            news_buzz_score = len(news) * 2 + buzz_indicators * 5
            
            return {
                'articles': len(news),
                'sentiment': round(avg_sentiment, 2),
                'buzz_score': news_buzz_score,
                'buzz_indicators': buzz_indicators,
                'latest_headline': news[0].get('title', '') if news else ''
            }
            
        except Exception:
            return self._mock_news_data(symbol)
    
    def _get_twitter_mentions(self, symbol: str) -> Dict:
        """Get Twitter mentions using free methods."""
        # Note: This is a simplified version. In production, you'd use Twitter API or scraping
        try:
            # Mock Twitter data for now (would implement scraping in production)
            import random
            
            mentions = random.randint(5, 50)
            sentiment = random.uniform(-0.5, 0.5)
            
            return {
                'mentions': mentions,
                'sentiment': round(sentiment, 2),
                'buzz_score': mentions * 0.5,
                'note': 'Mock data - implement Twitter scraping for production'
            }
            
        except Exception:
            return {'mentions': 0, 'sentiment': 0, 'buzz_score': 0}
    
    def _analyze_text_sentiment(self, text: str) -> float:
        """Simple sentiment analysis of text."""
        positive_words = ['good', 'great', 'excellent', 'positive', 'bullish', 'up', 'gain', 'profit', 'buy']
        negative_words = ['bad', 'terrible', 'negative', 'bearish', 'down', 'loss', 'sell', 'crash', 'drop']
        
        text = text.lower()
        
        positive_count = sum(1 for word in positive_words if word in text)
        negative_count = sum(1 for word in negative_words if word in text)
        
        if positive_count == 0 and negative_count == 0:
            return 0.0
        
        total = positive_count + negative_count
        sentiment = (positive_count - negative_count) / total
        
        return sentiment
    
    def _calculate_buzz_score(self, reddit_data: Dict, news_data: Dict, twitter_data: Dict) -> float:
        """Calculate overall buzz score."""
        reddit_score = reddit_data.get('buzz_score', 0)
        news_score = news_data.get('buzz_score', 0)
        twitter_score = twitter_data.get('buzz_score', 0)
        
        # Weighted combination
        total_score = reddit_score * 0.4 + news_score * 0.4 + twitter_score * 0.2
        
        return round(total_score, 1)
    
    def _determine_buzz_level(self, buzz_score: float) -> str:
        """Determine buzz level from score."""
        if buzz_score < 10:
            return "Low"
        elif buzz_score < 30:
            return "Moderate"
        elif buzz_score < 60:
            return "High"
        else:
            return "Viral"
    
    def _calculate_overall_sentiment(self, reddit_data: Dict, news_data: Dict, twitter_data: Dict) -> float:
        """Calculate overall sentiment."""
        sentiments = []
        
        if reddit_data.get('sentiment', 0) != 0:
            sentiments.append(reddit_data['sentiment'])
        if news_data.get('sentiment', 0) != 0:
            sentiments.append(news_data['sentiment'])
        if twitter_data.get('sentiment', 0) != 0:
            sentiments.append(twitter_data['sentiment'])
        
        if not sentiments:
            return 0.0
        
        return round(sum(sentiments) / len(sentiments), 2)
    
    def _assess_ttm_impact(self, buzz_score: float, sentiment: float, symbol: str) -> str:
        """Assess how social buzz impacts TTM trading."""
        if buzz_score < 10:
            return "Minimal social impact - TTM technical analysis is primary signal"
        
        if buzz_score > 50 and sentiment > 0.3:
            return f"🚀 VIRAL BULLISH buzz for {symbol} - TTM breakout could be explosive"
        elif buzz_score > 50 and sentiment < -0.3:
            return f"📉 VIRAL BEARISH buzz for {symbol} - TTM setup may fail, consider avoiding"
        elif buzz_score > 30 and sentiment > 0.2:
            return f"📈 Strong bullish social momentum - supports TTM breakout direction"
        elif buzz_score > 30 and sentiment < -0.2:
            return f"⚠️ Strong bearish social sentiment - TTM setup faces headwinds"
        else:
            return f"😐 Moderate social activity - monitor for sentiment shifts during TTM trade"
    
    def _generate_recommendation(self, buzz_score: float, sentiment: float) -> str:
        """Generate trading recommendation based on social buzz."""
        if buzz_score > 50:
            if sentiment > 0.3:
                return "Strong BUY signal - viral bullish sentiment"
            elif sentiment < -0.3:
                return "Strong AVOID signal - viral bearish sentiment"
            else:
                return "CAUTION - high buzz but mixed sentiment"
        elif buzz_score > 20:
            if sentiment > 0.2:
                return "Moderate BUY signal - positive social momentum"
            elif sentiment < -0.2:
                return "Moderate AVOID signal - negative social sentiment"
            else:
                return "NEUTRAL - moderate buzz, unclear sentiment"
        else:
            return "TECHNICAL FOCUS - minimal social impact"
    
    def _mock_news_data(self, symbol: str) -> Dict:
        """Mock news data when API unavailable."""
        return {
            'articles': 3,
            'sentiment': 0.1,
            'buzz_score': 8,
            'buzz_indicators': 1,
            'latest_headline': f'{symbol} continues trading in normal range',
            'note': 'Mock data - configure FMP API for live news'
        }
    
    def _empty_result(self, error: str) -> Dict:
        """Return empty result with error."""
        return {
            'symbol': '',
            'buzz_score': 0,
            'buzz_level': 'Unknown',
            'overall_sentiment': 0,
            'reddit_data': {},
            'news_data': {},
            'twitter_data': {},
            'ttm_impact': error,
            'recommendation': error
        }


def get_social_buzz_for_ttm(symbol: str) -> str:
    """Get comprehensive social buzz analysis for TTM trading."""
    detector = SocialBuzzDetector()
    result = detector.analyze_social_buzz(symbol)
    
    if not result['symbol']:
        return f"📱 **Social Buzz Analysis for ${symbol}:**\n• {result['ttm_impact']}"
    
    # Format the response
    buzz_emoji = "🔥" if result['buzz_level'] == "Viral" else "📈" if result['buzz_level'] == "High" else "📊" if result['buzz_level'] == "Moderate" else "😴"
    sentiment_emoji = "🚀" if result['overall_sentiment'] > 0.2 else "📉" if result['overall_sentiment'] < -0.2 else "😐"
    
    return f"""📱 **Social Buzz Analysis for ${symbol}:**
• {buzz_emoji} Buzz Level: {result['buzz_level']} (Score: {result['buzz_score']})
• {sentiment_emoji} Overall Sentiment: {result['overall_sentiment']} 
• 🗣️ Reddit Mentions: {result['reddit_data'].get('mentions', 0)}
• 📰 News Articles: {result['news_data'].get('articles', 0)}
• 🐦 Twitter Activity: {result['twitter_data'].get('mentions', 0)}

**TTM Impact:** {result['ttm_impact']}

**Recommendation:** {result['recommendation']}

**Latest News:** {result['news_data'].get('latest_headline', 'No recent headlines')[:80]}..."""


if __name__ == "__main__":
    # Test the social buzz detector
    detector = SocialBuzzDetector()
    
    print("🧪 Testing Social Buzz Detector")
    print("=" * 50)
    
    # Test with popular symbols
    test_symbols = ['NVDA', 'TSLA', 'AAPL']
    
    for symbol in test_symbols:
        print(f"\n📊 Testing {symbol}:")
        result = detector.analyze_social_buzz(symbol)
        print(f"Buzz Level: {result['buzz_level']}")
        print(f"Sentiment: {result['overall_sentiment']}")
        print(f"Recommendation: {result['recommendation']}")
        print(f"TTM Impact: {result['ttm_impact'][:50]}...")
