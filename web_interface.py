#!/usr/bin/env python3
"""
Ultimate TTM Trading System - Professional Web Interface
Like Incite AI but better - with chart upload and Deep Search RAG
"""
import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import base64
import io
from PIL import Image
import sys
import os

# Add core directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.join(current_dir, 'core'))

# Configure Streamlit page
st.set_page_config(
    page_title="Ultimate TTM Trading System",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for professional styling
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .metric-card {
        background: white;
        padding: 1rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border-left: 4px solid #2a5298;
    }
    
    .ai-response {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 10px;
        border-left: 4px solid #28a745;
        margin: 1rem 0;
    }
    
    .chart-upload {
        background: #fff3cd;
        padding: 1rem;
        border-radius: 10px;
        border: 2px dashed #ffc107;
        text-align: center;
    }
    
    .stButton > button {
        background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
        color: white;
        border: none;
        border-radius: 5px;
        padding: 0.5rem 1rem;
        font-weight: bold;
    }
    
    .sidebar .sidebar-content {
        background: #f8f9fa;
    }
</style>
""", unsafe_allow_html=True)

def main():
    """Main application interface"""
    
    # Header
    st.markdown("""
    <div class="main-header">
        <h1>🚀 Ultimate TTM Trading System</h1>
        <p>Professional AI-Powered Trading Platform with Deep Search & Chart Analysis</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Sidebar Navigation
    with st.sidebar:
        st.image("https://via.placeholder.com/200x100/2a5298/ffffff?text=TTM+AI", width=200)
        
        page = st.selectbox(
            "🧭 Navigation",
            [
                "🏠 Dashboard",
                "🧠 AI Chat & Deep Search", 
                "📊 TTM Scanner",
                "📈 Chart Analysis",
                "💼 Portfolio",
                "⚙️ Settings"
            ]
        )
        
        st.markdown("---")
        
        # Quick Stats
        st.markdown("### 📊 Quick Stats")
        col1, col2 = st.columns(2)
        with col1:
            st.metric("Daily P&L", "$245", "+12.3%")
        with col2:
            st.metric("Win Rate", "73%", "+5%")
        
        st.metric("Active Positions", "3", "")
        st.metric("Confidence Score", "87.5", "+2.1")
        
        st.markdown("---")
        
        # System Status
        st.markdown("### 🔋 System Status")
        st.success("🟢 AI Brain: Online")
        st.success("🟢 Scanner: Active")
        st.success("🟢 Risk Mgmt: Active")
        st.info("🔵 Auto-Trading: Standby")
    
    # Main Content Area
    if page == "🏠 Dashboard":
        show_dashboard()
    elif page == "🧠 AI Chat & Deep Search":
        show_ai_chat()
    elif page == "📊 TTM Scanner":
        show_ttm_scanner()
    elif page == "📈 Chart Analysis":
        show_chart_analysis()
    elif page == "💼 Portfolio":
        show_portfolio()
    elif page == "⚙️ Settings":
        show_settings()

def show_dashboard():
    """Main dashboard view"""
    
    # Top Metrics Row
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.markdown("""
        <div class="metric-card">
            <h3>💰 Daily P&L</h3>
            <h2 style="color: #28a745;">+$245.67</h2>
            <p>+12.3% today</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        st.markdown("""
        <div class="metric-card">
            <h3>🎯 Win Rate</h3>
            <h2 style="color: #2a5298;">73.2%</h2>
            <p>Last 30 trades</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col3:
        st.markdown("""
        <div class="metric-card">
            <h3>📊 Confidence</h3>
            <h2 style="color: #ffc107;">87.5</h2>
            <p>Current market</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col4:
        st.markdown("""
        <div class="metric-card">
            <h3>🔥 A+ Setups</h3>
            <h2 style="color: #dc3545;">5</h2>
            <p>Available now</p>
        </div>
        """, unsafe_allow_html=True)
    
    st.markdown("<br>", unsafe_allow_html=True)
    
    # Main Content Columns
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.markdown("### 📈 Performance Chart")
        
        # Sample performance data
        dates = pd.date_range(start='2024-01-01', end='2024-01-15', freq='D')
        performance = [100, 102, 98, 105, 108, 103, 110, 115, 112, 118, 125, 120, 128, 135, 132]
        
        fig = go.Figure()
        fig.add_trace(go.Scatter(
            x=dates,
            y=performance,
            mode='lines+markers',
            name='Portfolio Value',
            line=dict(color='#28a745', width=3),
            marker=dict(size=6)
        ))
        
        fig.update_layout(
            title="Portfolio Performance (Last 15 Days)",
            xaxis_title="Date",
            yaxis_title="Portfolio Value (%)",
            template="plotly_white",
            height=400
        )
        
        st.plotly_chart(fig, use_container_width=True)
        
        # Recent Trades
        st.markdown("### 📋 Recent Trades")
        trades_data = {
            'Symbol': ['AAPL', 'TSLA', 'NVDA', 'MSFT', 'GOOGL'],
            'Action': ['BUY', 'SELL', 'BUY', 'BUY', 'SELL'],
            'Grade': ['A+', 'B', 'A', 'B+', 'A-'],
            'P&L': ['+$125', '+$67', '+$89', '-$23', '+$156'],
            'Confidence': [92, 78, 88, 71, 85],
            'Time': ['09:45', '10:23', '11:15', '13:30', '14:45']
        }
        
        df = pd.DataFrame(trades_data)
        st.dataframe(df, use_container_width=True)
    
    with col2:
        st.markdown("### 🎯 Top TTM Setups")
        
        # Top setups
        setups = [
            {"symbol": "AAPL", "grade": "A+", "confidence": 92, "reason": "13-bar squeeze firing"},
            {"symbol": "NVDA", "grade": "A", "confidence": 88, "reason": "Volume surge + momentum"},
            {"symbol": "TSLA", "grade": "A-", "confidence": 85, "reason": "Breakout confirmation"},
            {"symbol": "MSFT", "grade": "B+", "confidence": 78, "reason": "Building pressure"},
            {"symbol": "GOOGL", "grade": "B", "confidence": 72, "reason": "Early squeeze signal"}
        ]
        
        for setup in setups:
            grade_color = {
                "A+": "#28a745", "A": "#20c997", "A-": "#17a2b8",
                "B+": "#ffc107", "B": "#fd7e14"
            }.get(setup["grade"], "#6c757d")
            
            st.markdown(f"""
            <div style="background: white; padding: 0.8rem; margin: 0.5rem 0; border-radius: 8px; 
                        border-left: 4px solid {grade_color}; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div>
                        <strong style="font-size: 1.1em;">{setup["symbol"]}</strong>
                        <span style="background: {grade_color}; color: white; padding: 2px 6px; 
                              border-radius: 4px; font-size: 0.8em; margin-left: 8px;">{setup["grade"]}</span>
                    </div>
                    <div style="text-align: right;">
                        <div style="font-weight: bold; color: {grade_color};">{setup["confidence"]}</div>
                        <div style="font-size: 0.8em; color: #6c757d;">confidence</div>
                    </div>
                </div>
                <div style="margin-top: 0.5rem; font-size: 0.9em; color: #495057;">
                    {setup["reason"]}
                </div>
            </div>
            """, unsafe_allow_html=True)
        
        st.markdown("### 🧠 AI Insights")
        st.markdown("""
        <div class="ai-response">
            <strong>🤖 AI Analysis:</strong><br>
            Market showing strong momentum with 5 A-grade setups available. 
            VIX at 18.5 suggests moderate volatility environment favorable for TTM strategies. 
            Recommend focusing on AAPL and NVDA for highest probability trades.
        </div>
        """, unsafe_allow_html=True)

def show_ai_chat():
    """AI Chat and Deep Search interface"""
    
    st.markdown("### 🧠 AI Chat & Deep Search")
    st.markdown("Ask your AI anything about your trading system, positions, or market analysis.")
    
    # Chat interface
    col1, col2 = st.columns([3, 1])
    
    with col1:
        # Chat input
        user_input = st.text_input(
            "💬 Ask your AI:",
            placeholder="What's happening in my system right now?",
            key="chat_input"
        )
        
        # Quick question buttons
        st.markdown("**Quick Questions:**")
        col_a, col_b, col_c = st.columns(3)
        
        with col_a:
            if st.button("📊 System Status"):
                user_input = "What's happening in my system right now?"
        
        with col_b:
            if st.button("🎯 Best Trades"):
                user_input = "What are the best trades available right now?"
        
        with col_c:
            if st.button("💰 Performance"):
                user_input = "How's my performance today?"
        
        # Process AI response
        if user_input:
            with st.spinner("🧠 AI thinking..."):
                ai_response = get_ai_response(user_input)
                
                st.markdown(f"""
                <div class="ai-response">
                    <strong>🤖 AI Response:</strong><br>
                    {ai_response}
                </div>
                """, unsafe_allow_html=True)
    
    with col2:
        st.markdown("### 🔍 Deep Search")
        st.markdown("Search through all your trading data, decisions, and analysis.")
        
        search_query = st.text_input("🔍 Search:", placeholder="NVDA trades last week")
        
        if search_query:
            search_results = perform_deep_search(search_query)
            st.markdown("**Search Results:**")
            for result in search_results:
                st.markdown(f"• {result}")

def show_chart_analysis():
    """Chart analysis with upload functionality"""
    
    st.markdown("### 📈 Chart Analysis & Upload")
    st.markdown("Upload Think or Swim charts for AI analysis, or analyze current market data.")
    
    # Chart upload section
    st.markdown("""
    <div class="chart-upload">
        <h4>📤 Upload Think or Swim Chart</h4>
        <p>Upload a screenshot or chart image for AI analysis</p>
    </div>
    """, unsafe_allow_html=True)
    
    uploaded_file = st.file_uploader(
        "Choose a chart image...",
        type=['png', 'jpg', 'jpeg'],
        help="Upload a Think or Swim chart screenshot for AI analysis"
    )
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        if uploaded_file is not None:
            # Display uploaded image
            image = Image.open(uploaded_file)
            st.image(image, caption="Uploaded Chart", use_column_width=True)
            
            # Analyze button
            if st.button("🧠 Analyze Chart with AI"):
                with st.spinner("🔍 AI analyzing chart..."):
                    analysis = analyze_chart_image(image)
                    
                    st.markdown(f"""
                    <div class="ai-response">
                        <strong>🤖 Chart Analysis:</strong><br>
                        {analysis}
                    </div>
                    """, unsafe_allow_html=True)
        else:
            # Live chart example
            st.markdown("### 📊 Live Market Analysis")
            
            # Sample chart
            symbol = st.selectbox("Select Symbol:", ["AAPL", "TSLA", "NVDA", "MSFT", "GOOGL"])
            
            # Generate sample chart data
            dates = pd.date_range(start='2024-01-01', periods=50, freq='D')
            prices = pd.Series([150 + i + (i % 7) * 2 for i in range(50)], index=dates)
            
            fig = go.Figure(data=go.Candlestick(
                x=dates,
                open=prices,
                high=prices * 1.02,
                low=prices * 0.98,
                close=prices * 1.01
            ))
            
            fig.update_layout(
                title=f"{symbol} - Live Chart Analysis",
                template="plotly_white",
                height=400
            )
            
            st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        st.markdown("### 🎯 Analysis Tools")
        
        # Analysis options
        analysis_type = st.selectbox(
            "Analysis Type:",
            ["TTM Squeeze", "Support/Resistance", "Pattern Recognition", "Volume Analysis"]
        )
        
        timeframe = st.selectbox(
            "Timeframe:",
            ["1min", "5min", "15min", "1hour", "4hour", "1day"]
        )
        
        if st.button("🔍 Analyze"):
            st.success(f"Analyzing {analysis_type} on {timeframe} timeframe...")
        
        st.markdown("### 📋 Recent Analyses")
        analyses = [
            "AAPL - TTM Squeeze A+ (5min)",
            "TSLA - Support Break B (15min)", 
            "NVDA - Volume Surge A- (1hour)",
            "MSFT - Pattern Forming B+ (4hour)"
        ]
        
        for analysis in analyses:
            st.markdown(f"• {analysis}")

def show_ttm_scanner():
    """TTM Scanner interface"""
    
    st.markdown("### 📊 TTM Squeeze Scanner")
    
    # Scanner controls
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        min_confidence = st.slider("Min Confidence", 0, 100, 70)
    
    with col2:
        min_grade = st.selectbox("Min Grade", ["A+", "A", "A-", "B+", "B", "C+", "C"])
    
    with col3:
        timeframe = st.selectbox("Timeframe", ["5min", "15min", "1hour", "4hour"])
    
    with col4:
        if st.button("🔍 Scan Now"):
            st.success("Scanning for TTM opportunities...")
    
    # Scanner results
    st.markdown("### 🎯 Scanner Results")
    
    # Sample results
    results_data = {
        'Symbol': ['AAPL', 'NVDA', 'TSLA', 'MSFT', 'GOOGL', 'AMD', 'META', 'NFLX'],
        'Grade': ['A+', 'A', 'A-', 'B+', 'B+', 'B', 'B-', 'C+'],
        'Confidence': [92, 88, 85, 78, 76, 72, 68, 65],
        'Price': [150.25, 875.30, 248.75, 378.90, 2750.50, 142.80, 485.20, 425.75],
        'Volume': ['2.1x', '1.8x', '1.5x', '1.3x', '1.2x', '1.4x', '1.1x', '1.0x'],
        'Squeeze Bars': [13, 11, 9, 15, 8, 12, 7, 6],
        'Entry': [150.50, 876.00, 249.00, 379.25, 2752.00, 143.00, 485.50, 426.00],
        'Target': [158.00, 920.00, 262.00, 395.00, 2890.00, 152.00, 510.00, 445.00],
        'Stop': [145.00, 850.00, 238.00, 365.00, 2650.00, 137.00, 470.00, 410.00]
    }
    
    df = pd.DataFrame(results_data)
    
    # Color code by grade
    def color_grade(val):
        colors = {
            'A+': 'background-color: #d4edda; color: #155724',
            'A': 'background-color: #d1ecf1; color: #0c5460',
            'A-': 'background-color: #d1ecf1; color: #0c5460',
            'B+': 'background-color: #fff3cd; color: #856404',
            'B': 'background-color: #fff3cd; color: #856404',
            'B-': 'background-color: #fff3cd; color: #856404',
            'C+': 'background-color: #f8d7da; color: #721c24'
        }
        return colors.get(val, '')
    
    styled_df = df.style.applymap(color_grade, subset=['Grade'])
    st.dataframe(styled_df, use_container_width=True)

def show_portfolio():
    """Portfolio management interface"""
    
    st.markdown("### 💼 Portfolio Management")
    
    # Portfolio summary
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("Total Value", "$125,450", "+$2,340")
    
    with col2:
        st.metric("Day P&L", "+$245", "+0.20%")
    
    with col3:
        st.metric("Buying Power", "$45,230", "")
    
    # Positions table
    st.markdown("### 📊 Current Positions")
    
    positions_data = {
        'Symbol': ['AAPL', 'TSLA', 'NVDA'],
        'Quantity': [100, 50, 25],
        'Avg Cost': [148.50, 245.20, 870.30],
        'Current Price': [150.25, 248.75, 875.30],
        'Market Value': [15025, 12437, 21882],
        'Unrealized P&L': [175, 177, 125],
        'Day P&L': [25, -10, 15],
        'Stop Loss': [145.00, 238.00, 850.00],
        'Target': [158.00, 262.00, 920.00]
    }
    
    df = pd.DataFrame(positions_data)
    st.dataframe(df, use_container_width=True)

def show_settings():
    """Settings and configuration"""
    
    st.markdown("### ⚙️ System Settings")
    
    # Trading settings
    st.markdown("#### 🎯 Trading Settings")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.slider("Max Risk Per Trade (%)", 0.5, 5.0, 2.0, 0.1)
        st.slider("Max Portfolio Risk (%)", 5.0, 20.0, 10.0, 0.5)
        st.selectbox("Position Sizing Method", ["Kelly Criterion", "Fixed %", "Volatility Based"])
    
    with col2:
        st.slider("Min Confidence Score", 50, 95, 70, 1)
        st.selectbox("Auto Trading", ["Disabled", "Conservative", "Balanced", "Aggressive"])
        st.slider("Stop Loss %", 1.0, 10.0, 3.0, 0.1)
    
    # AI settings
    st.markdown("#### 🧠 AI Settings")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.selectbox("AI Response Style", ["Professional", "Casual", "Technical"])
        st.slider("Memory Retention (days)", 7, 90, 30, 1)
    
    with col2:
        st.selectbox("Explanation Detail", ["Brief", "Detailed", "Expert"])
        st.checkbox("Enable Learning", value=True)
    
    if st.button("💾 Save Settings"):
        st.success("Settings saved successfully!")

def get_ai_response(query):
    """Get AI response for user query"""
    
    # Simulate AI response based on query
    responses = {
        "What's happening in my system right now?": """
        📊 **Current System Status:**
        
        **Active Positions (3):**
        • AAPL: +$25.00 profit (Entry: $150.00, Current: $150.25)
        • TSLA: -$10.00 loss (Entry: $248.75, Current: $248.65)
        • NVDA: +$15.00 profit (Entry: $875.30, Current: $875.45)
        
        **Daily P&L:** +$245.67 (+0.20%)
        **Automation:** Running in Conservative mode
        **Last Scan:** Found 5 A-grade setups (3 minutes ago)
        **Risk Exposure:** 12% of account
        """,
        
        "What are the best trades available right now?": """
        🎯 **Top Trading Opportunities:**
        
        **1. AAPL - Grade A+ (Confidence: 92)**
        • 13-bar TTM squeeze firing with bullish momentum
        • Entry: $150.50, Target: $158.00, Stop: $145.00
        • Volume: 2.1x average, strong institutional flow
        
        **2. NVDA - Grade A (Confidence: 88)**
        • Volume surge + momentum confirmation
        • Entry: $876.00, Target: $920.00, Stop: $850.00
        • Semiconductor strength, AI narrative support
        
        **3. TSLA - Grade A- (Confidence: 85)**
        • Breakout confirmation above resistance
        • Entry: $249.00, Target: $262.00, Stop: $238.00
        • EV sector rotation, technical setup strong
        """,
        
        "How's my performance today?": """
        📈 **Today's Performance Summary:**
        
        **Overall P&L:** +$245.67 (+0.20%)
        **Trades Executed:** 5 (4 winners, 1 loser)
        **Win Rate:** 80% (above 30-day average of 73%)
        **Best Performer:** NVDA (+$180 on swing trade)
        **Risk Management:** All stops honored, max risk 1.8%
        
        **AI Assessment:** Strong performance day with disciplined execution. 
        Risk-adjusted returns excellent. System confidence high for continued trading.
        """
    }
    
    return responses.get(query, f"🤖 I understand you're asking about: '{query}'. Let me analyze this for you...")

def perform_deep_search(query):
    """Perform deep search through trading data"""
    
    # Simulate search results
    results = [
        f"NVDA trade on Jan 10: Entry $870, Exit $885 (+$375 profit)",
        f"TTM squeeze analysis for NVDA: Grade A, 11-bar setup",
        f"Risk assessment: NVDA position sized at 1.5% account risk",
        f"AI decision log: NVDA selected due to semiconductor strength"
    ]
    
    return results

def analyze_chart_image(image):
    """Analyze uploaded chart image with AI"""
    
    # Simulate chart analysis
    analysis = """
    🔍 **Chart Analysis Results:**
    
    **Pattern Detected:** TTM Squeeze formation
    **Timeframe:** Appears to be 15-minute chart
    **Setup Quality:** Grade A- (Confidence: 83%)
    
    **Technical Observations:**
    • Bollinger Bands contracting inside Keltner Channels
    • 9 consecutive squeeze bars identified
    • Volume declining during compression phase
    • Momentum oscillator showing slight bullish bias
    
    **Trade Recommendation:**
    • Wait for squeeze release confirmation
    • Entry above current high with volume
    • Target: +5-7% move expected
    • Stop: Below recent swing low
    
    **Risk Assessment:** Medium risk, suitable for standard position sizing
    """
    
    return analysis

if __name__ == "__main__":
    main()
