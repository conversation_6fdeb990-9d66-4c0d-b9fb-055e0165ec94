#!/usr/bin/env python3
"""Profit Target Planner - Mock Implementation

Simple profit target planning functionality for GUI compatibility.
This is a lightweight version to support the existing GUI.
"""
import random
from typing import Dict, Any


class ProfitTargetPlanner:
    """Simple profit target planner for GUI compatibility."""
    
    def __init__(self):
        self.symbols = ['AAPL', 'NVDA', 'TSLA', 'AMD', 'MSFT', 'AMZN']
    
    def plan(self, target_profit_dollars: float = 50) -> Dict[str, Any]:
        """Generate a simple trading plan."""
        symbol = random.choice(self.symbols)
        entry_price = random.uniform(50, 200)
        
        # Calculate position size for target profit
        profit_per_share = target_profit_dollars / 100  # Assume $0.50 profit per share
        quantity = max(1, int(target_profit_dollars / profit_per_share))
        
        # Calculate prices
        stop_loss = entry_price * 0.97  # 3% stop
        take_profit = entry_price + (target_profit_dollars / quantity)
        required_cash = quantity * entry_price
        
        return {
            'symbol': symbol,
            'entry_price': entry_price,
            'quantity': quantity,
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'required_cash': required_cash,
            'stop_type': 'fixed',
            'stop_analysis': f'3% stop loss at ${stop_loss:.2f}',
            'ttm_analysis': f'Mock TTM analysis for {symbol} - Grade B setup detected'
        }
    
    def update_stop_loss(self, symbol: str, entry_price: float, current_stop: float) -> Dict[str, Any]:
        """Update stop loss recommendation."""
        # Simple trailing stop logic
        current_price = entry_price * random.uniform(0.98, 1.05)  # Mock current price
        
        # Calculate trailing stop (2% below current price)
        recommended_stop = current_price * 0.98
        should_update = recommended_stop > current_stop
        
        return {
            'symbol': symbol,
            'current_stop': current_stop,
            'recommended_stop': recommended_stop,
            'should_update': should_update,
            'stop_type': 'trailing',
            'analysis': f'Current price: ${current_price:.2f}, trailing stop recommended',
            'trailing_update': f'Move stop to ${recommended_stop:.2f}' if should_update else 'Keep current stop'
        }


if __name__ == "__main__":
    # Test the planner
    planner = ProfitTargetPlanner()
    
    print("🧪 Testing Profit Target Planner")
    print("=" * 40)
    
    # Test plan generation
    plan = planner.plan(50)
    print(f"Plan for $50 profit:")
    print(f"Symbol: {plan['symbol']}")
    print(f"Entry: ${plan['entry_price']:.2f}")
    print(f"Quantity: {plan['quantity']}")
    print(f"Target: ${plan['take_profit']:.2f}")
    print(f"Stop: ${plan['stop_loss']:.2f}")
    
    # Test stop loss update
    stop_update = planner.update_stop_loss("AAPL", 150.0, 145.0)
    print(f"\nStop loss update:")
    print(f"Should update: {stop_update['should_update']}")
    print(f"Recommended: ${stop_update['recommended_stop']:.2f}")
