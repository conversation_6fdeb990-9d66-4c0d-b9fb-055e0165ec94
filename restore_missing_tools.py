#!/usr/bin/env python3
"""
Restore Missing Tools to TotalRecall

This script identifies and restores any tools that may have been lost
during the MCP integration process.
"""

import sys
from pathlib import Path

def check_missing_tools():
    """Check for missing tools and restore them."""
    
    print("🔍 CHECKING FOR MISSING TOOLS")
    print("=" * 40)
    
    try:
        from core.chat_core import TOOLS
        current_tools = set(TOOLS.keys())
        print(f"📊 Current tools count: {len(current_tools)}")
        
        # Expected tools based on your original system
        expected_tools = {
            # TTM Analysis Tools
            "scan_ttm_squeeze_opportunities",
            "get_ttm_squeeze_analysis", 
            "analyze_ttm_setup",
            "ttm_theory",
            "ttm_grade_analysis",
            "ttm_market_context",
            "ttm_options_combo",
            "scan_simple_ttm_squeeze",
            "scan_advanced_ttm_squeeze",
            "unified_ttm_scan",
            "best_ttm_trade_now",
            "fastest_ttm_profits",
            "find_and_trade_ttm",
            
            # Profit Planning Tools
            "make_profit_plan",
            "make_profit_ttm", 
            "execute_profit_target",
            "enhanced_profit_planning",
            "small_account_ttm_strategy",
            
            # Market Analysis Tools
            "analyze_stock_comprehensive",
            "get_market_quote",
            "market_news_summary",
            "clean_news_analysis",
            "economic_calendar_analysis",
            "reddit_sentiment_analysis",
            "social_buzz_analysis",
            "options_flow_analysis",
            
            # Options Tools
            "get_options_strategy_recommendation",
            "analyze_options_strategies_comprehensive",
            "scan_best_options_opportunities",
            "scan_options_strategy_opportunities",
            "options_strategy",
            "options_pricing",
            "options_greeks",
            
            # Performance & Analytics
            "performance_summary",
            "show_performance_dashboard",
            "performance_heatmap",
            "ttm_grade_analysis",
            "strategy_comparison",
            "risk_analysis",
            "export_performance",
            "confidence_analysis",
            "learning_insights",
            "strategy_ranking",
            
            # Automation & Control
            "start_automation_conservative",
            "start_automation_balanced", 
            "stop_automation",
            "automation_status",
            "emergency_stop",
            "show_automation_panel",
            "start_monitoring",
            "stop_monitoring",
            "monitoring_status",
            "monitor_breakouts",
            "update_stop_loss",
            
            # System Tools
            "system_status",
            "explain_symbol",
            "judge_investment",
            "show_help",
            
            # MCP Enhanced Tools (should be present)
            "get_account_balance",
            "get_current_positions",
            "get_specific_position",
            "get_enhanced_quote",
            "get_stock_history",
            "get_stock_trades",
            "get_latest_trade",
            "get_latest_bar",
            "get_all_orders",
            "place_enhanced_order",
            "cancel_all_orders",
            "cancel_specific_order",
            "close_position",
            "close_all_positions",
            "get_asset_info",
            "search_assets",
            "get_watchlists",
            "create_watchlist",
            "update_watchlist",
            "get_market_status",
            "get_market_calendar",
            "get_earnings_calendar",
            "get_option_contracts",
            "get_option_quote",
            "get_options_greeks",
            "place_option_order",
            
            # Advanced Algorithmic Trading
            "run_momentum_algorithm",
            "run_mean_reversion_algorithm",
            "run_pairs_trading",
            "detect_market_regime",
            
            # Advanced Options Strategies
            "create_iron_condor",
            "create_butterfly_spread",
            "volatility_strategy_selector"
        }
        
        missing_tools = expected_tools - current_tools
        extra_tools = current_tools - expected_tools
        
        print(f"📊 Expected tools count: {len(expected_tools)}")
        print(f"❌ Missing tools count: {len(missing_tools)}")
        print(f"➕ Extra tools count: {len(extra_tools)}")
        
        if missing_tools:
            print(f"\n❌ MISSING TOOLS ({len(missing_tools)}):")
            for tool in sorted(missing_tools):
                print(f"   • {tool}")
        
        if extra_tools:
            print(f"\n➕ EXTRA TOOLS ({len(extra_tools)}):")
            for tool in sorted(extra_tools):
                print(f"   • {tool}")
        
        # Calculate what we should have
        total_expected = len(expected_tools)
        total_current = len(current_tools)
        
        print(f"\n📈 TOOL COUNT ANALYSIS:")
        print(f"   Expected: {total_expected}")
        print(f"   Current:  {total_current}")
        print(f"   Difference: {total_current - total_expected:+d}")
        
        if total_current >= total_expected:
            print("✅ Tool count looks good!")
        else:
            print(f"⚠️ Missing {total_expected - total_current} tools")
        
        return missing_tools, current_tools, expected_tools
        
    except Exception as e:
        print(f"❌ Error checking tools: {e}")
        return set(), set(), set()

def restore_missing_functions():
    """Restore any missing function implementations."""
    
    print("\n🔧 CHECKING FUNCTION IMPLEMENTATIONS")
    print("=" * 45)
    
    try:
        from core.chat_core import TOOLS
        
        # Check if functions are actually callable
        broken_tools = []
        working_tools = []
        
        for tool_name, tool_data in TOOLS.items():
            try:
                func = tool_data.get("function")
                if func is None:
                    broken_tools.append(f"{tool_name} - No function")
                elif not callable(func):
                    broken_tools.append(f"{tool_name} - Not callable")
                else:
                    working_tools.append(tool_name)
            except Exception as e:
                broken_tools.append(f"{tool_name} - Error: {e}")
        
        print(f"✅ Working tools: {len(working_tools)}")
        print(f"❌ Broken tools: {len(broken_tools)}")
        
        if broken_tools:
            print(f"\n❌ BROKEN TOOLS:")
            for tool in broken_tools:
                print(f"   • {tool}")
        
        return len(broken_tools) == 0
        
    except Exception as e:
        print(f"❌ Error checking functions: {e}")
        return False

def main():
    """Main restoration function."""
    
    print("🔧 TOTALRECALL TOOLS RESTORATION")
    print("=" * 50)
    print("Checking for missing tools after MCP integration...\n")
    
    # Check missing tools
    missing_tools, current_tools, expected_tools = check_missing_tools()
    
    # Check function implementations
    functions_ok = restore_missing_functions()
    
    # Summary
    print(f"\n📊 RESTORATION SUMMARY")
    print("=" * 30)
    
    current_count = len(current_tools)
    expected_count = len(expected_tools)
    missing_count = len(missing_tools)
    
    print(f"Current Tools: {current_count}")
    print(f"Expected Tools: {expected_count}")
    print(f"Missing Tools: {missing_count}")
    print(f"Functions Working: {'✅ Yes' if functions_ok else '❌ No'}")
    
    if current_count >= 100:
        print(f"\n🎉 EXCELLENT! You have {current_count} tools!")
        print("✅ Your system has more tools than expected")
    elif current_count >= 90:
        print(f"\n✅ GOOD! You have {current_count} tools")
        print("Your system is well-equipped")
    else:
        print(f"\n⚠️ You have {current_count} tools")
        print("Some tools may be missing")
    
    if missing_count == 0:
        print("\n🎉 ALL EXPECTED TOOLS ARE PRESENT!")
    else:
        print(f"\n⚠️ {missing_count} tools are missing")
        print("These may be due to:")
        print("• Import errors")
        print("• Module dependencies")
        print("• Configuration issues")
    
    # Recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    if current_count >= 90:
        print("✅ Your TotalRecall system is ready for trading!")
        print("✅ Both original and MCP tools are available")
        print("✅ Try: 'make me $50 today' and 'What's my account balance?'")
    else:
        print("🔧 Consider checking module imports")
        print("🔧 Verify all dependencies are installed")
        print("🔧 Check for any import errors in chat_core.py")
    
    return current_count >= 90

if __name__ == "__main__":
    main()
