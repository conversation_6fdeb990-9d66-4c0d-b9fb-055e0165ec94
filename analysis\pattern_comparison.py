#!/usr/bin/env python3
"""
TTM Pattern Analysis Comparison
Compares the accuracy of different TTM squeeze pattern detection methods
"""

import pandas as pd
import numpy as np
import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any, Optional
import warnings
warnings.filterwarnings('ignore')

# Try to import pandas_ta
try:
    import pandas_ta as ta
    PANDAS_TA_AVAILABLE = True
except ImportError:
    PANDAS_TA_AVAILABLE = False
    print("⚠️ pandas_ta not available. Install with: pip install pandas_ta")

try:
    from scanners.enhanced_ttm_squeeze_scanner import EnhancedTTMSqueezeScanner
    ENHANCED_SCANNER_AVAILABLE = True
except ImportError:
    ENHANCED_SCANNER_AVAILABLE = False
    print("⚠️ Enhanced scanner not available")

try:
    from core.logger_util import info, warning, error
except ImportError:
    def info(msg): print(f"ℹ️ {msg}")
    def warning(msg): print(f"⚠️ {msg}")
    def error(msg): print(f"❌ {msg}")


class PandasTAPatternAnalyzer:
    """Your pandas_ta based pattern analysis implementation."""
    
    @staticmethod
    def calculate_signal(df: pd.DataFrame) -> pd.Series:
        """
        Calculate TTM squeeze signal using pandas_ta approach.
        Returns binary signal (1 = setup, 0 = no setup).
        """
        try:
            if not PANDAS_TA_AVAILABLE:
                return pd.Series([0] * len(df), index=df.index)
            
            # Calculate 5-period EMA
            ema5 = ta.ema(df['close'], length=5)
            
            # Calculate 8-period EMA
            ema8 = ta.ema(df['close'], length=8)
            
            # Calculate Momentum
            momentum = ta.mom(df['close'], length=14)
            
            # Calculate TTM Squeeze
            ttm_squeeze = ta.squeeze(df['high'], df['low'], df['close'])
            
            # EMA5 conditions
            ema5_rising = ema5 > ema5.shift(1)
            
            # EMA8 conditions
            ema8_rising = ema8 > ema8.shift(3)
            
            # Momentum conditions
            mom_rising = momentum > momentum.shift(3)
            
            # TTM Squeeze Histogram conditions
            hist = ttm_squeeze['SQZ_HIST']
            hist_rising = (hist > hist.shift(1)) & \
                          (hist.shift(1) < hist.shift(2)) & \
                          (hist.shift(2) < hist.shift(3)) & \
                          (hist.shift(3) < hist.shift(4)) & \
                          (hist.shift(4) < hist.shift(5))
            
            # Five dots in a row (SqueezeAlert == 0 for 3 periods)
            squeeze_alert = ttm_squeeze['SQZ_NO']
            five_dots = squeeze_alert.rolling(window=3).sum() == 3
            
            # Combine all conditions
            signal = ema5_rising & ema8_rising & mom_rising & hist_rising & five_dots
            
            return signal.astype(int)
            
        except Exception as e:
            error(f"Error in pandas_ta pattern analysis: {e}")
            return pd.Series([0] * len(df), index=df.index)
    
    @staticmethod
    def get_signal_details(df: pd.DataFrame) -> Dict[str, Any]:
        """Get detailed breakdown of signal components."""
        try:
            if not PANDAS_TA_AVAILABLE:
                return {"error": "pandas_ta not available"}
            
            # Calculate components
            ema5 = ta.ema(df['close'], length=5)
            ema8 = ta.ema(df['close'], length=8)
            momentum = ta.mom(df['close'], length=14)
            ttm_squeeze = ta.squeeze(df['high'], df['low'], df['close'])
            
            # Individual conditions
            ema5_rising = ema5 > ema5.shift(1)
            ema8_rising = ema8 > ema8.shift(3)
            mom_rising = momentum > momentum.shift(3)
            
            hist = ttm_squeeze['SQZ_HIST']
            hist_rising = (hist > hist.shift(1)) & \
                          (hist.shift(1) < hist.shift(2)) & \
                          (hist.shift(2) < hist.shift(3)) & \
                          (hist.shift(3) < hist.shift(4)) & \
                          (hist.shift(4) < hist.shift(5))
            
            squeeze_alert = ttm_squeeze['SQZ_NO']
            five_dots = squeeze_alert.rolling(window=3).sum() == 3
            
            # Get latest values
            latest_idx = df.index[-1]
            
            return {
                "ema5_rising": bool(ema5_rising.iloc[-1]) if not pd.isna(ema5_rising.iloc[-1]) else False,
                "ema8_rising": bool(ema8_rising.iloc[-1]) if not pd.isna(ema8_rising.iloc[-1]) else False,
                "momentum_rising": bool(mom_rising.iloc[-1]) if not pd.isna(mom_rising.iloc[-1]) else False,
                "histogram_rising": bool(hist_rising.iloc[-1]) if not pd.isna(hist_rising.iloc[-1]) else False,
                "five_dots": bool(five_dots.iloc[-1]) if not pd.isna(five_dots.iloc[-1]) else False,
                "ema5_value": float(ema5.iloc[-1]) if not pd.isna(ema5.iloc[-1]) else 0,
                "ema8_value": float(ema8.iloc[-1]) if not pd.isna(ema8.iloc[-1]) else 0,
                "momentum_value": float(momentum.iloc[-1]) if not pd.isna(momentum.iloc[-1]) else 0,
                "histogram_value": float(hist.iloc[-1]) if not pd.isna(hist.iloc[-1]) else 0,
                "squeeze_active": bool(squeeze_alert.iloc[-1]) if not pd.isna(squeeze_alert.iloc[-1]) else False
            }
            
        except Exception as e:
            error(f"Error getting signal details: {e}")
            return {"error": str(e)}


class EnhancedPatternAnalyzer:
    """Enhanced pattern analyzer wrapper for comparison."""
    
    def __init__(self):
        self.scanner = EnhancedTTMSqueezeScanner() if ENHANCED_SCANNER_AVAILABLE else None
    
    def calculate_signal(self, df: pd.DataFrame) -> pd.Series:
        """Calculate signal using enhanced pattern recognition."""
        try:
            if not self.scanner:
                return pd.Series([0] * len(df), index=df.index)
            
            # Calculate technical indicators
            df_with_indicators = self.scanner.calculate_technical_indicators(df.copy())
            
            # Analyze setup quality
            if len(df_with_indicators) < 50:
                return pd.Series([0] * len(df), index=df.index)
            
            # Get current squeeze status and quality scores
            current = df_with_indicators.iloc[-1]
            
            if not current.get('squeeze_on', False):
                return pd.Series([0] * len(df), index=df.index)
            
            # Extract arrays for pattern analysis
            bb_width = df_with_indicators['bb_width'].values
            kc_width = df_with_indicators['kc_width'].values
            prices = df_with_indicators['close'].values
            momentum = df_with_indicators['momentum'].values
            volume = df_with_indicators['volume'].values
            ema5 = df_with_indicators['ema5'].values
            ema8 = df_with_indicators['ema8'].values
            ema21 = df_with_indicators['ema21'].values
            
            # Calculate pattern scores
            compression_score = self.scanner.pattern_analyzer.analyze_compression_quality(bb_width, kc_width)
            momentum_score = self.scanner.pattern_analyzer.detect_momentum_divergence(prices, momentum)
            volume_score = self.scanner.pattern_analyzer.analyze_volume_confirmation(
                volume, current['avg_volume_20'], (prices[-1] - prices[-2]) / prices[-2]
            )
            directional_score = self.scanner.pattern_analyzer.calculate_directional_bias(ema5, ema8, ema21, momentum)
            
            # Calculate overall quality
            pattern_quality = (compression_score * 0.3 + momentum_score * 0.25 + 
                             volume_score * 0.25 + directional_score * 0.2)
            
            # Create signal series (1 if high quality setup, 0 otherwise)
            signal_value = 1 if pattern_quality >= 0.6 else 0
            return pd.Series([signal_value] * len(df), index=df.index)
            
        except Exception as e:
            error(f"Error in enhanced pattern analysis: {e}")
            return pd.Series([0] * len(df), index=df.index)
    
    def get_signal_details(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Get detailed breakdown of enhanced signal components."""
        try:
            if not self.scanner:
                return {"error": "Enhanced scanner not available"}
            
            # Calculate technical indicators
            df_with_indicators = self.scanner.calculate_technical_indicators(df.copy())
            
            if len(df_with_indicators) < 50:
                return {"error": "Insufficient data"}
            
            current = df_with_indicators.iloc[-1]
            
            # Extract arrays for pattern analysis
            bb_width = df_with_indicators['bb_width'].values
            kc_width = df_with_indicators['kc_width'].values
            prices = df_with_indicators['close'].values
            momentum = df_with_indicators['momentum'].values
            volume = df_with_indicators['volume'].values
            ema5 = df_with_indicators['ema5'].values
            ema8 = df_with_indicators['ema8'].values
            ema21 = df_with_indicators['ema21'].values
            
            # Calculate all scores
            compression_score = self.scanner.pattern_analyzer.analyze_compression_quality(bb_width, kc_width)
            momentum_score = self.scanner.pattern_analyzer.detect_momentum_divergence(prices, momentum)
            volume_score = self.scanner.pattern_analyzer.analyze_volume_confirmation(
                volume, current['avg_volume_20'], (prices[-1] - prices[-2]) / prices[-2]
            )
            directional_score = self.scanner.pattern_analyzer.calculate_directional_bias(ema5, ema8, ema21, momentum)
            
            pattern_quality = (compression_score * 0.3 + momentum_score * 0.25 + 
                             volume_score * 0.25 + directional_score * 0.2)
            
            return {
                "squeeze_active": bool(current.get('squeeze_on', False)),
                "compression_score": float(compression_score),
                "momentum_score": float(momentum_score),
                "volume_score": float(volume_score),
                "directional_score": float(directional_score),
                "pattern_quality": float(pattern_quality),
                "bb_width": float(current.get('bb_width', 0)),
                "kc_width": float(current.get('kc_width', 0)),
                "squeeze_ratio": float(current.get('squeeze_ratio', 0)),
                "ema5_value": float(current.get('ema5', 0)),
                "ema8_value": float(current.get('ema8', 0)),
                "ema21_value": float(current.get('ema21', 0)),
                "momentum_value": float(current.get('momentum', 0)),
                "volume_ratio": float(current.get('volume_ratio', 0))
            }
            
        except Exception as e:
            error(f"Error getting enhanced signal details: {e}")
            return {"error": str(e)}


class PatternComparisonAnalyzer:
    """Comprehensive pattern comparison and accuracy analysis."""
    
    def __init__(self):
        self.pandas_ta_analyzer = PandasTAPatternAnalyzer()
        self.enhanced_analyzer = EnhancedPatternAnalyzer()
        
    async def get_market_data(self, symbol: str, timeframe: str = "15min", limit: int = 200) -> pd.DataFrame:
        """Get market data for analysis."""
        try:
            if ENHANCED_SCANNER_AVAILABLE:
                scanner = EnhancedTTMSqueezeScanner()
                return await scanner.get_market_data(symbol, timeframe, limit)
            else:
                # Fallback - create dummy data for testing
                dates = pd.date_range(end=datetime.now(), periods=limit, freq='15min')
                np.random.seed(42)  # For reproducible results
                
                # Generate realistic price data
                base_price = 100
                returns = np.random.normal(0, 0.02, limit)
                prices = [base_price]
                
                for ret in returns[1:]:
                    prices.append(prices[-1] * (1 + ret))
                
                df = pd.DataFrame({
                    'date': dates,
                    'open': prices,
                    'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
                    'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
                    'close': prices,
                    'volume': np.random.randint(100000, 1000000, limit)
                })
                
                return df
                
        except Exception as e:
            error(f"Error getting market data for {symbol}: {e}")
            return pd.DataFrame()
    
    async def compare_patterns_single_symbol(self, symbol: str) -> Dict[str, Any]:
        """Compare pattern detection methods for a single symbol."""
        try:
            info(f"🔍 Analyzing {symbol}...")
            
            # Get market data
            df = await self.get_market_data(symbol, "15min", 200)
            
            if df.empty:
                return {"symbol": symbol, "error": "No data available"}
            
            # Analyze with both methods
            pandas_ta_signal = self.pandas_ta_analyzer.calculate_signal(df)
            enhanced_signal = self.enhanced_analyzer.calculate_signal(df)
            
            # Get detailed breakdowns
            pandas_ta_details = self.pandas_ta_analyzer.get_signal_details(df)
            enhanced_details = self.enhanced_analyzer.get_signal_details(df)
            
            # Calculate signal statistics
            pandas_ta_signals = pandas_ta_signal.sum()
            enhanced_signals = enhanced_signal.sum()
            
            # Agreement analysis
            agreement = (pandas_ta_signal == enhanced_signal).sum() / len(pandas_ta_signal)
            
            # Current signal status
            current_pandas_ta = pandas_ta_signal.iloc[-1] if len(pandas_ta_signal) > 0 else 0
            current_enhanced = enhanced_signal.iloc[-1] if len(enhanced_signal) > 0 else 0
            
            return {
                "symbol": symbol,
                "current_price": float(df['close'].iloc[-1]),
                "data_points": len(df),
                "pandas_ta": {
                    "current_signal": bool(current_pandas_ta),
                    "total_signals": int(pandas_ta_signals),
                    "signal_rate": float(pandas_ta_signals / len(df)),
                    "details": pandas_ta_details
                },
                "enhanced": {
                    "current_signal": bool(current_enhanced),
                    "total_signals": int(enhanced_signals),
                    "signal_rate": float(enhanced_signals / len(df)),
                    "details": enhanced_details
                },
                "comparison": {
                    "agreement_rate": float(agreement),
                    "both_signal": bool(current_pandas_ta and current_enhanced),
                    "pandas_ta_only": bool(current_pandas_ta and not current_enhanced),
                    "enhanced_only": bool(not current_pandas_ta and current_enhanced),
                    "neither_signal": bool(not current_pandas_ta and not current_enhanced)
                }
            }
            
        except Exception as e:
            error(f"Error comparing patterns for {symbol}: {e}")
            return {"symbol": symbol, "error": str(e)}
    
    async def comprehensive_comparison(self, symbols: List[str]) -> Dict[str, Any]:
        """Run comprehensive comparison across multiple symbols."""
        try:
            info(f"🚀 Starting comprehensive pattern comparison for {len(symbols)} symbols...")
            
            results = []
            for symbol in symbols:
                result = await self.compare_patterns_single_symbol(symbol)
                results.append(result)
                
                # Brief pause between symbols
                await asyncio.sleep(0.5)
            
            # Aggregate statistics
            valid_results = [r for r in results if "error" not in r]
            
            if not valid_results:
                return {"error": "No valid results obtained"}
            
            # Calculate overall statistics
            total_agreement = np.mean([r["comparison"]["agreement_rate"] for r in valid_results])
            
            pandas_ta_signal_rate = np.mean([r["pandas_ta"]["signal_rate"] for r in valid_results])
            enhanced_signal_rate = np.mean([r["enhanced"]["signal_rate"] for r in valid_results])
            
            # Current signal distribution
            current_signals = {
                "both_signal": sum(1 for r in valid_results if r["comparison"]["both_signal"]),
                "pandas_ta_only": sum(1 for r in valid_results if r["comparison"]["pandas_ta_only"]),
                "enhanced_only": sum(1 for r in valid_results if r["comparison"]["enhanced_only"]),
                "neither_signal": sum(1 for r in valid_results if r["comparison"]["neither_signal"])
            }
            
            return {
                "analysis_timestamp": datetime.now().isoformat(),
                "symbols_analyzed": len(valid_results),
                "symbols_with_errors": len(results) - len(valid_results),
                "overall_statistics": {
                    "agreement_rate": total_agreement,
                    "pandas_ta_signal_rate": pandas_ta_signal_rate,
                    "enhanced_signal_rate": enhanced_signal_rate,
                    "current_signal_distribution": current_signals
                },
                "detailed_results": results,
                "summary": self._generate_summary(valid_results)
            }
            
        except Exception as e:
            error(f"Error in comprehensive comparison: {e}")
            return {"error": str(e)}
    
    def _generate_summary(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate summary analysis of pattern comparison."""
        try:
            # Find symbols with current signals
            pandas_ta_signals = [r for r in results if r["pandas_ta"]["current_signal"]]
            enhanced_signals = [r for r in results if r["enhanced"]["current_signal"]]
            both_signals = [r for r in results if r["comparison"]["both_signal"]]
            
            # Quality analysis for enhanced method
            enhanced_quality_scores = []
            for r in results:
                if "details" in r["enhanced"] and "pattern_quality" in r["enhanced"]["details"]:
                    enhanced_quality_scores.append(r["enhanced"]["details"]["pattern_quality"])
            
            avg_quality = np.mean(enhanced_quality_scores) if enhanced_quality_scores else 0
            
            return {
                "pandas_ta_current_signals": len(pandas_ta_signals),
                "enhanced_current_signals": len(enhanced_signals),
                "agreement_signals": len(both_signals),
                "enhanced_avg_quality": float(avg_quality),
                "recommendation": self._get_recommendation(results)
            }
            
        except Exception as e:
            error(f"Error generating summary: {e}")
            return {"error": str(e)}
    
    def _get_recommendation(self, results: List[Dict[str, Any]]) -> str:
        """Generate recommendation based on analysis."""
        try:
            total_symbols = len(results)
            agreement_rate = np.mean([r["comparison"]["agreement_rate"] for r in results])
            
            pandas_ta_signals = sum(1 for r in results if r["pandas_ta"]["current_signal"])
            enhanced_signals = sum(1 for r in results if r["enhanced"]["current_signal"])
            
            if agreement_rate > 0.8:
                return "HIGH_AGREEMENT: Both methods show strong correlation. Either can be used reliably."
            elif enhanced_signals < pandas_ta_signals * 0.5:
                return "ENHANCED_MORE_SELECTIVE: Enhanced method is more selective, likely higher quality signals."
            elif pandas_ta_signals < enhanced_signals * 0.5:
                return "PANDAS_TA_MORE_SELECTIVE: pandas_ta method is more selective."
            else:
                return "MODERATE_AGREEMENT: Methods show different signal patterns. Consider combining both."
                
        except Exception as e:
            return f"Error generating recommendation: {e}"


async def main():
    """Run pattern comparison analysis."""
    print("🎯 TTM PATTERN ANALYSIS COMPARISON")
    print("=" * 50)
    
    # Test symbols
    test_symbols = ['AAPL', 'PLTR', 'MSFT', 'NVDA', 'TSLA', 'META', 'GOOGL', 'AMZN']
    
    # Create analyzer
    analyzer = PatternComparisonAnalyzer()
    
    # Run comprehensive comparison
    results = await analyzer.comprehensive_comparison(test_symbols)
    
    if "error" in results:
        print(f"❌ Analysis failed: {results['error']}")
        return
    
    # Display results
    print(f"\n📊 ANALYSIS RESULTS")
    print(f"Symbols Analyzed: {results['symbols_analyzed']}")
    print(f"Agreement Rate: {results['overall_statistics']['agreement_rate']:.1%}")
    print(f"pandas_ta Signal Rate: {results['overall_statistics']['pandas_ta_signal_rate']:.1%}")
    print(f"Enhanced Signal Rate: {results['overall_statistics']['enhanced_signal_rate']:.1%}")
    
    print(f"\n🎯 CURRENT SIGNALS:")
    dist = results['overall_statistics']['current_signal_distribution']
    print(f"Both Methods: {dist['both_signal']}")
    print(f"pandas_ta Only: {dist['pandas_ta_only']}")
    print(f"Enhanced Only: {dist['enhanced_only']}")
    print(f"Neither: {dist['neither_signal']}")
    
    print(f"\n💡 RECOMMENDATION:")
    print(f"{results['summary']['recommendation']}")
    
    # Show detailed results for symbols with signals
    print(f"\n📋 DETAILED RESULTS:")
    for result in results['detailed_results']:
        if "error" not in result:
            symbol = result['symbol']
            pandas_ta_signal = result['pandas_ta']['current_signal']
            enhanced_signal = result['enhanced']['current_signal']
            
            if pandas_ta_signal or enhanced_signal:
                print(f"\n{symbol}: ${result['current_price']:.2f}")
                print(f"  pandas_ta: {'✅' if pandas_ta_signal else '❌'}")
                print(f"  Enhanced: {'✅' if enhanced_signal else '❌'}")
                
                if enhanced_signal and "details" in result['enhanced']:
                    details = result['enhanced']['details']
                    if "pattern_quality" in details:
                        print(f"  Quality: {details['pattern_quality']:.1%}")


if __name__ == "__main__":
    asyncio.run(main())
