#!/usr/bin/env python3
"""TotalRecall Trading System Launcher

Main entry point for the Enhanced TTM Squeeze Trading System with
sophisticated pattern recognition and high-frequency scanning.
"""
import sys
import os
import subprocess
from pathlib import Path

def check_enhanced_features():
    """Check if enhanced scanning features are available."""
    try:
        # Add paths for imports
        current_dir = Path(__file__).parent
        sys.path.insert(0, str(current_dir / 'core'))
        sys.path.insert(0, str(current_dir / 'scanners'))

        from scanners.enhanced_ttm_squeeze_scanner import EnhancedTTMSqueezeScanner
        from core.high_frequency_scanner import get_high_frequency_scanner
        from core.symbol_manager import get_symbol_manager
        return True
    except ImportError:
        return False

def main():
    """Launch the Enhanced TTM Trading System GUI."""
    try:
        print("🚀 Starting TotalRecall Trading System...")
        print("📊 Initializing Enhanced TTM Squeeze Scanner...")

        # Check for enhanced features
        enhanced_available = check_enhanced_features()
        if enhanced_available:
            print("⚡ Enhanced scanning features available!")
            print("   • Sophisticated pattern recognition algorithms")
            print("   • S&P 500 + $100B+ market cap coverage")
            print("   • High-frequency 5-minute scanning")
            print("   • Advanced 5-point quality criteria system")
        else:
            print("⚠️ Enhanced features not available - using standard scanner")
            print("💡 Install dependencies: pip install aiohttp")

        # Change to the core directory and run launch_gui.py
        core_dir = os.path.join(os.path.dirname(__file__), 'core')
        launch_script = os.path.join(core_dir, 'launch_gui.py')

        if os.path.exists(launch_script):
            print("📂 Launching TotalRecall GUI...")
            if enhanced_available:
                print("✅ Enhanced TTM Scanner integrated and ready!")
                print("   • Click '⚡ Enhanced Scanner' for sophisticated pattern detection")
                print("   • Click '🔥 High-Freq Scan' for continuous S&P 500 monitoring")

            # Run the launch script from the core directory
            result = subprocess.run([sys.executable, launch_script],
                                  cwd=core_dir,
                                  capture_output=False)
            return result.returncode
        else:
            print(f"❌ Launch script not found: {launch_script}")
            return 1

    except Exception as e:
        print(f"❌ Error starting system: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    if exit_code != 0:
        print("\n💡 Troubleshooting:")
        print("1. Make sure all dependencies are installed:")
        print("   pip install -r config/requirements.txt")
        print("2. Check that all files are in the correct folders")
        print("3. Try running directly: python core/launch_gui.py")
        sys.exit(exit_code)