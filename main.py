#!/usr/bin/env python3
"""TTM Trading System Launcher

Main entry point for the TTM Squeeze Trading System.
"""
import sys
import os
import subprocess

def main():
    """Launch the TTM Trading System GUI."""
    try:
        print("🚀 Starting TTM Trading System...")

        # Change to the core directory and run launch_gui.py
        core_dir = os.path.join(os.path.dirname(__file__), 'core')
        launch_script = os.path.join(core_dir, 'launch_gui.py')

        if os.path.exists(launch_script):
            print("📂 Launching from core directory...")
            # Run the launch script from the core directory
            result = subprocess.run([sys.executable, launch_script],
                                  cwd=core_dir,
                                  capture_output=False)
            return result.returncode
        else:
            print(f"❌ Launch script not found: {launch_script}")
            return 1

    except Exception as e:
        print(f"❌ Error starting system: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    if exit_code != 0:
        print("\n💡 Troubleshooting:")
        print("1. Make sure all dependencies are installed:")
        print("   pip install -r config/requirements.txt")
        print("2. Check that all files are in the correct folders")
        print("3. Try running directly: python core/launch_gui.py")
        sys.exit(exit_code)