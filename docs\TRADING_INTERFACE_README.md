# Ultimate Trading Expert GUI Interface

## 🎯 Overview

A comprehensive trading interface that combines AI-powered trading advice with advanced technical analysis tools. Features real-time TTM Squeeze scanning with A-F grading, dynamic stop loss management, and automated alerts for high-grade opportunities.

## 🚀 Quick Start

```bash
# Launch the GUI interface
python launch_gui.py

# Or launch directly
python trading_interface.py
```

## 📊 Features

### 💬 AI Chat Interface
- **Direct communication** with the Ultimate Trading Expert AI
- **Function calling** for specific trading tasks
- **Real-time responses** with actionable trading plans
- **Chat history** for reference

**Example Commands:**
- "make me $50 profit today"
- "analyze NVDA for trading opportunities"
- "scan for TTM squeeze setups"
- "update my stop loss for AAPL"

### 🎯 TTM Squeeze Scanner
- **A-F Grading System** for signal quality
- **Multi-timeframe scanning** (15min, 1hour, 1day)
- **Real-time market data** from Financial Modeling Prep
- **Customizable filters** (min grade, max results)
- **Manual and automatic scanning** modes

**Grading Criteria:**
- **A+ (90-100%)**: Perfect setup - all criteria met
- **A (85-89%)**: Excellent setup - strong signals
- **B (75-84%)**: Good setup - most criteria met
- **C (65-74%)**: Average setup - some criteria met
- **D (55-64%)**: Weak setup - few criteria met
- **F (<55%)**: Poor setup - avoid

### 🔔 Real-time Alerts
- **Automatic monitoring** for A/B grade opportunities
- **5-minute scan intervals** during market hours
- **Visual and text alerts** for new high-grade setups
- **Historical alert log** for tracking opportunities

### 📊 Position Management
- **Dynamic stop loss calculation** based on:
  - ATR volatility analysis
  - Market momentum
  - Support/resistance levels
  - Trailing stop logic
- **Position sizing** for target profit goals
- **Risk/reward analysis**
- **Real-time stop loss updates**

## 🎛️ Interface Tabs

### Tab 1: 💬 Chat & Analysis
- Chat input/output area
- Send button and enter key support
- Clear chat functionality
- AI-powered trading advice

### Tab 2: 🎯 TTM Scanner
- Grade filter dropdown (A, B, C, D, F)
- Max results input
- Scan controls (Manual, Auto, Stop)
- Results table with:
  - Symbol
  - Grade & Confidence
  - Entry/Stop/Target prices
  - Risk:Reward ratio
- Real-time alerts panel

### Tab 3: 📊 Position Manager
- Symbol and quantity inputs
- Entry price and current stop inputs
- Position calculation tools
- Target profit planning
- Dynamic stop loss updates
- Detailed analysis output

## 🔧 Configuration

### Required API Keys
Set these in your `config.env` file:
```
FMP_API_KEY=your_fmp_key
OPENAI_API_KEY=your_openai_key
ALPACA_API_KEY=your_alpaca_key
ALPACA_API_SECRET=your_alpaca_secret
```

### Dependencies
```bash
pip install PySimpleGUI requests openai alpaca-py pandas numpy ta-lib
```

## 📈 Usage Examples

### 1. Finding Profit Opportunities
1. Go to **Chat & Analysis** tab
2. Type: "make me $50 profit today"
3. AI will scan TTM opportunities and provide specific trade plan
4. Review entry, stop, and target prices

### 2. TTM Squeeze Scanning
1. Go to **TTM Scanner** tab
2. Set minimum grade to "B"
3. Click "🔍 Scan Now"
4. Review results table for opportunities
5. Click "🔄 Auto Scan" for continuous monitoring

### 3. Managing Existing Positions
1. Go to **Position Manager** tab
2. Enter your position details:
   - Symbol: NVDA
   - Entry Price: 141.50
   - Current Stop: 138.50
3. Click "🛡️ Update Stop Loss"
4. Review dynamic stop recommendations

### 4. Setting Up Alerts
1. Go to **TTM Scanner** tab
2. Set minimum grade to "B"
3. Click "🔄 Auto Scan"
4. Monitor alerts panel for new opportunities
5. System scans every 5 minutes automatically

## 🎯 TTM Squeeze Pattern Detection

The scanner identifies the exact pattern from successful setups:

### Core Components
1. **Squeeze Release** - Bollinger Bands break outside Keltner Channels
2. **Histogram Build** - 3 consecutive rising bars after ≥2 down bars
3. **EMA Confirmation** - 8-EMA rising vs 4 bars ago
4. **Momentum Confirmation** - Momentum(12) rising vs 4 bars ago
5. **Price Filter** - Close > 5-EMA
6. **SqueezeLine Threshold** - SqueezeLine > 50% (relaxed for more opportunities)

### Market Scanning
- **50 large cap stocks** (>$100B market cap)
- **3 timeframes** (15min, 1hour, 1day)
- **Real-time data** from Financial Modeling Prep
- **~8 second scan time** for full analysis

## 🛡️ Dynamic Stop Loss Features

### ATR-Based Volatility Adjustment
- Calculates 14-period Average True Range
- Adjusts stop distance based on volatility
- Wider stops for volatile stocks
- Tighter stops for stable stocks

### Momentum-Based Adjustments
- Analyzes 10-period price momentum
- Tighter stops in weak trends
- Wider stops in strong trends
- Directional bias consideration

### Support/Resistance Integration
- Identifies key support/resistance levels
- Adjusts stops to respect market structure
- Prevents stops in "no man's land"

### Trailing Stop Logic
- Automatically trails stops in profitable positions
- Trails at 50% of current profit
- Only moves stops in favorable direction
- Protects profits while allowing for growth

## 🚨 Alert System

### High-Grade Opportunity Alerts
- Monitors for A/B grade TTM setups
- 5-minute scanning intervals
- Visual alerts in interface
- Detailed opportunity information

### Alert Information Includes
- Symbol and grade
- Confidence percentage
- Entry, stop, and target prices
- Risk:reward ratio
- Timestamp of discovery

## 📞 Support & Troubleshooting

### Common Issues
1. **GUI won't start**: Install PySimpleGUI (`pip install PySimpleGUI`)
2. **No scan results**: Check FMP API key and internet connection
3. **Chat not working**: Verify OpenAI API key
4. **Position errors**: Ensure Alpaca keys are configured

### Performance Tips
- Use minimum grade "B" or "C" for faster scans
- Limit max results to 10-20 for better performance
- Stop auto-scanning when not actively trading
- Clear chat history periodically

## 🔮 Future Enhancements
- Portfolio integration
- Backtesting capabilities
- Custom alert conditions
- Mobile notifications
- Advanced charting
- Paper trading integration
