#!/usr/bin/env python3
"""Enhanced Strategy Environment Engine

Intelligent strategy ranking and selection based on:
- Real-time market conditions (VIX, trend, volume)
- Economic calendar events
- Sector rotation patterns
- Volatility regimes
- Time-of-day optimization
- Seasonal patterns
- Options expiration cycles
"""
import json
import sqlite3
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from pathlib import Path
import logging


class MarketEnvironment:
    """Represents current market environment with all relevant factors."""
    
    def __init__(self):
        self.timestamp = datetime.now()
        
        # Market condition factors
        self.vix_level = 0.0
        self.vix_regime = "normal"  # low, normal, high, extreme
        self.trend_strength = 0.0
        self.trend_direction = "neutral"  # bullish, bearish, neutral
        self.volume_regime = "normal"  # low, normal, high
        self.sector_rotation = "balanced"  # growth, value, defensive, balanced
        
        # Time factors
        self.time_of_day = "market_hours"  # pre_market, market_open, mid_day, power_hour, after_hours
        self.day_of_week = datetime.now().strftime("%A")
        self.week_of_month = self._get_week_of_month()
        self.options_expiration_week = self._is_options_expiration_week()
        
        # Economic factors
        self.economic_events_today = []
        self.earnings_season = self._is_earnings_season()
        self.fed_meeting_week = self._is_fed_meeting_week()
        
        # Sentiment factors
        self.fear_greed_index = 50.0
        self.put_call_ratio = 1.0
        self.margin_debt_level = "normal"
        
        # Calculate overall environment score
        self.environment_score = self._calculate_environment_score()
        self.optimal_strategies = self._determine_optimal_strategies()
    
    def _get_week_of_month(self) -> int:
        """Get week of month (1-4)."""
        day = self.timestamp.day
        return (day - 1) // 7 + 1
    
    def _is_options_expiration_week(self) -> bool:
        """Check if this is options expiration week (3rd Friday)."""
        # Simplified - would use actual options calendar
        return self.week_of_month == 3 and self.day_of_week in ["Thursday", "Friday"]
    
    def _is_earnings_season(self) -> bool:
        """Check if we're in earnings season."""
        month = self.timestamp.month
        return month in [1, 4, 7, 10]  # Quarterly earnings months
    
    def _is_fed_meeting_week(self) -> bool:
        """Check if this is a Fed meeting week."""
        # Simplified - would use actual Fed calendar
        return False  # Placeholder
    
    def _calculate_environment_score(self) -> float:
        """Calculate overall market environment favorability (0-100)."""
        score = 50.0  # Neutral baseline
        
        # VIX impact
        if self.vix_level < 15:
            score += 15  # Low volatility = good for TTM
        elif self.vix_level > 30:
            score -= 10  # High volatility = challenging
        
        # Trend strength impact
        if self.trend_strength > 0.7:
            score += 10  # Strong trends = good for breakouts
        elif self.trend_strength < 0.3:
            score -= 5   # Weak trends = choppy markets
        
        # Time factors
        if self.time_of_day in ["market_open", "power_hour"]:
            score += 5   # High activity periods
        
        if self.options_expiration_week:
            score -= 5   # Increased volatility/manipulation
        
        # Economic events
        if len(self.economic_events_today) > 2:
            score -= 10  # Too many events = uncertainty
        
        return max(0, min(100, score))
    
    def _determine_optimal_strategies(self) -> List[str]:
        """Determine optimal trading strategies for current environment."""
        strategies = []
        
        # Based on VIX regime
        if self.vix_regime == "low":
            strategies.extend(["ttm_squeeze", "momentum_breakout", "trend_following"])
        elif self.vix_regime == "high":
            strategies.extend(["mean_reversion", "volatility_contraction", "range_trading"])
        else:
            strategies.extend(["ttm_squeeze", "adaptive_momentum"])
        
        # Based on trend
        if self.trend_direction == "bullish" and self.trend_strength > 0.6:
            strategies.extend(["bull_flag_breakout", "momentum_continuation"])
        elif self.trend_direction == "bearish" and self.trend_strength > 0.6:
            strategies.extend(["bear_flag_breakdown", "short_squeeze"])
        
        # Based on time
        if self.time_of_day == "market_open":
            strategies.extend(["gap_trading", "opening_range_breakout"])
        elif self.time_of_day == "power_hour":
            strategies.extend(["momentum_acceleration", "closing_drive"])
        
        return list(set(strategies))  # Remove duplicates


class StrategyRanker:
    """Ranks trading strategies based on market environment and historical performance."""
    
    def __init__(self, db_path: str = "data/strategy_ranking.db"):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        
        # Strategy definitions with base characteristics
        self.strategy_definitions = {
            "ttm_squeeze": {
                "name": "TTM Squeeze",
                "description": "Classic TTM squeeze breakout strategy",
                "optimal_vix": (12, 25),
                "optimal_volume": "normal_to_high",
                "optimal_trend": "any",
                "time_preference": ["market_open", "mid_day"],
                "base_success_rate": 0.65,
                "base_reward_risk": 2.5
            },
            "momentum_breakout": {
                "name": "Momentum Breakout",
                "description": "High-momentum breakout strategy",
                "optimal_vix": (10, 20),
                "optimal_volume": "high",
                "optimal_trend": "strong",
                "time_preference": ["market_open", "power_hour"],
                "base_success_rate": 0.60,
                "base_reward_risk": 3.0
            },
            "mean_reversion": {
                "name": "Mean Reversion",
                "description": "Oversold/overbought reversal strategy",
                "optimal_vix": (20, 40),
                "optimal_volume": "normal",
                "optimal_trend": "weak",
                "time_preference": ["mid_day"],
                "base_success_rate": 0.55,
                "base_reward_risk": 2.0
            },
            "volatility_contraction": {
                "name": "Volatility Contraction",
                "description": "Low volatility expansion strategy",
                "optimal_vix": (8, 15),
                "optimal_volume": "low_to_normal",
                "optimal_trend": "any",
                "time_preference": ["mid_day"],
                "base_success_rate": 0.70,
                "base_reward_risk": 2.2
            },
            "trend_following": {
                "name": "Trend Following",
                "description": "Strong trend continuation strategy",
                "optimal_vix": (12, 25),
                "optimal_volume": "normal_to_high",
                "optimal_trend": "strong",
                "time_preference": ["market_open", "mid_day"],
                "base_success_rate": 0.58,
                "base_reward_risk": 2.8
            }
        }
        
        # Initialize database
        self.init_database()
        self.load_historical_performance()
    
    def init_database(self):
        """Initialize strategy ranking database."""
        Path(self.db_path).parent.mkdir(exist_ok=True)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Strategy performance table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS strategy_performance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    strategy_name TEXT,
                    environment_conditions TEXT,
                    trade_date TEXT,
                    success BOOLEAN,
                    pnl REAL,
                    reward_risk_ratio REAL,
                    hold_time_hours REAL,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Environment rankings table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS environment_rankings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date TEXT,
                    environment_data TEXT,
                    strategy_rankings TEXT,
                    top_strategy TEXT,
                    environment_score REAL,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
    
    def load_historical_performance(self):
        """Load historical strategy performance data."""
        # This would load actual historical data
        # For now, we'll use the base success rates
        pass
    
    def rank_strategies(self, market_env: MarketEnvironment) -> List[Dict]:
        """Rank all strategies for current market environment."""
        rankings = []
        
        for strategy_name, strategy_def in self.strategy_definitions.items():
            score = self._calculate_strategy_score(strategy_def, market_env)
            
            # Get historical performance adjustment
            historical_adjustment = self._get_historical_adjustment(strategy_name, market_env)
            
            # Calculate final score
            final_score = score * (1 + historical_adjustment)
            
            # Estimate current success rate and reward:risk
            success_rate = strategy_def["base_success_rate"] * (final_score / 100)
            reward_risk = strategy_def["base_reward_risk"] * (final_score / 100)
            
            rankings.append({
                "strategy": strategy_name,
                "name": strategy_def["name"],
                "description": strategy_def["description"],
                "score": final_score,
                "success_rate": min(0.95, max(0.30, success_rate)),  # Cap between 30-95%
                "reward_risk_ratio": max(1.5, reward_risk),
                "confidence": self._calculate_confidence(final_score),
                "recommendation": self._get_recommendation(final_score)
            })
        
        # Sort by score (highest first)
        rankings.sort(key=lambda x: x["score"], reverse=True)
        
        # Store rankings
        self._store_rankings(market_env, rankings)
        
        return rankings
    
    def _calculate_strategy_score(self, strategy_def: Dict, market_env: MarketEnvironment) -> float:
        """Calculate strategy score for current environment."""
        score = 50.0  # Base score
        
        # VIX alignment
        vix_min, vix_max = strategy_def["optimal_vix"]
        if vix_min <= market_env.vix_level <= vix_max:
            score += 20
        else:
            # Penalty for being outside optimal range
            distance = min(abs(market_env.vix_level - vix_min), abs(market_env.vix_level - vix_max))
            score -= min(15, distance)
        
        # Volume alignment
        volume_pref = strategy_def["optimal_volume"]
        if volume_pref == "high" and market_env.volume_regime == "high":
            score += 15
        elif volume_pref == "normal" and market_env.volume_regime == "normal":
            score += 10
        elif volume_pref == "low_to_normal" and market_env.volume_regime in ["low", "normal"]:
            score += 10
        elif volume_pref == "normal_to_high" and market_env.volume_regime in ["normal", "high"]:
            score += 12
        
        # Trend alignment
        trend_pref = strategy_def["optimal_trend"]
        if trend_pref == "strong" and market_env.trend_strength > 0.6:
            score += 15
        elif trend_pref == "weak" and market_env.trend_strength < 0.4:
            score += 15
        elif trend_pref == "any":
            score += 5
        
        # Time preference
        if market_env.time_of_day in strategy_def["time_preference"]:
            score += 10
        
        # Market environment score influence
        env_influence = (market_env.environment_score - 50) * 0.3
        score += env_influence
        
        return max(0, min(100, score))
    
    def _get_historical_adjustment(self, strategy_name: str, market_env: MarketEnvironment) -> float:
        """Get historical performance adjustment (-0.2 to +0.2)."""
        # This would analyze historical performance in similar environments
        # For now, return small random adjustment
        import random
        return random.uniform(-0.1, 0.1)
    
    def _calculate_confidence(self, score: float) -> str:
        """Calculate confidence level based on score."""
        if score >= 80:
            return "Very High"
        elif score >= 70:
            return "High"
        elif score >= 60:
            return "Medium"
        elif score >= 50:
            return "Low"
        else:
            return "Very Low"
    
    def _get_recommendation(self, score: float) -> str:
        """Get trading recommendation based on score."""
        if score >= 75:
            return "Strongly Recommended"
        elif score >= 65:
            return "Recommended"
        elif score >= 55:
            return "Consider"
        elif score >= 45:
            return "Caution"
        else:
            return "Avoid"
    
    def _store_rankings(self, market_env: MarketEnvironment, rankings: List[Dict]):
        """Store strategy rankings in database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO environment_rankings 
                    (date, environment_data, strategy_rankings, top_strategy, environment_score)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    market_env.timestamp.date().isoformat(),
                    json.dumps(market_env.__dict__, default=str),
                    json.dumps(rankings),
                    rankings[0]["strategy"] if rankings else "",
                    market_env.environment_score
                ))
                conn.commit()
        except Exception as e:
            self.logger.error(f"Error storing rankings: {e}")
    
    def get_top_strategy(self, market_env: MarketEnvironment) -> Dict:
        """Get the top-ranked strategy for current environment."""
        rankings = self.rank_strategies(market_env)
        return rankings[0] if rankings else None
    
    def get_strategy_insights(self) -> Dict:
        """Get insights about strategy performance patterns."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Get recent rankings
                cursor.execute('''
                    SELECT strategy_rankings, environment_score, date
                    FROM environment_rankings 
                    ORDER BY created_at DESC 
                    LIMIT 30
                ''')
                
                recent_data = cursor.fetchall()
                
                if not recent_data:
                    return {"message": "No historical ranking data available"}
                
                # Analyze patterns
                strategy_frequency = {}
                avg_scores = {}
                
                for rankings_json, env_score, date in recent_data:
                    rankings = json.loads(rankings_json)
                    if rankings:
                        top_strategy = rankings[0]["strategy"]
                        strategy_frequency[top_strategy] = strategy_frequency.get(top_strategy, 0) + 1
                        
                        if top_strategy not in avg_scores:
                            avg_scores[top_strategy] = []
                        avg_scores[top_strategy].append(rankings[0]["score"])
                
                # Calculate averages
                for strategy in avg_scores:
                    avg_scores[strategy] = np.mean(avg_scores[strategy])
                
                return {
                    "most_frequent_top_strategy": max(strategy_frequency, key=strategy_frequency.get) if strategy_frequency else "None",
                    "strategy_frequency": strategy_frequency,
                    "average_scores": avg_scores,
                    "total_rankings": len(recent_data)
                }
                
        except Exception as e:
            self.logger.error(f"Error getting strategy insights: {e}")
            return {"error": str(e)}


# Global instances
_market_environment = None
_strategy_ranker = None

def get_current_market_environment() -> MarketEnvironment:
    """Get current market environment."""
    global _market_environment
    # Always create fresh environment for real-time data
    _market_environment = MarketEnvironment()
    return _market_environment

def get_strategy_ranker() -> StrategyRanker:
    """Get strategy ranker instance."""
    global _strategy_ranker
    if _strategy_ranker is None:
        _strategy_ranker = StrategyRanker()
    return _strategy_ranker


if __name__ == "__main__":
    # Test the strategy environment engine
    print("🌟 Testing Strategy Environment Engine")
    print("=" * 45)
    
    # Test market environment
    env = get_current_market_environment()
    print(f"✅ Market Environment:")
    print(f"   Environment Score: {env.environment_score:.1f}/100")
    print(f"   VIX Regime: {env.vix_regime}")
    print(f"   Trend: {env.trend_direction} ({env.trend_strength:.2f})")
    print(f"   Time: {env.time_of_day}")
    print(f"   Optimal Strategies: {env.optimal_strategies}")
    
    # Test strategy ranking
    ranker = get_strategy_ranker()
    rankings = ranker.rank_strategies(env)
    
    print(f"\n✅ Strategy Rankings:")
    for i, ranking in enumerate(rankings[:3], 1):
        print(f"   {i}. {ranking['name']}: {ranking['score']:.1f} ({ranking['confidence']})")
        print(f"      Success Rate: {ranking['success_rate']:.1%}")
        print(f"      Reward:Risk: {ranking['reward_risk_ratio']:.1f}:1")
        print(f"      Recommendation: {ranking['recommendation']}")
    
    print("🌟 Strategy environment engine ready!")
