# TotalRecall Trading System - Git Ignore File

# API Keys and Sensitive Configuration
config/config.json
config/api_keys.json
.env
*.env
api_keys.txt
credentials.json

# Trading Data and Logs
logs/
*.log
trading_data/
backtest_results/
performance_data/
trade_history/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Trading Platform Specific
alpaca_credentials.json
fmp_api_key.txt
trading_session_*.json
portfolio_state_*.json
active_positions.json
order_history.json

# Temporary Files
temp/
tmp/
*.tmp
*.temp
*.bak
*.backup

# Database Files
*.db
*.sqlite
*.sqlite3

# Cache Files
cache/
.cache/
*.cache

# Model Files (if using ML models)
models/
*.pkl
*.joblib
*.h5
*.model

# Performance Data
performance_*.csv
trades_*.csv
analytics_*.json

# User-specific files
user_config.json
personal_settings.json
my_*.py
test_*.py
debug_*.py

# Documentation builds
docs/build/
docs/_build/

# Backup files
*.orig
*.rej

# Lock files
*.lock
poetry.lock
Pipfile.lock

# Node modules (if any JS components)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Compiled files
*.com
*.class
*.dll
*.exe
*.o
*.so

# Archives
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Video and images (unless specifically needed)
*.mp4
*.avi
*.mov
*.wmv
*.flv
*.webm
*.jpg
*.jpeg
*.png
*.gif
*.bmp
*.tiff
*.svg

# Audio files
*.mp3
*.wav
*.flac
*.aac

# Large data files
*.csv
*.xlsx
*.xls
*.json
data/
datasets/

# Exclude specific large directories
historical_data/
market_data/
options_data/
earnings_data/

# But include essential config templates
!config/config_template.json
!config/requirements.txt
!docs/
!README.md
!LICENSE
