#!/usr/bin/env python3
"""Advanced Alerting System

Multi-channel alert system for critical trading events:
- Voice alerts using text-to-speech
- Email notifications
- Slack/Discord integration
- Mobile push notifications
- GUI popup alerts
- Sound alerts
"""
import smtplib
import json
import requests
import threading
import time
from datetime import datetime
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON>art
from typing import Dict, List, Optional, Callable
from pathlib import Path
import logging

try:
    import pyttsx3
    TTS_AVAILABLE = True
except ImportError:
    TTS_AVAILABLE = False

try:
    import winsound
    SOUND_AVAILABLE = True
except ImportError:
    SOUND_AVAILABLE = False


class AdvancedAlertingSystem:
    """Multi-channel alerting system for trading events."""
    
    def __init__(self, config_path: str = "data/alert_config.json"):
        self.config_path = config_path
        self.logger = logging.getLogger(__name__)
        
        # Alert queue and processing
        self.alert_queue = []
        self.processing_thread = None
        self.is_processing = False
        
        # TTS engine
        self.tts_engine = None
        if TTS_AVAILABLE:
            try:
                self.tts_engine = pyttsx3.init()
                self.tts_engine.setProperty('rate', 150)  # Speed
                self.tts_engine.setProperty('volume', 0.8)  # Volume
            except:
                self.tts_engine = None
        
        # Load configuration
        self.load_config()
        
        # Start processing thread
        self.start_processing()
    
    def load_config(self):
        """Load alerting configuration."""
        default_config = {
            "voice": {
                "enabled": True,
                "voice_id": 0,  # 0 = default, 1 = female, etc.
                "rate": 150,
                "volume": 0.8
            },
            "email": {
                "enabled": False,
                "smtp_server": "smtp.gmail.com",
                "smtp_port": 587,
                "username": "",
                "password": "",  # Use app password for Gmail
                "to_addresses": []
            },
            "slack": {
                "enabled": False,
                "webhook_url": "",
                "channel": "#trading-alerts"
            },
            "discord": {
                "enabled": False,
                "webhook_url": ""
            },
            "sound": {
                "enabled": True,
                "critical_sound": "SystemExclamation",
                "warning_sound": "SystemAsterisk",
                "info_sound": "SystemDefault"
            },
            "gui": {
                "enabled": True,
                "popup_duration": 5000  # milliseconds
            },
            "alert_levels": {
                "CRITICAL": ["voice", "email", "slack", "discord", "sound", "gui"],
                "HIGH": ["voice", "slack", "sound", "gui"],
                "MEDIUM": ["sound", "gui"],
                "LOW": ["gui"]
            }
        }
        
        try:
            if Path(self.config_path).exists():
                with open(self.config_path, 'r') as f:
                    loaded_config = json.load(f)
                    # Merge with defaults
                    self.config = {**default_config, **loaded_config}
            else:
                self.config = default_config
                self.save_config()
        except Exception as e:
            self.logger.error(f"Error loading alert config: {e}")
            self.config = default_config
    
    def save_config(self):
        """Save alerting configuration."""
        try:
            Path(self.config_path).parent.mkdir(exist_ok=True)
            with open(self.config_path, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            self.logger.error(f"Error saving alert config: {e}")
    
    def send_alert(self, alert_type: str, message: str, severity: str = "MEDIUM", data: Dict = None):
        """Send alert through configured channels."""
        alert = {
            "type": alert_type,
            "message": message,
            "severity": severity,
            "timestamp": datetime.now().isoformat(),
            "data": data or {}
        }
        
        # Add to queue for processing
        self.alert_queue.append(alert)
    
    def start_processing(self):
        """Start the alert processing thread."""
        if not self.is_processing:
            self.is_processing = True
            self.processing_thread = threading.Thread(target=self._process_alerts, daemon=True)
            self.processing_thread.start()
    
    def stop_processing(self):
        """Stop the alert processing thread."""
        self.is_processing = False
        if self.processing_thread:
            self.processing_thread.join(timeout=5)
    
    def _process_alerts(self):
        """Process alerts from the queue."""
        while self.is_processing:
            try:
                if self.alert_queue:
                    alert = self.alert_queue.pop(0)
                    self._send_alert_channels(alert)
                else:
                    time.sleep(0.1)  # Short sleep when no alerts
            except Exception as e:
                self.logger.error(f"Alert processing error: {e}")
                time.sleep(1)
    
    def _send_alert_channels(self, alert: Dict):
        """Send alert through appropriate channels based on severity."""
        severity = alert["severity"]
        channels = self.config["alert_levels"].get(severity, ["gui"])
        
        for channel in channels:
            try:
                if channel == "voice":
                    self._send_voice_alert(alert)
                elif channel == "email":
                    self._send_email_alert(alert)
                elif channel == "slack":
                    self._send_slack_alert(alert)
                elif channel == "discord":
                    self._send_discord_alert(alert)
                elif channel == "sound":
                    self._send_sound_alert(alert)
                elif channel == "gui":
                    self._send_gui_alert(alert)
            except Exception as e:
                self.logger.error(f"Error sending {channel} alert: {e}")
    
    def _send_voice_alert(self, alert: Dict):
        """Send voice alert using text-to-speech."""
        if not self.config["voice"]["enabled"] or not self.tts_engine:
            return
        
        try:
            # Create voice message
            severity = alert["severity"]
            message = alert["message"]
            
            if severity == "CRITICAL":
                voice_text = f"Critical trading alert. {message}"
            elif severity == "HIGH":
                voice_text = f"High priority alert. {message}"
            else:
                voice_text = f"Trading alert. {message}"
            
            # Speak the message
            self.tts_engine.say(voice_text)
            self.tts_engine.runAndWait()
            
        except Exception as e:
            self.logger.error(f"Voice alert error: {e}")
    
    def _send_email_alert(self, alert: Dict):
        """Send email alert."""
        if not self.config["email"]["enabled"]:
            return
        
        try:
            email_config = self.config["email"]
            
            # Create message
            msg = MIMEMultipart()
            msg['From'] = email_config["username"]
            msg['Subject'] = f"TTM Trading Alert - {alert['severity']}"
            
            # Email body
            body = f"""
TTM Trading System Alert

Severity: {alert['severity']}
Type: {alert['type']}
Time: {alert['timestamp']}

Message: {alert['message']}

Additional Data:
{json.dumps(alert['data'], indent=2)}

---
TTM Trading System
            """
            
            msg.attach(MIMEText(body, 'plain'))
            
            # Send to all configured addresses
            server = smtplib.SMTP(email_config["smtp_server"], email_config["smtp_port"])
            server.starttls()
            server.login(email_config["username"], email_config["password"])
            
            for to_address in email_config["to_addresses"]:
                msg['To'] = to_address
                server.send_message(msg)
                del msg['To']
            
            server.quit()
            
        except Exception as e:
            self.logger.error(f"Email alert error: {e}")
    
    def _send_slack_alert(self, alert: Dict):
        """Send Slack alert."""
        if not self.config["slack"]["enabled"]:
            return
        
        try:
            webhook_url = self.config["slack"]["webhook_url"]
            if not webhook_url:
                return
            
            # Create Slack message
            color = {
                "CRITICAL": "danger",
                "HIGH": "warning", 
                "MEDIUM": "good",
                "LOW": "#36a64f"
            }.get(alert["severity"], "good")
            
            payload = {
                "channel": self.config["slack"]["channel"],
                "username": "TTM Trading Bot",
                "icon_emoji": ":chart_with_upwards_trend:",
                "attachments": [{
                    "color": color,
                    "title": f"TTM Trading Alert - {alert['severity']}",
                    "text": alert["message"],
                    "fields": [
                        {
                            "title": "Type",
                            "value": alert["type"],
                            "short": True
                        },
                        {
                            "title": "Time",
                            "value": alert["timestamp"],
                            "short": True
                        }
                    ],
                    "footer": "TTM Trading System",
                    "ts": int(datetime.now().timestamp())
                }]
            }
            
            response = requests.post(webhook_url, json=payload, timeout=10)
            response.raise_for_status()
            
        except Exception as e:
            self.logger.error(f"Slack alert error: {e}")
    
    def _send_discord_alert(self, alert: Dict):
        """Send Discord alert."""
        if not self.config["discord"]["enabled"]:
            return
        
        try:
            webhook_url = self.config["discord"]["webhook_url"]
            if not webhook_url:
                return
            
            # Create Discord embed
            color = {
                "CRITICAL": 0xff0000,  # Red
                "HIGH": 0xff9900,      # Orange
                "MEDIUM": 0xffff00,    # Yellow
                "LOW": 0x00ff00        # Green
            }.get(alert["severity"], 0x00ff00)
            
            payload = {
                "username": "TTM Trading Bot",
                "embeds": [{
                    "title": f"TTM Trading Alert - {alert['severity']}",
                    "description": alert["message"],
                    "color": color,
                    "fields": [
                        {
                            "name": "Type",
                            "value": alert["type"],
                            "inline": True
                        },
                        {
                            "name": "Time", 
                            "value": alert["timestamp"],
                            "inline": True
                        }
                    ],
                    "footer": {
                        "text": "TTM Trading System"
                    },
                    "timestamp": alert["timestamp"]
                }]
            }
            
            response = requests.post(webhook_url, json=payload, timeout=10)
            response.raise_for_status()
            
        except Exception as e:
            self.logger.error(f"Discord alert error: {e}")
    
    def _send_sound_alert(self, alert: Dict):
        """Send sound alert."""
        if not self.config["sound"]["enabled"] or not SOUND_AVAILABLE:
            return
        
        try:
            severity = alert["severity"]
            sound_config = self.config["sound"]
            
            if severity == "CRITICAL":
                sound = sound_config["critical_sound"]
            elif severity in ["HIGH", "MEDIUM"]:
                sound = sound_config["warning_sound"]
            else:
                sound = sound_config["info_sound"]
            
            # Play system sound
            winsound.MessageBeep(getattr(winsound, f"MB_{sound.upper()}", winsound.MB_OK))
            
        except Exception as e:
            self.logger.error(f"Sound alert error: {e}")
    
    def _send_gui_alert(self, alert: Dict):
        """Send GUI alert (popup notification)."""
        if not self.config["gui"]["enabled"]:
            return
        
        try:
            # This would integrate with your GUI system
            # For now, we'll just log it
            severity_emoji = {
                "CRITICAL": "🚨",
                "HIGH": "⚠️",
                "MEDIUM": "📢",
                "LOW": "ℹ️"
            }.get(alert["severity"], "📢")
            
            gui_message = f"{severity_emoji} {alert['message']}"
            self.logger.info(f"GUI Alert: {gui_message}")
            
            # You could integrate with tkinter messagebox here
            # messagebox.showwarning(f"TTM Alert - {alert['severity']}", alert['message'])
            
        except Exception as e:
            self.logger.error(f"GUI alert error: {e}")


# Global alerting system instance
_alerting_system = None

def get_alerting_system() -> AdvancedAlertingSystem:
    """Get the global alerting system instance."""
    global _alerting_system
    if _alerting_system is None:
        _alerting_system = AdvancedAlertingSystem()
    return _alerting_system


# Convenience functions for common alerts
def send_critical_alert(message: str, data: Dict = None):
    """Send critical alert."""
    get_alerting_system().send_alert("CRITICAL", message, "CRITICAL", data)

def send_loss_alert(symbol: str, amount: float, percentage: float):
    """Send loss threshold alert."""
    message = f"Loss alert: {symbol} down ${abs(amount):.0f} ({percentage:.1f}%)"
    data = {"symbol": symbol, "loss_amount": amount, "loss_percentage": percentage}
    get_alerting_system().send_alert("LOSS_THRESHOLD", message, "HIGH", data)

def send_position_alert(symbol: str, action: str, quantity: int, price: float):
    """Send position update alert."""
    message = f"Position {action}: {symbol} {quantity} shares @ ${price:.2f}"
    data = {"symbol": symbol, "action": action, "quantity": quantity, "price": price}
    get_alerting_system().send_alert("POSITION_UPDATE", message, "MEDIUM", data)


if __name__ == "__main__":
    # Test the alerting system
    alerting = AdvancedAlertingSystem()
    
    print("🔔 Testing Advanced Alerting System")
    print("=" * 45)
    
    # Test different severity levels
    alerting.send_alert("TEST", "Low priority test message", "LOW")
    time.sleep(1)
    
    alerting.send_alert("TEST", "Medium priority test message", "MEDIUM")
    time.sleep(1)
    
    alerting.send_alert("TEST", "High priority test message", "HIGH")
    time.sleep(1)
    
    # Test convenience functions
    send_loss_alert("AAPL", -150.0, -2.5)
    send_position_alert("NVDA", "opened", 100, 450.0)
    
    print("✅ Alert tests sent")
    time.sleep(3)  # Let alerts process
    
    alerting.stop_processing()
    print("🔔 Alerting system test complete!")
