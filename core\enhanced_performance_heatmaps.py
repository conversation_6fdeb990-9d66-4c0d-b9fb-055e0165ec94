#!/usr/bin/env python3
"""Enhanced Performance Heatmaps

Advanced performance visualization system with:
- Interactive heatmaps for strategy performance
- Time-based performance analysis
- Win/loss streak tracking
- Risk-adjusted performance metrics
- Sector performance heatmaps
- Real-time performance dashboards
"""
import json
import sqlite3
import numpy as np
import pandas as pd
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
import logging

try:
    import matplotlib.pyplot as plt
    import seaborn as sns
    PLOTTING_AVAILABLE = True
except ImportError:
    PLOTTING_AVAILABLE = False

try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False
    # Create mock go object for fallback
    class MockGO:
        class Figure:
            def to_json(self): return "{}"
        class Heatmap:
            def __init__(self, **kwargs): pass
    go = MockGO()


class PerformanceHeatmapGenerator:
    """Generates advanced performance heatmaps and visualizations."""
    
    def __init__(self, db_path: str = "data/performance_heatmaps.db"):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        
        # Color schemes for different metrics
        self.color_schemes = {
            "pnl": ["#d73027", "#f46d43", "#fdae61", "#fee08b", "#e6f598", "#abdda4", "#66c2a5", "#3288bd"],
            "win_rate": ["#d73027", "#fc8d59", "#fee08b", "#e6f598", "#99d594", "#3288bd"],
            "sharpe": ["#d73027", "#f46d43", "#fdae61", "#ffffbf", "#e6f598", "#abdda4", "#66c2a5", "#3288bd"],
            "drawdown": ["#3288bd", "#66c2a5", "#abdda4", "#e6f598", "#fee08b", "#fdae61", "#f46d43", "#d73027"]
        }
        
        # Initialize
        self.init_database()
    
    def init_database(self):
        """Initialize heatmap database."""
        Path(self.db_path).parent.mkdir(exist_ok=True)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Performance metrics table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS performance_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date TEXT,
                    strategy TEXT,
                    symbol TEXT,
                    timeframe TEXT,
                    pnl REAL,
                    win_rate REAL,
                    sharpe_ratio REAL,
                    max_drawdown REAL,
                    total_trades INTEGER,
                    avg_hold_time REAL,
                    risk_adjusted_return REAL,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Streak tracking table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS streak_tracking (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date TEXT,
                    strategy TEXT,
                    current_streak INTEGER,
                    streak_type TEXT,
                    max_win_streak INTEGER,
                    max_loss_streak INTEGER,
                    streak_pnl REAL,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Sector performance table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sector_performance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date TEXT,
                    sector TEXT,
                    strategy TEXT,
                    pnl REAL,
                    win_rate REAL,
                    trade_count INTEGER,
                    avg_return REAL,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
    
    def generate_strategy_performance_heatmap(self, days: int = 30) -> Dict:
        """Generate strategy performance heatmap."""
        try:
            # Get performance data
            performance_data = self._get_strategy_performance_data(days)
            
            if not performance_data:
                return {"error": "No performance data available"}
            
            # Create DataFrame
            df = pd.DataFrame(performance_data)
            
            # Pivot for heatmap
            heatmap_data = df.pivot_table(
                index='strategy',
                columns='date',
                values='pnl',
                aggfunc='sum',
                fill_value=0
            )
            
            # Generate heatmap
            if PLOTLY_AVAILABLE:
                fig = self._create_plotly_heatmap(heatmap_data, "Strategy Performance (P&L)", "pnl")
                return {
                    "type": "plotly",
                    "figure": fig.to_json(),
                    "data": heatmap_data.to_dict(),
                    "summary": self._calculate_heatmap_summary(heatmap_data)
                }
            else:
                # Text-based heatmap
                return {
                    "type": "text",
                    "data": heatmap_data.to_dict(),
                    "summary": self._calculate_heatmap_summary(heatmap_data),
                    "text_visualization": self._create_text_heatmap(heatmap_data)
                }
                
        except Exception as e:
            self.logger.error(f"Error generating strategy heatmap: {e}")
            return {"error": str(e)}
    
    def generate_time_performance_heatmap(self, strategy: str = "all") -> Dict:
        """Generate time-based performance heatmap (hour vs day of week)."""
        try:
            # Get time-based performance data
            time_data = self._get_time_performance_data(strategy)
            
            if not time_data:
                return {"error": "No time performance data available"}
            
            # Create time matrix (24 hours x 7 days)
            time_matrix = np.zeros((24, 7))
            day_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
            
            for data in time_data:
                hour = data['hour']
                day = data['day_of_week']
                pnl = data['pnl']
                time_matrix[hour][day] += pnl
            
            if PLOTLY_AVAILABLE:
                fig = go.Figure(data=go.Heatmap(
                    z=time_matrix,
                    x=day_names,
                    y=list(range(24)),
                    colorscale='RdYlBu_r',
                    hoverongaps=False
                ))
                
                fig.update_layout(
                    title="Performance by Time of Day and Day of Week",
                    xaxis_title="Day of Week",
                    yaxis_title="Hour of Day",
                    height=600
                )
                
                return {
                    "type": "plotly",
                    "figure": fig.to_json(),
                    "data": time_matrix.tolist(),
                    "best_times": self._find_best_trading_times(time_matrix, day_names)
                }
            else:
                return {
                    "type": "text",
                    "data": time_matrix.tolist(),
                    "best_times": self._find_best_trading_times(time_matrix, day_names),
                    "text_visualization": self._create_time_text_heatmap(time_matrix, day_names)
                }
                
        except Exception as e:
            self.logger.error(f"Error generating time heatmap: {e}")
            return {"error": str(e)}
    
    def generate_streak_analysis(self, strategy: str = "all") -> Dict:
        """Generate win/loss streak analysis."""
        try:
            streak_data = self._get_streak_data(strategy)
            
            if not streak_data:
                return {"error": "No streak data available"}
            
            # Analyze streaks
            analysis = {
                "current_streaks": {},
                "max_win_streaks": {},
                "max_loss_streaks": {},
                "streak_distribution": {},
                "streak_pnl_impact": {}
            }
            
            for data in streak_data:
                strategy_name = data['strategy']
                
                analysis["current_streaks"][strategy_name] = {
                    "streak": data['current_streak'],
                    "type": data['streak_type'],
                    "pnl": data['streak_pnl']
                }
                
                analysis["max_win_streaks"][strategy_name] = data['max_win_streak']
                analysis["max_loss_streaks"][strategy_name] = data['max_loss_streak']
            
            # Generate streak visualization
            if PLOTLY_AVAILABLE:
                fig = self._create_streak_visualization(analysis)
                return {
                    "type": "plotly",
                    "figure": fig.to_json(),
                    "analysis": analysis,
                    "insights": self._generate_streak_insights(analysis)
                }
            else:
                return {
                    "type": "text",
                    "analysis": analysis,
                    "insights": self._generate_streak_insights(analysis),
                    "text_visualization": self._create_streak_text_analysis(analysis)
                }
                
        except Exception as e:
            self.logger.error(f"Error generating streak analysis: {e}")
            return {"error": str(e)}
    
    def generate_sector_heatmap(self, days: int = 30) -> Dict:
        """Generate sector performance heatmap."""
        try:
            sector_data = self._get_sector_performance_data(days)
            
            if not sector_data:
                return {"error": "No sector performance data available"}
            
            # Create sector matrix
            df = pd.DataFrame(sector_data)
            sector_matrix = df.pivot_table(
                index='sector',
                columns='strategy',
                values='pnl',
                aggfunc='sum',
                fill_value=0
            )
            
            if PLOTLY_AVAILABLE:
                fig = self._create_plotly_heatmap(sector_matrix, "Sector Performance by Strategy", "pnl")
                return {
                    "type": "plotly",
                    "figure": fig.to_json(),
                    "data": sector_matrix.to_dict(),
                    "top_sectors": self._find_top_sectors(sector_matrix)
                }
            else:
                return {
                    "type": "text",
                    "data": sector_matrix.to_dict(),
                    "top_sectors": self._find_top_sectors(sector_matrix),
                    "text_visualization": self._create_text_heatmap(sector_matrix)
                }
                
        except Exception as e:
            self.logger.error(f"Error generating sector heatmap: {e}")
            return {"error": str(e)}
    
    def generate_risk_adjusted_heatmap(self, metric: str = "sharpe") -> Dict:
        """Generate risk-adjusted performance heatmap."""
        try:
            risk_data = self._get_risk_adjusted_data(metric)
            
            if not risk_data:
                return {"error": f"No {metric} data available"}
            
            # Create risk-adjusted matrix
            df = pd.DataFrame(risk_data)
            risk_matrix = df.pivot_table(
                index='strategy',
                columns='timeframe',
                values=metric,
                aggfunc='mean',
                fill_value=0
            )
            
            if PLOTLY_AVAILABLE:
                fig = self._create_plotly_heatmap(
                    risk_matrix, 
                    f"Risk-Adjusted Performance ({metric.title()})", 
                    metric
                )
                return {
                    "type": "plotly",
                    "figure": fig.to_json(),
                    "data": risk_matrix.to_dict(),
                    "best_performers": self._find_best_risk_adjusted(risk_matrix, metric)
                }
            else:
                return {
                    "type": "text",
                    "data": risk_matrix.to_dict(),
                    "best_performers": self._find_best_risk_adjusted(risk_matrix, metric),
                    "text_visualization": self._create_text_heatmap(risk_matrix)
                }
                
        except Exception as e:
            self.logger.error(f"Error generating risk-adjusted heatmap: {e}")
            return {"error": str(e)}
    
    def _get_strategy_performance_data(self, days: int) -> List[Dict]:
        """Get strategy performance data from database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                start_date = (datetime.now() - timedelta(days=days)).date().isoformat()
                
                cursor.execute('''
                    SELECT date, strategy, SUM(pnl) as total_pnl, 
                           AVG(win_rate) as avg_win_rate, COUNT(*) as trade_count
                    FROM performance_metrics 
                    WHERE date >= ?
                    GROUP BY date, strategy
                    ORDER BY date, strategy
                ''', (start_date,))
                
                columns = ['date', 'strategy', 'pnl', 'win_rate', 'trade_count']
                return [dict(zip(columns, row)) for row in cursor.fetchall()]
                
        except Exception as e:
            self.logger.error(f"Error getting strategy performance data: {e}")
            return []
    
    def _get_time_performance_data(self, strategy: str) -> List[Dict]:
        """Get time-based performance data."""
        # Mock data for demonstration
        import random
        time_data = []
        
        for hour in range(24):
            for day in range(7):
                if 9 <= hour <= 16:  # Market hours
                    pnl = random.uniform(-50, 100)
                else:
                    pnl = random.uniform(-10, 20)
                
                time_data.append({
                    'hour': hour,
                    'day_of_week': day,
                    'pnl': pnl
                })
        
        return time_data
    
    def _get_streak_data(self, strategy: str) -> List[Dict]:
        """Get streak tracking data."""
        # Mock data for demonstration
        import random
        strategies = ['ttm_squeeze', 'momentum_breakout', 'mean_reversion']
        
        return [
            {
                'strategy': strat,
                'current_streak': random.randint(-5, 8),
                'streak_type': random.choice(['win', 'loss']),
                'max_win_streak': random.randint(3, 12),
                'max_loss_streak': random.randint(2, 6),
                'streak_pnl': random.uniform(-200, 500)
            }
            for strat in strategies
        ]
    
    def _get_sector_performance_data(self, days: int) -> List[Dict]:
        """Get sector performance data."""
        # Mock data for demonstration
        import random
        sectors = ['Technology', 'Healthcare', 'Finance', 'Energy', 'Consumer', 'Industrial']
        strategies = ['ttm_squeeze', 'momentum_breakout', 'mean_reversion']
        
        data = []
        for sector in sectors:
            for strategy in strategies:
                data.append({
                    'sector': sector,
                    'strategy': strategy,
                    'pnl': random.uniform(-100, 300),
                    'win_rate': random.uniform(0.4, 0.8),
                    'trade_count': random.randint(5, 25)
                })
        
        return data
    
    def _get_risk_adjusted_data(self, metric: str) -> List[Dict]:
        """Get risk-adjusted performance data."""
        # Mock data for demonstration
        import random
        strategies = ['ttm_squeeze', 'momentum_breakout', 'mean_reversion', 'trend_following']
        timeframes = ['1m', '5m', '15m', '1h', '1d']
        
        data = []
        for strategy in strategies:
            for timeframe in timeframes:
                if metric == 'sharpe':
                    value = random.uniform(0.5, 2.5)
                elif metric == 'win_rate':
                    value = random.uniform(0.45, 0.75)
                else:
                    value = random.uniform(-0.15, 0.25)
                
                data.append({
                    'strategy': strategy,
                    'timeframe': timeframe,
                    metric: value
                })
        
        return data
    
    def _create_plotly_heatmap(self, data: pd.DataFrame, title: str, metric: str):
        """Create interactive Plotly heatmap."""
        fig = go.Figure(data=go.Heatmap(
            z=data.values,
            x=data.columns,
            y=data.index,
            colorscale='RdYlBu_r' if metric == 'pnl' else 'Viridis',
            hoverongaps=False,
            text=data.values,
            texttemplate="%{text:.2f}",
            textfont={"size": 10}
        ))
        
        fig.update_layout(
            title=title,
            xaxis_title="Date" if 'date' in str(data.columns) else "Category",
            yaxis_title="Strategy" if 'strategy' in str(data.index) else "Category",
            height=500,
            width=800
        )
        
        return fig
    
    def _create_text_heatmap(self, data: pd.DataFrame) -> str:
        """Create text-based heatmap visualization."""
        text_viz = f"\n📊 **PERFORMANCE HEATMAP**\n\n"
        
        # Create simple text representation
        for index in data.index:
            text_viz += f"**{index}:**\n"
            row_data = data.loc[index]
            
            for col, value in row_data.items():
                if value > 0:
                    emoji = "🟢" if value > 50 else "🟡"
                else:
                    emoji = "🔴" if value < -50 else "🟠"
                
                text_viz += f"  {col}: {emoji} ${value:.2f}\n"
            text_viz += "\n"
        
        return text_viz
    
    def _calculate_heatmap_summary(self, data: pd.DataFrame) -> Dict:
        """Calculate summary statistics for heatmap."""
        return {
            "total_pnl": float(data.sum().sum()),
            "best_strategy": data.sum(axis=1).idxmax(),
            "worst_strategy": data.sum(axis=1).idxmin(),
            "best_day": data.sum(axis=0).idxmax() if hasattr(data.sum(axis=0), 'idxmax') else "N/A",
            "avg_daily_pnl": float(data.sum(axis=0).mean()),
            "win_rate": float((data > 0).sum().sum() / data.size)
        }
    
    def _find_best_trading_times(self, time_matrix: np.ndarray, day_names: List[str]) -> Dict:
        """Find best trading times from time matrix."""
        best_hour = np.unravel_index(time_matrix.argmax(), time_matrix.shape)[0]
        best_day = np.unravel_index(time_matrix.argmax(), time_matrix.shape)[1]
        
        return {
            "best_hour": best_hour,
            "best_day": day_names[best_day],
            "best_pnl": float(time_matrix[best_hour, best_day]),
            "market_hours_avg": float(time_matrix[9:17, :5].mean()),  # 9-5 weekdays
            "after_hours_avg": float(np.concatenate([time_matrix[:9, :], time_matrix[17:, :]]).mean())
        }
    
    def _generate_streak_insights(self, analysis: Dict) -> List[str]:
        """Generate insights from streak analysis."""
        insights = []
        
        # Current streak insights
        for strategy, streak_info in analysis["current_streaks"].items():
            if streak_info["streak"] > 3:
                insights.append(f"🔥 {strategy} is on a {streak_info['streak']}-trade {streak_info['type']} streak")
            elif streak_info["streak"] < -3:
                insights.append(f"❄️ {strategy} is on a {abs(streak_info['streak'])}-trade loss streak")
        
        # Max streak insights
        best_win_streak = max(analysis["max_win_streaks"].items(), key=lambda x: x[1])
        insights.append(f"🏆 Best win streak: {best_win_streak[0]} with {best_win_streak[1]} consecutive wins")
        
        return insights
    
    def _create_streak_text_analysis(self, analysis: Dict) -> str:
        """Create text-based streak analysis."""
        text = "\n🎯 **STREAK ANALYSIS**\n\n"
        
        text += "**Current Streaks:**\n"
        for strategy, streak_info in analysis["current_streaks"].items():
            emoji = "🔥" if streak_info["type"] == "win" else "❄️"
            text += f"  {emoji} {strategy}: {abs(streak_info['streak'])} {streak_info['type']} streak (${streak_info['pnl']:.2f})\n"
        
        text += "\n**Record Streaks:**\n"
        for strategy, max_win in analysis["max_win_streaks"].items():
            max_loss = analysis["max_loss_streaks"][strategy]
            text += f"  📊 {strategy}: {max_win} max wins, {max_loss} max losses\n"
        
        return text
    
    def _find_top_sectors(self, sector_matrix: pd.DataFrame) -> Dict:
        """Find top performing sectors."""
        sector_totals = sector_matrix.sum(axis=1).sort_values(ascending=False)
        
        return {
            "best_sector": sector_totals.index[0],
            "best_sector_pnl": float(sector_totals.iloc[0]),
            "worst_sector": sector_totals.index[-1],
            "worst_sector_pnl": float(sector_totals.iloc[-1]),
            "sector_rankings": sector_totals.to_dict()
        }
    
    def _find_best_risk_adjusted(self, risk_matrix: pd.DataFrame, metric: str) -> Dict:
        """Find best risk-adjusted performers."""
        strategy_avgs = risk_matrix.mean(axis=1).sort_values(ascending=False)
        
        return {
            "best_strategy": strategy_avgs.index[0],
            "best_value": float(strategy_avgs.iloc[0]),
            "worst_strategy": strategy_avgs.index[-1],
            "worst_value": float(strategy_avgs.iloc[-1]),
            "rankings": strategy_avgs.to_dict()
        }


# Global heatmap generator instance
_heatmap_generator = None

def get_heatmap_generator() -> PerformanceHeatmapGenerator:
    """Get the global heatmap generator instance."""
    global _heatmap_generator
    if _heatmap_generator is None:
        _heatmap_generator = PerformanceHeatmapGenerator()
    return _heatmap_generator


if __name__ == "__main__":
    # Test the performance heatmap generator
    generator = PerformanceHeatmapGenerator()
    
    print("📊 Testing Enhanced Performance Heatmaps")
    print("=" * 50)
    
    # Test strategy performance heatmap
    strategy_heatmap = generator.generate_strategy_performance_heatmap(30)
    print(f"✅ Strategy heatmap: {strategy_heatmap['type']} format")
    
    # Test time performance heatmap
    time_heatmap = generator.generate_time_performance_heatmap()
    print(f"✅ Time heatmap: {time_heatmap['type']} format")
    
    # Test streak analysis
    streak_analysis = generator.generate_streak_analysis()
    print(f"✅ Streak analysis: {len(streak_analysis.get('insights', []))} insights")
    
    # Test sector heatmap
    sector_heatmap = generator.generate_sector_heatmap()
    print(f"✅ Sector heatmap: {sector_heatmap['type']} format")
    
    print("📊 Enhanced performance heatmaps ready!")
