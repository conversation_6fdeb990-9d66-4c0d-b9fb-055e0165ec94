#!/usr/bin/env python3
"""Free Options Flow Analyzer

Tracks unusual options activity using free data sources.
Detects smart money moves and options volume spikes.
"""
import requests
import yfinance as yf
from typing import Dict, List, Optional
from datetime import datetime, timedelta
import statistics


class FreeOptionsFlowAnalyzer:
    """Free options flow analysis using Yahoo Finance and other free sources."""
    
    def __init__(self):
        self.fmp_key = None
        try:
            from config import get_api_key
            self.fmp_key = get_api_key('FMP_API_KEY')
        except:
            pass
    
    def analyze_options_activity(self, symbol: str) -> Dict:
        """Analyze options activity for unusual volume and smart money moves."""
        try:
            # Get stock data
            ticker = yf.Ticker(symbol)
            
            # Get options chain
            options_dates = ticker.options
            if not options_dates:
                return self._empty_result(f"No options data available for {symbol}")
            
            # Use nearest expiration
            nearest_exp = options_dates[0]
            options_chain = ticker.option_chain(nearest_exp)
            
            calls = options_chain.calls
            puts = options_chain.puts
            
            # Analyze volume patterns
            analysis = self._analyze_volume_patterns(calls, puts, symbol)
            
            # Get stock price context
            stock_info = ticker.info
            current_price = stock_info.get('currentPrice', 0)
            
            # Calculate put/call ratio
            total_call_volume = calls['volume'].sum() if not calls.empty else 0
            total_put_volume = puts['volume'].sum() if not puts.empty else 0
            
            put_call_ratio = total_put_volume / total_call_volume if total_call_volume > 0 else 0
            
            # Detect unusual activity
            unusual_activity = self._detect_unusual_activity(calls, puts, current_price)
            
            # Smart money analysis
            smart_money_signal = self._analyze_smart_money(calls, puts, current_price)
            
            return {
                'symbol': symbol,
                'current_price': current_price,
                'expiration_date': nearest_exp,
                'total_call_volume': int(total_call_volume),
                'total_put_volume': int(total_put_volume),
                'put_call_ratio': round(put_call_ratio, 2),
                'unusual_activity': unusual_activity,
                'smart_money_signal': smart_money_signal,
                'analysis': analysis,
                'ttm_impact': self._assess_ttm_impact(put_call_ratio, unusual_activity, smart_money_signal)
            }
            
        except Exception as e:
            return self._empty_result(f"Error analyzing options: {str(e)}")
    
    def _analyze_volume_patterns(self, calls, puts, symbol) -> str:
        """Analyze volume patterns in options data."""
        if calls.empty and puts.empty:
            return "No options volume data available"
        
        # Calculate volume statistics
        call_volumes = calls['volume'].dropna() if not calls.empty else []
        put_volumes = puts['volume'].dropna() if not puts.empty else []
        
        if len(call_volumes) == 0 and len(put_volumes) == 0:
            return "No volume data in options chain"
        
        total_volume = sum(call_volumes) + sum(put_volumes)
        
        if total_volume < 100:
            return "Low options activity - minimal institutional interest"
        elif total_volume < 1000:
            return "Moderate options activity - some institutional interest"
        else:
            return "High options activity - significant institutional interest"
    
    def _detect_unusual_activity(self, calls, puts, current_price) -> List[Dict]:
        """Detect unusual options activity."""
        unusual = []
        
        # Check calls for unusual volume
        if not calls.empty:
            high_volume_calls = calls[calls['volume'] > calls['volume'].quantile(0.9)]
            
            for _, option in high_volume_calls.iterrows():
                if option['volume'] > 500:  # Significant volume threshold
                    distance_from_money = abs(option['strike'] - current_price) / current_price
                    
                    unusual.append({
                        'type': 'CALL',
                        'strike': option['strike'],
                        'volume': int(option['volume']),
                        'open_interest': int(option.get('openInterest', 0)),
                        'distance_from_money': round(distance_from_money * 100, 1),
                        'significance': 'High' if option['volume'] > 1000 else 'Moderate'
                    })
        
        # Check puts for unusual volume
        if not puts.empty:
            high_volume_puts = puts[puts['volume'] > puts['volume'].quantile(0.9)]
            
            for _, option in high_volume_puts.iterrows():
                if option['volume'] > 500:
                    distance_from_money = abs(option['strike'] - current_price) / current_price
                    
                    unusual.append({
                        'type': 'PUT',
                        'strike': option['strike'],
                        'volume': int(option['volume']),
                        'open_interest': int(option.get('openInterest', 0)),
                        'distance_from_money': round(distance_from_money * 100, 1),
                        'significance': 'High' if option['volume'] > 1000 else 'Moderate'
                    })
        
        return sorted(unusual, key=lambda x: x['volume'], reverse=True)[:5]
    
    def _analyze_smart_money(self, calls, puts, current_price) -> Dict:
        """Analyze smart money positioning."""
        # Look for large trades near the money
        smart_money = {
            'direction': 'Neutral',
            'confidence': 0,
            'reasoning': ''
        }
        
        if calls.empty and puts.empty:
            return smart_money
        
        # Find near-the-money options (within 5%)
        if not calls.empty:
            near_money_calls = calls[abs(calls['strike'] - current_price) / current_price < 0.05]
            call_volume = near_money_calls['volume'].sum() if not near_money_calls.empty else 0
        else:
            call_volume = 0
        
        if not puts.empty:
            near_money_puts = puts[abs(puts['strike'] - current_price) / current_price < 0.05]
            put_volume = near_money_puts['volume'].sum() if not near_money_puts.empty else 0
        else:
            put_volume = 0
        
        total_near_money = call_volume + put_volume
        
        if total_near_money < 100:
            smart_money['reasoning'] = "Insufficient near-the-money volume for analysis"
            return smart_money
        
        call_percentage = call_volume / total_near_money if total_near_money > 0 else 0
        
        if call_percentage > 0.7:
            smart_money['direction'] = 'Bullish'
            smart_money['confidence'] = min(90, int(call_percentage * 100))
            smart_money['reasoning'] = f"Heavy call buying ({call_percentage:.1%}) suggests bullish positioning"
        elif call_percentage < 0.3:
            smart_money['direction'] = 'Bearish'
            smart_money['confidence'] = min(90, int((1 - call_percentage) * 100))
            smart_money['reasoning'] = f"Heavy put buying ({1-call_percentage:.1%}) suggests bearish positioning"
        else:
            smart_money['direction'] = 'Neutral'
            smart_money['confidence'] = 50
            smart_money['reasoning'] = "Balanced call/put activity - no clear directional bias"
        
        return smart_money
    
    def _assess_ttm_impact(self, put_call_ratio, unusual_activity, smart_money) -> str:
        """Assess how options activity impacts TTM trading decisions."""
        if not unusual_activity:
            return "No significant options activity - TTM setup stands alone"
        
        if smart_money['direction'] == 'Bullish' and smart_money['confidence'] > 70:
            return "🚀 BULLISH options flow confirms TTM setup - high conviction trade"
        elif smart_money['direction'] == 'Bearish' and smart_money['confidence'] > 70:
            return "⚠️ BEARISH options flow contradicts bullish TTM - proceed with caution"
        elif put_call_ratio > 1.5:
            return "📉 High put/call ratio suggests bearish sentiment - monitor TTM closely"
        elif put_call_ratio < 0.5:
            return "📈 Low put/call ratio suggests bullish sentiment - supports TTM setup"
        else:
            return "😐 Neutral options flow - TTM technical analysis is primary signal"
    
    def _empty_result(self, error: str) -> Dict:
        """Return empty result with error."""
        return {
            'symbol': '',
            'current_price': 0,
            'expiration_date': '',
            'total_call_volume': 0,
            'total_put_volume': 0,
            'put_call_ratio': 0,
            'unusual_activity': [],
            'smart_money_signal': {'direction': 'Unknown', 'confidence': 0, 'reasoning': error},
            'analysis': error,
            'ttm_impact': error
        }


def get_options_flow_for_ttm(symbol: str) -> str:
    """Get options flow analysis for TTM trading decisions."""
    analyzer = FreeOptionsFlowAnalyzer()
    result = analyzer.analyze_options_activity(symbol)
    
    if not result['symbol']:
        return f"📊 **Options Flow Analysis for ${symbol}:**\n• {result['analysis']}"
    
    # Format the response
    direction_emoji = "🚀" if result['smart_money_signal']['direction'] == 'Bullish' else "📉" if result['smart_money_signal']['direction'] == 'Bearish' else "😐"
    
    unusual_summary = ""
    if result['unusual_activity']:
        top_unusual = result['unusual_activity'][0]
        unusual_summary = f"\n• 🔥 Unusual Activity: {top_unusual['type']} ${top_unusual['strike']} ({top_unusual['volume']:,} volume)"
    
    return f"""📊 **Options Flow Analysis for ${symbol}:**
• 💰 Current Price: ${result['current_price']:.2f}
• 📞 Call Volume: {result['total_call_volume']:,}
• 📉 Put Volume: {result['total_put_volume']:,}
• ⚖️ Put/Call Ratio: {result['put_call_ratio']}
• {direction_emoji} Smart Money: {result['smart_money_signal']['direction']} ({result['smart_money_signal']['confidence']}% confidence)
• 📈 Activity Level: {result['analysis']}{unusual_summary}

**TTM Impact:** {result['ttm_impact']}

**Reasoning:** {result['smart_money_signal']['reasoning']}"""


if __name__ == "__main__":
    # Test the options flow analyzer
    analyzer = FreeOptionsFlowAnalyzer()
    
    print("🧪 Testing Free Options Flow Analyzer")
    print("=" * 50)
    
    # Test with popular symbols
    test_symbols = ['AAPL', 'NVDA', 'TSLA']
    
    for symbol in test_symbols:
        print(f"\n📊 Testing {symbol}:")
        result = analyzer.analyze_options_activity(symbol)
        print(f"Put/Call Ratio: {result['put_call_ratio']}")
        print(f"Smart Money: {result['smart_money_signal']['direction']}")
        print(f"Unusual Activity: {len(result['unusual_activity'])} items")
        print(f"TTM Impact: {result['ttm_impact'][:50]}...")
