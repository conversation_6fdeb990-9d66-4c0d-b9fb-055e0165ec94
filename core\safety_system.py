#!/usr/bin/env python3
"""Enhanced Safety System

Critical safety features for live trading:
- Daily loss limits with lockout
- Position size validation
- Risk circuit breakers
- Emergency stop mechanisms
- Real-time risk monitoring
- Alert system integration
"""
import json
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable
from pathlib import Path
import logging


class TradingSafetySystem:
    """Comprehensive trading safety and risk management system."""
    
    def __init__(self, config_path: str = "data/safety_config.json"):
        self.config_path = config_path
        self.db_path = "data/safety_tracking.db"
        self.logger = logging.getLogger(__name__)
        
        # Safety state
        self.is_locked_out = False
        self.lockout_reason = ""
        self.daily_loss = 0.0
        self.daily_trades = 0
        self.active_positions = {}
        
        # Alert callbacks
        self.alert_callbacks = []
        
        # Initialize
        self.load_config()
        self.init_database()
        self.check_daily_status()
    
    def load_config(self):
        """Load safety configuration."""
        default_config = {
            "daily_loss_limit": 500.0,
            "max_daily_trades": 10,
            "max_position_size": 5000.0,
            "max_portfolio_risk": 10.0,  # % of account
            "max_single_position_risk": 2.0,  # % of account
            "max_concurrent_positions": 5,
            "account_size": 25000.0,
            "red_day_lockout": True,
            "consecutive_loss_limit": 3,
            "drawdown_limit": 1000.0,  # Max drawdown from peak
            "alerts": {
                "email_enabled": False,
                "voice_enabled": False,
                "slack_enabled": False,
                "loss_threshold_pct": 50.0  # Alert at 50% of daily limit
            }
        }
        
        try:
            if Path(self.config_path).exists():
                with open(self.config_path, 'r') as f:
                    loaded_config = json.load(f)
                    # Merge with defaults
                    self.config = {**default_config, **loaded_config}
            else:
                self.config = default_config
                self.save_config()
        except Exception as e:
            self.logger.error(f"Error loading safety config: {e}")
            self.config = default_config
    
    def save_config(self):
        """Save safety configuration."""
        try:
            Path(self.config_path).parent.mkdir(exist_ok=True)
            with open(self.config_path, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            self.logger.error(f"Error saving safety config: {e}")
    
    def init_database(self):
        """Initialize safety tracking database."""
        Path(self.db_path).parent.mkdir(exist_ok=True)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Daily safety metrics
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS daily_safety (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date TEXT UNIQUE,
                    daily_loss REAL DEFAULT 0,
                    daily_trades INTEGER DEFAULT 0,
                    max_drawdown REAL DEFAULT 0,
                    lockout_triggered BOOLEAN DEFAULT 0,
                    lockout_reason TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Risk events log
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS risk_events (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT,
                    event_type TEXT,
                    severity TEXT,
                    description TEXT,
                    action_taken TEXT,
                    trade_id TEXT,
                    symbol TEXT,
                    amount REAL
                )
            ''')
            
            conn.commit()
    
    def check_daily_status(self):
        """Check current daily status and load existing metrics."""
        today = datetime.now().date().isoformat()
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT daily_loss, daily_trades, lockout_triggered, lockout_reason
                FROM daily_safety WHERE date = ?
            ''', (today,))
            
            result = cursor.fetchone()
            if result:
                self.daily_loss, self.daily_trades, lockout_triggered, self.lockout_reason = result
                self.is_locked_out = bool(lockout_triggered)
            else:
                # Create today's record
                cursor.execute('''
                    INSERT INTO daily_safety (date) VALUES (?)
                ''', (today,))
                conn.commit()
    
    def validate_trade(self, trade_request: Dict) -> Dict:
        """Validate a trade request against safety rules."""
        validation_result = {
            "approved": False,
            "warnings": [],
            "errors": [],
            "adjusted_size": None
        }
        
        # Check if locked out
        if self.is_locked_out:
            validation_result["errors"].append(f"Trading locked out: {self.lockout_reason}")
            return validation_result
        
        symbol = trade_request.get("symbol", "")
        quantity = trade_request.get("quantity", 0)
        price = trade_request.get("price", 0)
        position_value = quantity * price
        
        # Check daily loss limit
        if self.daily_loss <= -self.config["daily_loss_limit"]:
            self._trigger_lockout("Daily loss limit exceeded")
            validation_result["errors"].append("Daily loss limit exceeded")
            return validation_result
        
        # Check daily trade limit
        if self.daily_trades >= self.config["max_daily_trades"]:
            validation_result["errors"].append("Daily trade limit exceeded")
            return validation_result
        
        # Check position size
        if position_value > self.config["max_position_size"]:
            # Calculate adjusted size
            max_quantity = int(self.config["max_position_size"] / price)
            validation_result["adjusted_size"] = max_quantity
            validation_result["warnings"].append(f"Position size reduced to ${self.config['max_position_size']:,.0f}")
        
        # Check portfolio risk
        account_size = self.config["account_size"]
        position_risk_pct = (position_value / account_size) * 100
        
        if position_risk_pct > self.config["max_single_position_risk"]:
            # Calculate max allowed size
            max_risk_value = account_size * (self.config["max_single_position_risk"] / 100)
            max_quantity = int(max_risk_value / price)
            validation_result["adjusted_size"] = max_quantity
            validation_result["warnings"].append(f"Position risk reduced to {self.config['max_single_position_risk']}%")
        
        # Check concurrent positions
        if len(self.active_positions) >= self.config["max_concurrent_positions"]:
            validation_result["errors"].append("Maximum concurrent positions reached")
            return validation_result
        
        # Check if approaching loss threshold for alerts
        loss_threshold = self.config["daily_loss_limit"] * (self.config["alerts"]["loss_threshold_pct"] / 100)
        if self.daily_loss <= -loss_threshold:
            self._send_risk_alert("LOSS_THRESHOLD", f"Daily loss at {abs(self.daily_loss):.0f} (${loss_threshold:.0f} threshold)")
        
        # If we get here, trade is approved (possibly with adjustments)
        validation_result["approved"] = True
        return validation_result
    
    def record_trade_execution(self, trade_data: Dict):
        """Record a trade execution for safety tracking."""
        try:
            trade_id = trade_data.get("trade_id", "")
            symbol = trade_data.get("symbol", "")
            pnl = trade_data.get("pnl", 0.0)
            
            # Update daily metrics
            self.daily_loss += pnl
            self.daily_trades += 1
            
            # Add to active positions if opening
            if trade_data.get("action") == "open":
                self.active_positions[trade_id] = {
                    "symbol": symbol,
                    "entry_time": datetime.now().isoformat(),
                    "entry_price": trade_data.get("price", 0),
                    "quantity": trade_data.get("quantity", 0),
                    "unrealized_pnl": 0.0
                }
            
            # Remove from active positions if closing
            elif trade_data.get("action") == "close":
                if trade_id in self.active_positions:
                    del self.active_positions[trade_id]
            
            # Update database
            self._update_daily_record()
            
            # Check for risk events
            self._check_risk_events(trade_data)
            
        except Exception as e:
            self.logger.error(f"Error recording trade execution: {e}")
    
    def update_position_pnl(self, trade_id: str, current_pnl: float):
        """Update unrealized P&L for active position."""
        if trade_id in self.active_positions:
            old_pnl = self.active_positions[trade_id]["unrealized_pnl"]
            self.active_positions[trade_id]["unrealized_pnl"] = current_pnl
            
            # Check for significant adverse movement
            if current_pnl < old_pnl - 100:  # $100 adverse move
                self._send_risk_alert("ADVERSE_MOVE", f"Position {trade_id} down ${abs(current_pnl):.0f}")
    
    def emergency_stop_all(self) -> str:
        """Emergency stop with immediate lockout."""
        self._trigger_lockout("Emergency stop activated")
        
        # Log emergency event
        self._log_risk_event("EMERGENCY_STOP", "CRITICAL", "Manual emergency stop", "All trading halted")
        
        return "🚨 EMERGENCY STOP: All trading halted immediately"
    
    def reset_daily_limits(self) -> str:
        """Reset daily limits (admin function)."""
        self.daily_loss = 0.0
        self.daily_trades = 0
        self.is_locked_out = False
        self.lockout_reason = ""
        
        self._update_daily_record()
        
        return "✅ Daily limits reset - trading re-enabled"
    
    def get_safety_status(self) -> Dict:
        """Get current safety system status."""
        account_size = self.config["account_size"]
        daily_limit = self.config["daily_loss_limit"]
        
        return {
            "is_locked_out": self.is_locked_out,
            "lockout_reason": self.lockout_reason,
            "daily_loss": self.daily_loss,
            "daily_loss_limit": daily_limit,
            "remaining_loss_buffer": daily_limit + self.daily_loss if self.daily_loss < 0 else daily_limit,
            "daily_trades": self.daily_trades,
            "max_daily_trades": self.config["max_daily_trades"],
            "active_positions": len(self.active_positions),
            "max_positions": self.config["max_concurrent_positions"],
            "account_size": account_size,
            "portfolio_risk_pct": self._calculate_portfolio_risk(),
            "max_portfolio_risk": self.config["max_portfolio_risk"]
        }
    
    def register_alert_callback(self, callback: Callable):
        """Register callback for safety alerts."""
        self.alert_callbacks.append(callback)
    
    def _trigger_lockout(self, reason: str):
        """Trigger trading lockout."""
        self.is_locked_out = True
        self.lockout_reason = reason
        
        # Log the lockout
        self._log_risk_event("LOCKOUT", "CRITICAL", reason, "Trading halted")
        
        # Send alert
        self._send_risk_alert("LOCKOUT", f"Trading locked out: {reason}")
        
        # Update database
        self._update_daily_record()
    
    def _check_risk_events(self, trade_data: Dict):
        """Check for risk events after trade execution."""
        pnl = trade_data.get("pnl", 0.0)
        
        # Large single loss
        if pnl < -200:
            self._log_risk_event("LARGE_LOSS", "HIGH", f"Single trade loss: ${abs(pnl):.0f}", "Monitoring increased")
        
        # Check consecutive losses
        # This would require tracking recent trade history
        
        # Check if approaching daily limit
        remaining_buffer = self.config["daily_loss_limit"] + self.daily_loss
        if remaining_buffer < 100:
            self._send_risk_alert("APPROACHING_LIMIT", f"Only ${remaining_buffer:.0f} loss buffer remaining")
    
    def _calculate_portfolio_risk(self) -> float:
        """Calculate current portfolio risk percentage."""
        total_exposure = sum(
            pos["entry_price"] * pos["quantity"] 
            for pos in self.active_positions.values()
        )
        return (total_exposure / self.config["account_size"]) * 100
    
    def _update_daily_record(self):
        """Update daily safety record in database."""
        today = datetime.now().date().isoformat()
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE daily_safety 
                SET daily_loss = ?, daily_trades = ?, lockout_triggered = ?, lockout_reason = ?
                WHERE date = ?
            ''', (self.daily_loss, self.daily_trades, self.is_locked_out, self.lockout_reason, today))
            conn.commit()
    
    def _log_risk_event(self, event_type: str, severity: str, description: str, action: str):
        """Log a risk event to database."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO risk_events (timestamp, event_type, severity, description, action_taken)
                VALUES (?, ?, ?, ?, ?)
            ''', (datetime.now().isoformat(), event_type, severity, description, action))
            conn.commit()
    
    def _send_risk_alert(self, alert_type: str, message: str):
        """Send risk alert through configured channels."""
        alert_data = {
            "type": alert_type,
            "message": message,
            "timestamp": datetime.now().isoformat(),
            "severity": "HIGH" if alert_type in ["LOCKOUT", "EMERGENCY_STOP"] else "MEDIUM"
        }
        
        # Call registered callbacks
        for callback in self.alert_callbacks:
            try:
                callback(alert_data)
            except Exception as e:
                self.logger.error(f"Alert callback error: {e}")
        
        # Log the alert
        self.logger.warning(f"SAFETY ALERT [{alert_type}]: {message}")


# Global safety system instance
_safety_system = None

def get_safety_system() -> TradingSafetySystem:
    """Get the global safety system instance."""
    global _safety_system
    if _safety_system is None:
        _safety_system = TradingSafetySystem()
    return _safety_system


if __name__ == "__main__":
    # Test the safety system
    safety = TradingSafetySystem()
    
    print("🛡️ Testing Safety System")
    print("=" * 40)
    
    # Test trade validation
    test_trade = {
        "symbol": "AAPL",
        "quantity": 100,
        "price": 150.0,
        "action": "open"
    }
    
    result = safety.validate_trade(test_trade)
    print(f"✅ Trade validation: {'Approved' if result['approved'] else 'Rejected'}")
    
    # Test status
    status = safety.get_safety_status()
    print(f"✅ Safety status: Locked={status['is_locked_out']}, Loss=${status['daily_loss']:.2f}")
    
    print("🛡️ Safety system ready!")
