#!/usr/bin/env python3
"""Reddit WSB Sentiment Analyzer - FREE

Tracks Reddit WallStreetBets mentions and sentiment for TTM stocks.
Uses free Reddit API to detect social media buzz and sentiment.
"""
import requests
import json
import re
from typing import Dict, List, Optional
from datetime import datetime, timedelta
import time


class RedditSentimentAnalyzer:
    """Free Reddit sentiment analysis for TTM stocks."""
    
    def __init__(self):
        self.base_url = "https://www.reddit.com/r/wallstreetbets"
        self.headers = {
            'User-Agent': 'TTM-Trading-Bot/1.0'
        }
        
        # Sentiment keywords
        self.bullish_words = [
            'moon', 'rocket', 'bull', 'calls', 'buy', 'long', 'pump', 'squeeze',
            'breakout', 'rally', 'surge', 'bullish', 'green', 'gains', 'profit',
            'diamond hands', 'hold', 'hodl', 'to the moon', 'stonks'
        ]
        
        self.bearish_words = [
            'bear', 'puts', 'sell', 'short', 'dump', 'crash', 'drop', 'fall',
            'bearish', 'red', 'loss', 'paper hands', 'rip', 'dead', 'tank'
        ]
    
    def get_wsb_mentions(self, symbol: str, hours_back: int = 24) -> Dict:
        """Get WSB mentions for a symbol in the last X hours."""
        try:
            # Get hot posts from WSB
            url = f"{self.base_url}/hot.json?limit=100"
            response = requests.get(url, headers=self.headers, timeout=10)
            
            if response.status_code != 200:
                return self._empty_result(f"Reddit API error: {response.status_code}")
            
            data = response.json()
            posts = data.get('data', {}).get('children', [])
            
            mentions = []
            total_score = 0
            total_comments = 0
            
            cutoff_time = datetime.now() - timedelta(hours=hours_back)
            
            for post in posts:
                post_data = post.get('data', {})
                
                # Check if symbol is mentioned
                title = post_data.get('title', '').upper()
                selftext = post_data.get('selftext', '').upper()
                
                if symbol.upper() in title or symbol.upper() in selftext:
                    post_time = datetime.fromtimestamp(post_data.get('created_utc', 0))
                    
                    if post_time > cutoff_time:
                        sentiment = self._analyze_text_sentiment(title + " " + selftext)
                        
                        mentions.append({
                            'title': post_data.get('title', ''),
                            'score': post_data.get('score', 0),
                            'comments': post_data.get('num_comments', 0),
                            'sentiment': sentiment,
                            'url': f"https://reddit.com{post_data.get('permalink', '')}",
                            'created': post_time.strftime('%Y-%m-%d %H:%M')
                        })
                        
                        total_score += post_data.get('score', 0)
                        total_comments += post_data.get('num_comments', 0)
            
            # Calculate overall sentiment
            if mentions:
                avg_sentiment = sum(m['sentiment'] for m in mentions) / len(mentions)
                buzz_score = len(mentions) * 10 + total_score / 100
            else:
                avg_sentiment = 0
                buzz_score = 0
            
            return {
                'symbol': symbol,
                'mentions_count': len(mentions),
                'avg_sentiment': round(avg_sentiment, 2),
                'buzz_score': round(buzz_score, 1),
                'total_upvotes': total_score,
                'total_comments': total_comments,
                'mentions': mentions[:5],  # Top 5 mentions
                'analysis': self._interpret_sentiment(avg_sentiment, len(mentions))
            }
            
        except Exception as e:
            return self._empty_result(f"Error fetching Reddit data: {str(e)}")
    
    def _analyze_text_sentiment(self, text: str) -> float:
        """Analyze sentiment of text. Returns -1 to 1."""
        text = text.lower()
        
        bullish_count = sum(1 for word in self.bullish_words if word in text)
        bearish_count = sum(1 for word in self.bearish_words if word in text)
        
        if bullish_count == 0 and bearish_count == 0:
            return 0.0
        
        total = bullish_count + bearish_count
        sentiment = (bullish_count - bearish_count) / total
        
        return round(sentiment, 2)
    
    def _interpret_sentiment(self, sentiment: float, mention_count: int) -> str:
        """Interpret sentiment score and mention count."""
        if mention_count == 0:
            return "No recent WSB activity"
        
        buzz_level = "Low" if mention_count < 3 else "Moderate" if mention_count < 8 else "High"
        
        if sentiment > 0.3:
            sentiment_desc = "Very Bullish"
        elif sentiment > 0.1:
            sentiment_desc = "Bullish"
        elif sentiment > -0.1:
            sentiment_desc = "Neutral"
        elif sentiment > -0.3:
            sentiment_desc = "Bearish"
        else:
            sentiment_desc = "Very Bearish"
        
        return f"{sentiment_desc} sentiment, {buzz_level} buzz ({mention_count} mentions)"
    
    def _empty_result(self, error: str) -> Dict:
        """Return empty result with error."""
        return {
            'symbol': '',
            'mentions_count': 0,
            'avg_sentiment': 0,
            'buzz_score': 0,
            'total_upvotes': 0,
            'total_comments': 0,
            'mentions': [],
            'analysis': error
        }
    
    def get_multiple_symbols_sentiment(self, symbols: List[str]) -> Dict[str, Dict]:
        """Get sentiment for multiple symbols."""
        results = {}
        
        for symbol in symbols:
            results[symbol] = self.get_wsb_mentions(symbol)
            time.sleep(1)  # Be nice to Reddit's servers
        
        return results
    
    def get_trending_symbols(self) -> List[Dict]:
        """Get trending symbols on WSB."""
        try:
            url = f"{self.base_url}/hot.json?limit=50"
            response = requests.get(url, headers=self.headers, timeout=10)
            
            if response.status_code != 200:
                return []
            
            data = response.json()
            posts = data.get('data', {}).get('children', [])
            
            # Extract stock symbols from titles
            symbol_mentions = {}
            
            for post in posts:
                title = post.get('data', {}).get('title', '')
                
                # Find stock symbols (3-5 uppercase letters)
                symbols = re.findall(r'\b[A-Z]{3,5}\b', title)
                
                for symbol in symbols:
                    if symbol not in ['WSB', 'SEC', 'CEO', 'IPO', 'ETF', 'SPY', 'QQQ']:  # Filter common non-stocks
                        if symbol not in symbol_mentions:
                            symbol_mentions[symbol] = 0
                        symbol_mentions[symbol] += 1
            
            # Sort by mention count
            trending = [
                {'symbol': symbol, 'mentions': count}
                for symbol, count in sorted(symbol_mentions.items(), key=lambda x: x[1], reverse=True)
                if count >= 2  # At least 2 mentions
            ]
            
            return trending[:10]  # Top 10
            
        except Exception:
            return []


def get_reddit_sentiment_for_ttm(symbol: str) -> str:
    """Get Reddit sentiment analysis for TTM trading decisions."""
    analyzer = RedditSentimentAnalyzer()
    result = analyzer.get_wsb_mentions(symbol)
    
    if result['mentions_count'] == 0:
        return f"📱 **Reddit WSB Sentiment for ${symbol}:**\n• No recent mentions found\n• Social media buzz: Minimal"
    
    sentiment_emoji = "🚀" if result['avg_sentiment'] > 0.2 else "📉" if result['avg_sentiment'] < -0.2 else "😐"
    
    return f"""📱 **Reddit WSB Sentiment for ${symbol}:**
• {sentiment_emoji} Sentiment Score: {result['avg_sentiment']} ({result['analysis']})
• 💬 Mentions (24h): {result['mentions_count']}
• 🔥 Buzz Score: {result['buzz_score']}
• 👍 Total Upvotes: {result['total_upvotes']:,}
• 💭 Total Comments: {result['total_comments']:,}

**Recent Mentions:**
{chr(10).join([f"• {m['title'][:60]}... (Score: {m['score']}, Sentiment: {m['sentiment']})" for m in result['mentions'][:3]])}

**TTM Impact:** {'Bullish social confirmation for TTM setup' if result['avg_sentiment'] > 0.1 else 'Bearish social sentiment - proceed with caution' if result['avg_sentiment'] < -0.1 else 'Neutral social sentiment'}"""


if __name__ == "__main__":
    # Test the Reddit sentiment analyzer
    analyzer = RedditSentimentAnalyzer()
    
    print("🧪 Testing Reddit WSB Sentiment Analyzer")
    print("=" * 50)
    
    # Test with popular symbols
    test_symbols = ['NVDA', 'TSLA', 'AAPL']
    
    for symbol in test_symbols:
        print(f"\n📊 Testing {symbol}:")
        result = analyzer.get_wsb_mentions(symbol)
        print(f"Mentions: {result['mentions_count']}")
        print(f"Sentiment: {result['avg_sentiment']}")
        print(f"Analysis: {result['analysis']}")
        
        if result['mentions']:
            print(f"Top mention: {result['mentions'][0]['title'][:50]}...")
