#!/usr/bin/env python3
"""Investment Judge - AI Decision Engine

The AI that judges whether any investment idea is good or bad:
- Direct yes/no verdicts with reasoning
- Plain-English explanations
- TTM signals, sentiment, volatility analysis
- Better alternatives if it's a bad idea
- Complete investment intelligence
"""
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple
import logging

from ai_self_awareness import get_ai_brain, brain_log_decision


class InvestmentJudge:
    """AI judge that evaluates investment ideas with full reasoning."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.brain = get_ai_brain()
        
        # Judgment criteria weights
        self.criteria_weights = {
            'ttm_squeeze': 0.30,      # TTM squeeze signals
            'momentum': 0.25,         # Momentum and trend
            'sentiment': 0.20,        # Market sentiment
            'volatility': 0.15,       # Volatility analysis
            'risk_factors': 0.10      # Risk and macro factors
        }
        
        # Risk thresholds
        self.risk_thresholds = {
            'high_iv': 0.40,          # High IV threshold
            'extreme_sentiment': 0.70, # Extreme sentiment threshold
            'max_portfolio_exposure': 0.20  # Max single position exposure
        }
    
    def judge_investment(self, symbol: str, strategy: str = "buy stock", 
                        time_horizon: str = "1-3 days", amount: float = None) -> Dict:
        """Judge whether an investment idea is good or bad."""
        
        # Log the judgment request
        brain_log_decision("investment_judgment", symbol, 
                          f"Judging {strategy} for {time_horizon}", 
                          {"strategy": strategy, "time_horizon": time_horizon, "amount": amount})
        
        try:
            # Gather all analysis data
            analysis_data = self._gather_analysis_data(symbol)
            
            # Calculate individual scores
            scores = self._calculate_scores(symbol, analysis_data, strategy, time_horizon)
            
            # Calculate overall verdict
            overall_score = self._calculate_overall_score(scores)
            verdict = self._determine_verdict(overall_score, scores)
            
            # Generate reasoning
            reasoning = self._generate_reasoning(symbol, scores, analysis_data, strategy)
            
            # Get alternatives if verdict is negative
            alternatives = []
            if not verdict['is_good_idea']:
                alternatives = self._suggest_alternatives(symbol, analysis_data, strategy)
            
            # Create judgment result
            judgment = {
                'symbol': symbol,
                'strategy': strategy,
                'time_horizon': time_horizon,
                'timestamp': datetime.now().isoformat(),
                
                # Verdict
                'verdict': verdict,
                'overall_score': overall_score,
                
                # Detailed analysis
                'scores': scores,
                'reasoning': reasoning,
                'alternatives': alternatives,
                
                # Risk assessment
                'risk_assessment': self._assess_risks(symbol, analysis_data, strategy, amount),
                
                # Supporting data
                'analysis_data': analysis_data
            }
            
            # Update AI brain with judgment
            self.brain.update(f"last_judgment_{symbol}", judgment, 
                            f"Judged {symbol} {strategy}: {'✅ Good' if verdict['is_good_idea'] else '❌ Bad'}")
            
            return judgment
            
        except Exception as e:
            self.logger.error(f"Error judging investment {symbol}: {e}")
            return {
                'symbol': symbol,
                'error': str(e),
                'verdict': {'is_good_idea': False, 'confidence': 'Low', 'summary': 'Analysis failed'}
            }
    
    def _gather_analysis_data(self, symbol: str) -> Dict:
        """Gather all available analysis data for the symbol."""
        data = {}
        
        # Get data from AI brain
        brain_data = {
            'scan_grades': self.brain.get('scan_grades', {}),
            'scan_reasons': self.brain.get('scan_reasons', {}),
            'confidence_scores': self.brain.get('confidence_scores', {}),
            'squeeze_status': self.brain.get('squeeze_status', {}),
            'momentum_signals': self.brain.get('momentum_signals', {}),
            'sentiment_data': self.brain.get('sentiment_data', {}),
            'market_environment': self.brain.get('market_environment', {}),
            'active_positions': self.brain.get('active_positions', {}),
        }
        
        # Extract symbol-specific data
        data['ttm_grade'] = brain_data['scan_grades'].get(symbol, 'C')
        data['scan_reason'] = brain_data['scan_reasons'].get(symbol, 'No specific analysis')
        data['confidence_score'] = brain_data['confidence_scores'].get(symbol, 50.0)
        data['squeeze_status'] = brain_data['squeeze_status'].get(symbol, 'unknown')
        data['momentum'] = brain_data['momentum_signals'].get(symbol, {})
        data['sentiment'] = brain_data['sentiment_data'].get(symbol, {})
        data['market_env'] = brain_data['market_environment']
        data['existing_position'] = brain_data['active_positions'].get(symbol)
        
        # Mock additional data (in real system, would fetch from APIs)
        data.update({
            'current_price': 150.0,  # Would fetch real price
            'iv_rank': 0.35,         # Would fetch real IV
            'volume_ratio': 1.2,     # Would fetch real volume
            'rsi': 65,               # Would fetch real RSI
            'ema_trend': 'bullish',  # Would calculate real trend
            'support_level': 145.0,  # Would calculate real levels
            'resistance_level': 155.0,
            'earnings_date': None,   # Would fetch real earnings date
            'economic_events': []    # Would fetch real events
        })
        
        return data
    
    def _calculate_scores(self, symbol: str, data: Dict, strategy: str, time_horizon: str) -> Dict:
        """Calculate individual criterion scores."""
        scores = {}
        
        # TTM Squeeze Score (0-100)
        ttm_grade = data.get('ttm_grade', 'C')
        grade_scores = {'A+': 95, 'A': 85, 'B+': 75, 'B': 65, 'C+': 55, 'C': 45, 'D': 25, 'F': 10}
        ttm_score = grade_scores.get(ttm_grade, 45)
        
        # Boost for squeeze status
        squeeze_status = data.get('squeeze_status', 'unknown')
        if 'firing' in squeeze_status.lower():
            ttm_score += 10
        elif 'building' in squeeze_status.lower():
            ttm_score += 5
        
        scores['ttm_squeeze'] = min(100, ttm_score)
        
        # Momentum Score (0-100)
        momentum_score = 50  # Base score
        
        ema_trend = data.get('ema_trend', 'neutral')
        if ema_trend == 'bullish':
            momentum_score += 20
        elif ema_trend == 'bearish':
            momentum_score -= 20
        
        rsi = data.get('rsi', 50)
        if 30 <= rsi <= 70:  # Good RSI range
            momentum_score += 15
        elif rsi > 80 or rsi < 20:  # Extreme RSI
            momentum_score -= 10
        
        volume_ratio = data.get('volume_ratio', 1.0)
        if volume_ratio > 1.5:
            momentum_score += 15
        elif volume_ratio < 0.8:
            momentum_score -= 10
        
        scores['momentum'] = max(0, min(100, momentum_score))
        
        # Sentiment Score (0-100)
        sentiment_score = 50  # Base score
        
        sentiment_data = data.get('sentiment', {})
        reddit_sentiment = sentiment_data.get('reddit', 0.0)
        news_sentiment = sentiment_data.get('news', 0.0)
        
        # Positive sentiment boosts score
        if reddit_sentiment > 0.3:
            sentiment_score += 20
        elif reddit_sentiment > 0.1:
            sentiment_score += 10
        elif reddit_sentiment < -0.3:
            sentiment_score -= 15
        
        if news_sentiment > 0.2:
            sentiment_score += 15
        elif news_sentiment < -0.2:
            sentiment_score -= 10
        
        scores['sentiment'] = max(0, min(100, sentiment_score))
        
        # Volatility Score (0-100)
        volatility_score = 50  # Base score
        
        iv_rank = data.get('iv_rank', 0.5)
        
        if strategy in ['buy calls', 'buy puts']:
            # High IV is bad for buying options
            if iv_rank > 0.7:
                volatility_score -= 30
            elif iv_rank > 0.5:
                volatility_score -= 15
            elif iv_rank < 0.3:
                volatility_score += 15
        else:
            # For stock buying, moderate volatility is okay
            if iv_rank > 0.8:
                volatility_score -= 10
            elif 0.3 <= iv_rank <= 0.6:
                volatility_score += 10
        
        scores['volatility'] = max(0, min(100, volatility_score))
        
        # Risk Factors Score (0-100)
        risk_score = 70  # Start optimistic
        
        # Check for earnings
        if data.get('earnings_date'):
            risk_score -= 20
        
        # Check for economic events
        if data.get('economic_events'):
            risk_score -= 15
        
        # Check for existing position
        if data.get('existing_position'):
            risk_score -= 10
        
        # Check price levels
        current_price = data.get('current_price', 0)
        resistance = data.get('resistance_level', 0)
        if current_price and resistance and current_price > resistance * 0.98:
            risk_score -= 15  # Near resistance
        
        scores['risk_factors'] = max(0, min(100, risk_score))
        
        return scores
    
    def _calculate_overall_score(self, scores: Dict) -> float:
        """Calculate weighted overall score."""
        overall = 0.0
        
        for criterion, score in scores.items():
            weight = self.criteria_weights.get(criterion, 0.0)
            overall += score * weight
        
        return overall
    
    def _determine_verdict(self, overall_score: float, scores: Dict) -> Dict:
        """Determine the final verdict."""
        
        # Determine if it's a good idea
        is_good_idea = overall_score >= 65.0
        
        # Determine confidence level
        if overall_score >= 80:
            confidence = "Very High"
        elif overall_score >= 70:
            confidence = "High"
        elif overall_score >= 60:
            confidence = "Medium"
        elif overall_score >= 50:
            confidence = "Low"
        else:
            confidence = "Very Low"
        
        # Create summary
        if is_good_idea:
            if overall_score >= 80:
                summary = "Strong buy signal with high confidence"
            elif overall_score >= 70:
                summary = "Good opportunity with solid fundamentals"
            else:
                summary = "Decent opportunity but watch closely"
        else:
            if overall_score < 40:
                summary = "Avoid - multiple red flags"
            elif overall_score < 50:
                summary = "High risk - consider alternatives"
            else:
                summary = "Marginal - wait for better setup"
        
        return {
            'is_good_idea': is_good_idea,
            'confidence': confidence,
            'summary': summary,
            'score': overall_score
        }
    
    def _generate_reasoning(self, symbol: str, scores: Dict, data: Dict, strategy: str) -> str:
        """Generate detailed reasoning for the judgment."""
        
        reasoning = f"🧠 **INVESTMENT ANALYSIS - {symbol}**\n\n"
        
        # Overall verdict
        verdict = self._determine_verdict(self._calculate_overall_score(scores), scores)
        verdict_emoji = "✅" if verdict['is_good_idea'] else "❌"
        reasoning += f"{verdict_emoji} **Verdict:** {verdict['summary']}\n\n"
        
        # Detailed breakdown
        reasoning += "**📊 Analysis Breakdown:**\n"
        
        # TTM Analysis
        ttm_grade = data.get('ttm_grade', 'C')
        squeeze_status = data.get('squeeze_status', 'unknown')
        reasoning += f"• **TTM Squeeze:** Grade {ttm_grade} - {squeeze_status}\n"
        
        # Momentum Analysis
        ema_trend = data.get('ema_trend', 'neutral')
        rsi = data.get('rsi', 50)
        reasoning += f"• **Momentum:** {ema_trend.title()} trend, RSI {rsi}\n"
        
        # Sentiment Analysis
        sentiment_data = data.get('sentiment', {})
        reddit_sentiment = sentiment_data.get('reddit', 0.0)
        reasoning += f"• **Sentiment:** Reddit {reddit_sentiment:+.2f}, "
        
        if reddit_sentiment > 0.2:
            reasoning += "Bullish buzz\n"
        elif reddit_sentiment < -0.2:
            reasoning += "Bearish sentiment\n"
        else:
            reasoning += "Neutral\n"
        
        # Volatility Analysis
        iv_rank = data.get('iv_rank', 0.5)
        reasoning += f"• **Volatility:** IV Rank {iv_rank:.0%} - "
        
        if strategy in ['buy calls', 'buy puts']:
            if iv_rank > 0.6:
                reasoning += "High (bad for buying options)\n"
            else:
                reasoning += "Reasonable for option buying\n"
        else:
            reasoning += "Acceptable for stock\n"
        
        # Risk Factors
        risk_factors = []
        if data.get('earnings_date'):
            risk_factors.append("Earnings approaching")
        if data.get('existing_position'):
            risk_factors.append("Already have position")
        
        if risk_factors:
            reasoning += f"• **Risk Factors:** {', '.join(risk_factors)}\n"
        else:
            reasoning += "• **Risk Factors:** None identified\n"
        
        return reasoning
    
    def _suggest_alternatives(self, symbol: str, data: Dict, strategy: str) -> List[str]:
        """Suggest better alternatives if the idea is bad."""
        alternatives = []
        
        iv_rank = data.get('iv_rank', 0.5)
        ttm_grade = data.get('ttm_grade', 'C')
        
        # If high IV and trying to buy options
        if strategy in ['buy calls', 'buy puts'] and iv_rank > 0.6:
            alternatives.append("Consider selling premium instead (iron condor, credit spreads)")
            alternatives.append("Wait for IV to decrease before buying options")
        
        # If poor TTM grade
        if ttm_grade in ['D', 'F']:
            alternatives.append("Wait for better TTM squeeze setup")
            alternatives.append("Look for A or B grade alternatives")
        
        # If near resistance
        current_price = data.get('current_price', 0)
        resistance = data.get('resistance_level', 0)
        if current_price and resistance and current_price > resistance * 0.98:
            alternatives.append("Wait for pullback to support levels")
            alternatives.append("Consider short-term bearish play")
        
        # General alternatives
        if not alternatives:
            alternatives.append("Wait for better market conditions")
            alternatives.append("Consider diversifying into different sectors")
        
        return alternatives
    
    def _assess_risks(self, symbol: str, data: Dict, strategy: str, amount: float) -> Dict:
        """Assess specific risks for the investment."""
        
        risks = {
            'risk_level': 'Medium',
            'max_loss': 'Unknown',
            'probability_of_loss': 'Medium',
            'risk_factors': [],
            'position_sizing': 'Standard'
        }
        
        # Calculate risk level
        risk_score = 0
        
        if data.get('iv_rank', 0.5) > 0.7:
            risk_score += 2
            risks['risk_factors'].append("High volatility")
        
        if data.get('ttm_grade', 'C') in ['D', 'F']:
            risk_score += 2
            risks['risk_factors'].append("Poor technical setup")
        
        if data.get('earnings_date'):
            risk_score += 1
            risks['risk_factors'].append("Earnings event risk")
        
        # Determine risk level
        if risk_score >= 4:
            risks['risk_level'] = 'Very High'
            risks['position_sizing'] = 'Small (25% normal size)'
        elif risk_score >= 3:
            risks['risk_level'] = 'High'
            risks['position_sizing'] = 'Reduced (50% normal size)'
        elif risk_score >= 2:
            risks['risk_level'] = 'Medium-High'
            risks['position_sizing'] = 'Slightly reduced (75% normal size)'
        elif risk_score <= 1:
            risks['risk_level'] = 'Low'
            risks['position_sizing'] = 'Standard size'
        
        return risks
    
    def format_judgment_response(self, judgment: Dict) -> str:
        """Format judgment into natural language response."""
        
        if 'error' in judgment:
            return f"❌ **Analysis Error:** {judgment['error']}"
        
        symbol = judgment['symbol']
        strategy = judgment['strategy']
        verdict = judgment['verdict']
        
        # Header
        response = f"🎯 **INVESTMENT JUDGMENT: {symbol}**\n\n"
        
        # Main verdict
        verdict_emoji = "✅" if verdict['is_good_idea'] else "❌"
        response += f"{verdict_emoji} **{verdict['summary']}**\n"
        response += f"**Confidence:** {verdict['confidence']} ({verdict['score']:.1f}/100)\n\n"
        
        # Reasoning
        response += judgment['reasoning']
        
        # Risk assessment
        risk = judgment['risk_assessment']
        response += f"\n**🛡️ Risk Assessment:**\n"
        response += f"• Risk Level: {risk['risk_level']}\n"
        response += f"• Position Sizing: {risk['position_sizing']}\n"
        
        if risk['risk_factors']:
            response += f"• Risk Factors: {', '.join(risk['risk_factors'])}\n"
        
        # Alternatives if bad idea
        if not verdict['is_good_idea'] and judgment.get('alternatives'):
            response += f"\n**💡 Better Alternatives:**\n"
            for alt in judgment['alternatives']:
                response += f"• {alt}\n"
        
        response += f"\n💡 **Analysis based on real-time TTM data and market intelligence**"
        
        return response


# Global investment judge instance
_investment_judge = None

def get_investment_judge() -> InvestmentJudge:
    """Get the global investment judge instance."""
    global _investment_judge
    if _investment_judge is None:
        _investment_judge = InvestmentJudge()
    return _investment_judge


# Convenience function
def judge_investment(symbol: str, strategy: str = "buy stock", 
                    time_horizon: str = "1-3 days", amount: float = None) -> str:
    """Judge an investment idea and return formatted response."""
    judge = get_investment_judge()
    judgment = judge.judge_investment(symbol, strategy, time_horizon, amount)
    return judge.format_judgment_response(judgment)


if __name__ == "__main__":
    # Test the investment judge
    judge = InvestmentJudge()
    
    print("🎯 Testing Investment Judge")
    print("=" * 30)
    
    # Test judgment
    judgment = judge.judge_investment("AAPL", "buy calls", "1 week")
    response = judge.format_judgment_response(judgment)
    
    print(response)
    
    print("\n🎯 Investment Judge ready!")
