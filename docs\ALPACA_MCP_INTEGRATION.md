# 🚀 Alpaca MCP Server Integration Guide

## Overview

The Alpaca MCP (Model Context Protocol) Server integration transforms our TTM trading system by giving the AI chat **direct trading capabilities** through natural language. This means you can literally ask the AI to execute trades, analyze positions, and manage your portfolio through conversation.

## 🎯 What This Integration Provides

### **Enhanced AI Chat Capabilities**
- **Direct Trade Execution**: "Buy 10 shares of PLTR at market price"
- **Real-time Portfolio Analysis**: "What's my current PLTR position risk/reward?"
- **Live Market Data**: "What's the current AAPL quote with Greeks?"
- **Advanced Options Trading**: "Place a bull call spread on TSLA"
- **Complete Account Management**: "Show me my buying power and positions"

### **TTM System Enhancement**
- **Live Scanner → AI Analysis → Direct Execution** pipeline
- **Natural language risk/reward calculations**
- **Real-time position monitoring through chat**
- **Advanced options strategies via conversation**
- **Seamless integration with existing automation**

## 🔧 Installation & Setup

### Step 1: Run the Integration Script

```bash
python integrations/alpaca_mcp_integration.py
```

This will:
- Download the Alpaca MCP Server
- Install required dependencies
- Configure credentials
- Generate Claude Desktop config
- Start the MCP server

### Step 2: Configure Claude Desktop

1. **Open Claude Desktop**
2. **Go to Settings → Developer → Edit Config**
3. **Copy contents from `integrations/claude_desktop_config.json`**
4. **Restart Claude Desktop**

### Step 3: Test the Integration

Open Claude Desktop and try:
```
What's my current account balance and buying power?
```

## 💡 Example Natural Language Trading Commands

### **Basic Trading**
```
Buy 10 shares of AAPL at market price
Sell 5 shares of TSLA with a limit price of $300
Cancel all open orders
Liquidate my entire GOOGL position
Close 10% of my NVDA position
```

### **Portfolio Analysis**
```
What's my current PLTR position risk/reward?
Show me my current positions
How much buying power do I have?
What's my portfolio performance today?
List all my open orders
```

### **Market Data**
```
What's the current AAPL quote?
Show me TSLA's daily price history for the last 5 days
Get the latest trade price for NVDA
What are the Greeks for AAPL250613C00200000?
Is the market currently open?
```

### **Options Trading**
```
Show me AAPL option contracts expiring next month
Place a bull call spread on TSLA using June options
Get the option snapshot for SPY250627P00400000
What are the Greeks for my TSLA put options?
```

### **Advanced Strategies**
```
Place a bull call spread using AAPL June 6th options: 
one with a 190.00 strike and the other with a 200.00 strike

Create an iron condor on SPY with strikes at 400, 405, 415, 420

Buy a protective put for my TSLA position
```

## 🔗 Integration with TTM System

### **Enhanced Workflow**

1. **TTM Scanner finds A+ opportunity** (e.g., PLTR squeeze release)
2. **Dashboard displays the setup** with entry, stop, target
3. **AI Chat analyzes the opportunity**: 
   ```
   "Analyze the PLTR A+ TTM setup - what's the risk/reward?"
   ```
4. **AI provides detailed analysis**:
   ```
   PLTR A+ TTM Setup Analysis:
   • Entry: $130.39
   • Stop Loss: $127.18  
   • Target: $137.82
   • Risk: $3.21 per share
   • Reward: $7.43 per share
   • Risk/Reward Ratio: 1:2.31
   • Recommended position size: 31 shares ($100 risk)
   ```
5. **Execute through chat**:
   ```
   "Execute this PLTR trade with 31 shares"
   ```
6. **AI executes and confirms**:
   ```
   ✅ Order placed: BUY 31 shares PLTR at $130.39
   Order ID: abc123
   Position value: $4,042
   Max risk: $100
   Max reward: $230
   ```

### **Real-time Position Monitoring**

Ask anytime:
```
"How is my PLTR position performing?"
```

Get instant response:
```
PLTR Position Update:
• Current Price: $132.15 (+1.35%)
• Unrealized P&L: +$54.56 (+1.36%)
• Distance to Stop: $4.97 (3.8%)
• Distance to Target: $5.67 (4.3%)
• Position Status: ✅ Profitable
```

## 🛡️ Security & Risk Management

### **Built-in Safeguards**
- **Paper Trading by Default**: All initial setup uses paper trading
- **Order Confirmation**: AI shows order details before execution
- **Position Limits**: Respects account buying power and risk limits
- **Real-time Monitoring**: Continuous position tracking

### **Risk Controls**
```
"Set a stop loss at $125 for my PLTR position"
"What's my maximum daily loss exposure?"
"Show me all positions with unrealized losses"
```

## 🎯 Advanced Features

### **Multi-Asset Analysis**
```
"Compare the risk/reward of my AAPL and TSLA positions"
"Which of my positions has the best risk/reward ratio?"
"Show me my portfolio Greek exposure"
```

### **Strategy Optimization**
```
"Should I roll my AAPL calls to next month?"
"What's the optimal position size for this NVDA setup?"
"Analyze the probability of profit for my SPY iron condor"
```

### **Market Intelligence**
```
"What earnings are coming up for my holdings?"
"Show me unusual options activity for TSLA"
"What's the market sentiment for tech stocks today?"
```

## 🚀 Getting Started

1. **Run the integration script**:
   ```bash
   python integrations/alpaca_mcp_integration.py
   ```

2. **Configure Claude Desktop** with the generated config

3. **Start with simple queries**:
   ```
   What's my account balance?
   Show me my current positions
   ```

4. **Progress to trading commands**:
   ```
   Buy 1 share of AAPL to test the system
   ```

5. **Integrate with TTM workflow**:
   ```
   Analyze the latest A+ opportunity from the scanner
   ```

## 💡 Pro Tips

- **Always verify orders** before execution in live trading
- **Start with paper trading** to test the integration
- **Use specific quantities** for precise control
- **Ask for analysis** before making trades
- **Monitor positions** through chat regularly

## 🔄 Troubleshooting

### Common Issues:
- **MCP Server not starting**: Check API credentials
- **Claude not responding**: Restart Claude Desktop
- **Orders not executing**: Verify market hours and buying power

### Support:
- Check the MCP server logs
- Verify Alpaca API credentials
- Ensure Claude Desktop config is correct

---

**This integration transforms your TTM trading system into a conversational trading platform where you can execute sophisticated strategies through natural language!** 🚀📊💰
