# Advanced TTM Squeeze Scanner

## Overview

The Advanced TTM Squeeze Scanner identifies the exact pattern shown in the PLTR screenshot - a comprehensive multi-timeframe scanner that finds high-probability momentum setups across all stocks over $100B market cap.

## The PLTR Pattern

This scanner identifies the specific pattern from your PLTR 5-minute chart:

### 📊 Pattern Components

1. **Squeeze Release** - Bollinger Bands break outside Keltner Channels
2. **Histogram Build** - 3 consecutive rising bars after ≥4 down bars  
3. **EMA Confirmation** - 8-EMA rising vs 4 bars ago
4. **Momentum Confirmation** - Momentum(12) rising vs 4 bars ago
5. **Price Filter** - Close > 5-EMA
6. **SqueezeLine Threshold** - SqueezeLine > 70% (strong breakout)

### 🎯 What Makes This Special

- **Exact Pattern Match**: Replicates the precise conditions from your PLTR screenshot
- **Multi-Timeframe**: Scans 1min, 5min, 15min, 30min, 1hour, 4hour, 1day
- **Large Cap Focus**: Automatically scans all stocks over $100B market cap
- **High Probability**: Only flags setups with all 6 conditions aligned
- **Graded Results**: A+ to F grading system with confidence scores

## Installation

### Quick Install
```bash
# Install dependencies
python install_dependencies.py

# Test the scanner
python test_ttm_squeeze_scanner.py
```

### Manual Install
```bash
# Install required packages
pip install -r requirements.txt

# Note: TA-Lib may require special installation
# Windows: Download from https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib
# macOS: brew install ta-lib && pip install TA-Lib
# Linux: sudo apt-get install libta-lib-dev && pip install TA-Lib
```

## Usage

### Command Line
```bash
# Scan all large caps across all timeframes
python advanced_ttm_squeeze_scanner.py

# Scan specific symbols and timeframes
python advanced_ttm_squeeze_scanner.py --symbols AAPL MSFT PLTR --timeframes 5min 15min

# Limit results
python advanced_ttm_squeeze_scanner.py --max-results 10
```

### Python API
```python
from advanced_ttm_squeeze_scanner import run_ttm_squeeze_scan

# Scan with default settings (all large caps, all timeframes)
results = run_ttm_squeeze_scan()

# Scan specific symbols
results = run_ttm_squeeze_scan(
    symbols=['AAPL', 'MSFT', 'PLTR'],
    timeframes=['5min', '15min', '1hour'],
    max_results=20
)

# Display results
if "top_setups" in results:
    for setup in results["top_setups"]:
        print(f"{setup['symbol']} ({setup['timeframe']}) - Grade: {setup['grade']}")
        print(f"Entry: {setup['entry_price']} | Target: {setup['target_price']}")
```

### Chat Integration
```
Ask the AI: "Scan for TTM Squeeze setups like the PLTR pattern"
```

## Output Format

### Scan Summary
```json
{
  "scan_summary": {
    "total_setups_found": 15,
    "showing_top": 10,
    "scan_timestamp": "2025-01-27 14:30:00",
    "pattern": "TTM Squeeze Histogram Build + EMA/Momentum Confirmation"
  }
}
```

### Setup Details
```json
{
  "symbol": "PLTR",
  "timeframe": "5min",
  "grade": "A+",
  "confidence": "95.0%",
  "signal_time": "14:25",
  "current_price": "$25.50",
  "entry_price": "$25.55",
  "stop_loss": "$24.80",
  "target_price": "$27.03",
  "risk_reward": "1:2.4",
  "pattern_confirmation": {
    "squeeze_released": "✅",
    "histogram_build": "✅", 
    "ema8_rising": "✅",
    "momentum_rising": "✅",
    "price_above_5ema": "✅",
    "squeeze_line": "75.2%"
  }
}
```

## Technical Details

### Indicators Used
- **Bollinger Bands**: 20-period, 2 standard deviations
- **Keltner Channels**: 20-period EMA ± 1.5×ATR
- **EMAs**: 5, 8, 21 periods
- **Momentum**: 12-period momentum oscillator
- **TTM Squeeze Histogram**: Momentum when squeeze releases
- **SqueezeLine**: Normalized volatility ratio (0-100%)

### Grading System
- **A+ (90-100%)**: All 6 conditions + strong momentum
- **A (85-89%)**: All 6 conditions met
- **B (75-84%)**: 5 of 6 conditions
- **C (65-74%)**: 4 of 6 conditions
- **D (55-64%)**: 3 of 6 conditions
- **F (<55%)**: Insufficient conditions

### Performance Features
- **Async Processing**: Concurrent data fetching
- **Rate Limiting**: Respects FMP API limits
- **Error Handling**: Graceful failure recovery
- **Caching**: Efficient data management

## Configuration

### Environment Variables
```bash
# Required
FMP_API_KEY=your_fmp_api_key

# Optional
SCAN_MAX_CONCURRENT=10
SCAN_TIMEOUT_SECONDS=30
```

### Timeframes Available
- `1min` - 1-minute bars
- `5min` - 5-minute bars (PLTR pattern timeframe)
- `15min` - 15-minute bars
- `30min` - 30-minute bars
- `1hour` - 1-hour bars
- `4hour` - 4-hour bars
- `1day` - Daily bars

## Troubleshooting

### Common Issues

**TA-Lib Installation Failed**
```bash
# Windows
pip install --find-links https://www.lfd.uci.edu/~gohlke/pythonlibs/ TA-Lib

# macOS
brew install ta-lib
pip install TA-Lib

# Linux
sudo apt-get install libta-lib-dev
pip install TA-Lib
```

**No Data Returned**
- Check if markets are open
- Verify FMP API key is valid
- Ensure symbols exist and are tradeable

**API Rate Limits**
- Scanner includes built-in rate limiting
- Reduce concurrent requests if needed
- Consider upgrading FMP subscription

### Debug Mode
```python
import logging
logging.basicConfig(level=logging.DEBUG)

# Run scanner with debug output
results = run_ttm_squeeze_scan(symbols=['AAPL'])
```

## Integration

### With Existing Systems
```python
# Import the scanner
from advanced_ttm_squeeze_scanner import AdvancedTTMSqueezeScanner

# Create scanner instance
scanner = AdvancedTTMSqueezeScanner()

# Get large cap stocks
symbols = await scanner.get_large_cap_stocks()

# Scan specific symbol/timeframe
setups = await scanner.scan_symbol_timeframe('AAPL', '5min')
```

### Scheduled Scanning
```python
import schedule
import time

def run_scan():
    results = run_ttm_squeeze_scan(max_results=5)
    # Process results...

# Run every 5 minutes during market hours
schedule.every(5).minutes.do(run_scan)

while True:
    schedule.run_pending()
    time.sleep(1)
```

## Support

### Getting Help
1. Run the test script: `python test_ttm_squeeze_scanner.py`
2. Check the installation: `python install_dependencies.py`
3. Review logs for error details
4. Verify API keys and market hours

### Performance Tips
- Use specific symbols instead of scanning all large caps
- Limit timeframes to reduce API calls
- Run during market hours for fresh data
- Consider caching results for repeated queries

---

**Note**: This scanner identifies high-probability setups but does not guarantee profits. Always use proper risk management and position sizing.
