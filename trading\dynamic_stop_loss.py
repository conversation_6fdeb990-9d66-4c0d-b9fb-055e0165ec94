"""Dynamic Stop Loss Manager with Trailing Stops and Market Condition Adjustments

This module provides intelligent stop loss management that adapts to:
- Market volatility (ATR-based adjustments)
- Trend strength (momentum-based trailing)
- Support/resistance levels
- Time-based adjustments
"""
from __future__ import annotations

import math
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import requests
import numpy as np

from config import get_api_key
from logger_util import info, warning

FMP_BASE = "https://financialmodelingprep.com/api/v3"


class DynamicStopLossManager:
    """Advanced stop loss management with market condition awareness."""
    
    def __init__(self):
        self.api_key = get_api_key('FMP_API_KEY')
        if not self.api_key:
            raise ValueError("FMP_API_KEY not found in environment")
    
    def _get_historical_data(self, symbol: str, days: int = 30) -> List[Dict]:
        """Get historical price data for volatility calculations."""
        try:
            url = f"{FMP_BASE}/historical-price-full/{symbol}?apikey={self.api_key}"
            response = requests.get(url, timeout=10)
            data = response.json()
            
            if 'historical' in data:
                # Get last N days
                return data['historical'][:days]
            return []
        except Exception as e:
            warning(f"Failed to get historical data for {symbol}: {e}")
            return []
    
    def _calculate_atr(self, historical_data: List[Dict], period: int = 14) -> float:
        """Calculate Average True Range for volatility measurement."""
        if len(historical_data) < period + 1:
            return 0.02  # Default 2% if insufficient data
        
        true_ranges = []
        for i in range(1, min(len(historical_data), period + 1)):
            current = historical_data[i]
            previous = historical_data[i-1]
            
            high_low = current['high'] - current['low']
            high_close = abs(current['high'] - previous['close'])
            low_close = abs(current['low'] - previous['close'])
            
            true_range = max(high_low, high_close, low_close)
            true_ranges.append(true_range)
        
        if not true_ranges:
            return 0.02
        
        atr = sum(true_ranges) / len(true_ranges)
        # Convert to percentage of current price
        current_price = historical_data[0]['close']
        return atr / current_price if current_price > 0 else 0.02
    
    def _calculate_momentum(self, historical_data: List[Dict], period: int = 10) -> float:
        """Calculate price momentum for trend strength assessment."""
        if len(historical_data) < period:
            return 0.0
        
        current_price = historical_data[0]['close']
        past_price = historical_data[period-1]['close']
        
        return (current_price - past_price) / past_price if past_price > 0 else 0.0
    
    def _find_support_resistance(self, historical_data: List[Dict]) -> Tuple[float, float]:
        """Find nearest support and resistance levels."""
        if len(historical_data) < 10:
            return 0.0, 0.0
        
        highs = [d['high'] for d in historical_data[:20]]
        lows = [d['low'] for d in historical_data[:20]]
        current_price = historical_data[0]['close']
        
        # Find resistance (recent high above current price)
        resistance = 0.0
        for high in sorted(set(highs), reverse=True):
            if high > current_price * 1.01:  # At least 1% above
                resistance = high
                break
        
        # Find support (recent low below current price)
        support = 0.0
        for low in sorted(set(lows)):
            if low < current_price * 0.99:  # At least 1% below
                support = low
                break
        
        return support, resistance
    
    def calculate_dynamic_stop_loss(self, 
                                  symbol: str, 
                                  entry_price: float,
                                  position_type: str = "long",
                                  base_stop_pct: float = 0.02,
                                  use_trailing: bool = True) -> Dict:
        """
        Calculate dynamic stop loss based on market conditions.
        
        Args:
            symbol: Stock symbol
            entry_price: Entry price of the position
            position_type: "long" or "short"
            base_stop_pct: Base stop loss percentage (default 2%)
            use_trailing: Whether to use trailing stop logic
            
        Returns:
            Dict with stop loss recommendations and analysis
        """
        info(f"🎯 Calculating dynamic stop loss for {symbol}")
        
        # Get market data
        historical_data = self._get_historical_data(symbol)
        if not historical_data:
            warning(f"No historical data for {symbol}, using base stop loss")
            stop_price = entry_price * (1 - base_stop_pct) if position_type == "long" else entry_price * (1 + base_stop_pct)
            return {
                "stop_price": round(stop_price, 2),
                "stop_type": "fixed",
                "stop_percentage": base_stop_pct * 100,
                "analysis": "Using fixed stop loss due to insufficient data"
            }
        
        current_price = historical_data[0]['close']
        
        # Calculate market condition factors
        atr_pct = self._calculate_atr(historical_data)
        momentum = self._calculate_momentum(historical_data)
        support, resistance = self._find_support_resistance(historical_data)
        
        # Adjust stop loss based on volatility (ATR)
        volatility_multiplier = max(0.5, min(2.0, atr_pct / 0.02))  # Scale based on 2% baseline
        adjusted_stop_pct = base_stop_pct * volatility_multiplier
        
        # Adjust for momentum (tighter stops in weak trends)
        if position_type == "long":
            if momentum < -0.05:  # Weak downtrend
                adjusted_stop_pct *= 0.7  # Tighter stop
            elif momentum > 0.05:  # Strong uptrend
                adjusted_stop_pct *= 1.3  # Wider stop for volatility
        
        # Consider support/resistance levels
        if position_type == "long" and support > 0:
            support_stop_pct = (entry_price - support) / entry_price
            if support_stop_pct > 0 and support_stop_pct < adjusted_stop_pct * 2:
                adjusted_stop_pct = max(adjusted_stop_pct, support_stop_pct)
        
        # Calculate stop price
        if position_type == "long":
            stop_price = entry_price * (1 - adjusted_stop_pct)
        else:
            stop_price = entry_price * (1 + adjusted_stop_pct)
        
        # Trailing stop logic
        trailing_info = ""
        if use_trailing and position_type == "long":
            # For profitable positions, use trailing stop
            if current_price > entry_price * 1.02:  # At least 2% profit
                profit_pct = (current_price - entry_price) / entry_price
                # Trail stop at 50% of current profit
                trailing_stop = current_price * (1 - (profit_pct * 0.5))
                if trailing_stop > stop_price:
                    stop_price = trailing_stop
                    trailing_info = f" (trailing at 50% of {profit_pct*100:.1f}% profit)"
        
        # Determine stop type
        stop_type = "dynamic"
        if trailing_info:
            stop_type = "trailing"
        elif abs(adjusted_stop_pct - base_stop_pct) < 0.005:
            stop_type = "standard"
        
        analysis_parts = []
        analysis_parts.append(f"ATR-based volatility: {atr_pct*100:.1f}% (multiplier: {volatility_multiplier:.1f}x)")
        analysis_parts.append(f"Momentum: {momentum*100:+.1f}%")
        if support > 0:
            analysis_parts.append(f"Support level: ${support:.2f}")
        if resistance > 0:
            analysis_parts.append(f"Resistance level: ${resistance:.2f}")
        analysis_parts.append(f"Adjusted stop: {adjusted_stop_pct*100:.1f}%{trailing_info}")
        
        return {
            "stop_price": round(stop_price, 2),
            "stop_type": stop_type,
            "stop_percentage": round(adjusted_stop_pct * 100, 1),
            "atr_volatility": round(atr_pct * 100, 1),
            "momentum": round(momentum * 100, 1),
            "support_level": round(support, 2) if support > 0 else None,
            "resistance_level": round(resistance, 2) if resistance > 0 else None,
            "analysis": " | ".join(analysis_parts)
        }
    
    def update_trailing_stop(self, 
                           symbol: str,
                           entry_price: float,
                           current_stop: float,
                           position_type: str = "long") -> Dict:
        """Update trailing stop based on current market conditions."""
        info(f"🔄 Updating trailing stop for {symbol}")
        
        historical_data = self._get_historical_data(symbol, days=5)
        if not historical_data:
            return {
                "new_stop": current_stop,
                "updated": False,
                "reason": "No market data available"
            }
        
        current_price = historical_data[0]['close']
        
        if position_type == "long":
            # Only move stop up, never down
            if current_price > entry_price:
                profit_pct = (current_price - entry_price) / entry_price
                # Trail at 50% of profit
                new_trailing_stop = current_price * (1 - (profit_pct * 0.5))
                
                if new_trailing_stop > current_stop:
                    return {
                        "new_stop": round(new_trailing_stop, 2),
                        "updated": True,
                        "reason": f"Trailing up with {profit_pct*100:.1f}% profit"
                    }
        
        return {
            "new_stop": current_stop,
            "updated": False,
            "reason": "No trailing adjustment needed"
        }
