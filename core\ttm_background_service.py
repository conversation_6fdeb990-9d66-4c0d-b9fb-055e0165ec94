#!/usr/bin/env python3
"""
TTM Background Service - Ensures continuous TTM squeeze monitoring
Runs as a background service with automatic restart and error recovery
"""

import time
import threading
import logging
import os
import sys
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
import json
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/ttm_service.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

try:
    from core.ttm_alert_system import get_alert_system, start_ttm_alerts, stop_ttm_alerts
    ALERT_SYSTEM_AVAILABLE = True
except ImportError:
    ALERT_SYSTEM_AVAILABLE = False
    logger.error("TTM Alert System not available")


class TTMBackgroundService:
    """Background service for continuous TTM monitoring with auto-recovery."""
    
    def __init__(self, config_file: str = "data/service_config.json"):
        self.config_file = config_file
        self.config = self._load_config()
        self.is_running = False
        self.service_thread = None
        self.alert_system = None
        
        # Service statistics
        self.stats = {
            "service_start_time": None,
            "total_restarts": 0,
            "last_restart": None,
            "uptime_seconds": 0,
            "health_checks": 0,
            "health_failures": 0
        }
        
        # Health monitoring
        self.last_health_check = None
        self.consecutive_failures = 0
        self.max_failures = self.config.get("max_consecutive_failures", 3)
        
        # Auto-restart settings
        self.auto_restart = self.config.get("auto_restart", True)
        self.restart_delay = self.config.get("restart_delay_seconds", 30)
        
        # Ensure logs directory exists
        os.makedirs("logs", exist_ok=True)
    
    def _load_config(self) -> Dict[str, Any]:
        """Load service configuration."""
        default_config = {
            "auto_restart": True,
            "restart_delay_seconds": 30,
            "health_check_interval": 60,
            "max_consecutive_failures": 3,
            "monitoring_settings": {
                "min_grade": "A",
                "scan_interval": 120,
                "market_hours_only": True
            },
            "filters": {
                "min_price": 1.0,
                "max_price": 1000.0,
                "min_volume": 100000,
                "min_confidence": 0.6,
                "min_risk_reward": 1.5,
                "max_alerts_per_hour": 10
            }
        }
        
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                    # Merge with defaults
                    default_config.update(config)
                    return default_config
        except Exception as e:
            logger.warning(f"Failed to load config: {e}, using defaults")
        
        return default_config
    
    def _save_config(self):
        """Save current configuration."""
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save config: {e}")
    
    def start_service(self) -> bool:
        """Start the background service."""
        if self.is_running:
            logger.warning("Service already running")
            return False
        
        if not ALERT_SYSTEM_AVAILABLE:
            logger.error("Cannot start service: TTM Alert System not available")
            return False
        
        logger.info("🚀 Starting TTM Background Service")
        
        self.is_running = True
        self.stats["service_start_time"] = datetime.now()
        self.alert_system = get_alert_system()
        
        # Start service thread
        self.service_thread = threading.Thread(target=self._service_loop, daemon=True)
        self.service_thread.start()
        
        # Start monitoring with configured settings
        self._start_monitoring()
        
        logger.info("✅ TTM Background Service started successfully")
        return True
    
    def stop_service(self) -> bool:
        """Stop the background service."""
        if not self.is_running:
            logger.warning("Service not running")
            return False
        
        logger.info("⏹️ Stopping TTM Background Service")
        
        self.is_running = False
        
        # Stop TTM monitoring
        try:
            stop_ttm_alerts()
        except Exception as e:
            logger.error(f"Error stopping TTM alerts: {e}")
        
        # Wait for service thread to finish
        if self.service_thread and self.service_thread.is_alive():
            self.service_thread.join(timeout=5)
        
        logger.info("✅ TTM Background Service stopped")
        return True
    
    def _service_loop(self):
        """Main service monitoring loop."""
        logger.info("🔄 Service monitoring loop started")
        
        while self.is_running:
            try:
                # Update uptime
                if self.stats["service_start_time"]:
                    self.stats["uptime_seconds"] = (
                        datetime.now() - self.stats["service_start_time"]
                    ).total_seconds()
                
                # Perform health check
                self._health_check()
                
                # Sleep until next check
                time.sleep(self.config.get("health_check_interval", 60))
                
            except Exception as e:
                logger.error(f"Service loop error: {e}")
                time.sleep(30)  # Wait before retrying
    
    def _health_check(self):
        """Perform health check on TTM monitoring."""
        try:
            self.stats["health_checks"] += 1
            self.last_health_check = datetime.now()
            
            # Check if alert system is responsive
            if not self.alert_system:
                raise Exception("Alert system not initialized")
            
            status = self.alert_system.get_status()
            
            # Check if monitoring is running when it should be
            should_be_running = self._should_monitoring_be_running()
            is_running = status.get("is_running", False)
            
            if should_be_running and not is_running:
                logger.warning("🔧 Monitoring should be running but isn't - restarting")
                self._restart_monitoring()
                return
            
            # Check for excessive errors
            stats = self.alert_system.get_statistics()
            error_rate = stats.get("scan_errors", 0) / max(1, stats.get("total_scans", 1))
            
            if error_rate > 0.5:  # More than 50% error rate
                logger.warning(f"🔧 High error rate detected ({error_rate:.1%}) - restarting")
                self._restart_monitoring()
                return
            
            # Health check passed
            self.consecutive_failures = 0
            logger.debug("✅ Health check passed")
            
        except Exception as e:
            self.stats["health_failures"] += 1
            self.consecutive_failures += 1
            
            logger.error(f"❌ Health check failed: {e}")
            
            # Auto-restart if too many consecutive failures
            if (self.auto_restart and 
                self.consecutive_failures >= self.max_failures):
                logger.warning(f"🔄 {self.consecutive_failures} consecutive failures - restarting")
                self._restart_monitoring()
    
    def _should_monitoring_be_running(self) -> bool:
        """Check if monitoring should be running based on configuration."""
        if not self.config.get("monitoring_settings", {}).get("market_hours_only", True):
            return True  # Should always be running
        
        # Check market hours (simplified)
        current_time = datetime.now().time()
        current_weekday = datetime.now().weekday()
        
        # Skip weekends
        if current_weekday >= 5:
            return False
        
        # Check trading hours (4 AM to 8 PM ET, simplified)
        from datetime import time as dt_time
        return dt_time(4, 0) <= current_time <= dt_time(20, 0)
    
    def _start_monitoring(self):
        """Start TTM monitoring with configured settings."""
        try:
            settings = self.config.get("monitoring_settings", {})
            filters = self.config.get("filters", {})
            
            min_grade = settings.get("min_grade", "A")
            scan_interval = settings.get("scan_interval", 120)
            
            result = start_ttm_alerts(min_grade, scan_interval, filters)
            logger.info(f"✅ TTM monitoring started: {min_grade}+ every {scan_interval//60}min")
            
        except Exception as e:
            logger.error(f"❌ Failed to start monitoring: {e}")
            raise
    
    def _restart_monitoring(self):
        """Restart TTM monitoring."""
        try:
            self.stats["total_restarts"] += 1
            self.stats["last_restart"] = datetime.now()
            
            logger.info("🔄 Restarting TTM monitoring...")
            
            # Stop current monitoring
            try:
                stop_ttm_alerts()
                time.sleep(2)  # Brief pause
            except Exception as e:
                logger.warning(f"Error stopping monitoring: {e}")
            
            # Wait restart delay
            if self.restart_delay > 0:
                logger.info(f"⏳ Waiting {self.restart_delay} seconds before restart...")
                time.sleep(self.restart_delay)
            
            # Start monitoring again
            self._start_monitoring()
            
            # Reset failure counter
            self.consecutive_failures = 0
            
            logger.info("✅ TTM monitoring restarted successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to restart monitoring: {e}")
            # Don't reset failure counter on restart failure
    
    def get_service_status(self) -> Dict[str, Any]:
        """Get comprehensive service status."""
        return {
            "is_running": self.is_running,
            "service_start_time": self.stats["service_start_time"].isoformat() if self.stats["service_start_time"] else None,
            "uptime_seconds": self.stats["uptime_seconds"],
            "total_restarts": self.stats["total_restarts"],
            "last_restart": self.stats["last_restart"].isoformat() if self.stats["last_restart"] else None,
            "health_checks": self.stats["health_checks"],
            "health_failures": self.stats["health_failures"],
            "consecutive_failures": self.consecutive_failures,
            "last_health_check": self.last_health_check.isoformat() if self.last_health_check else None,
            "auto_restart": self.auto_restart,
            "should_be_monitoring": self._should_monitoring_be_running(),
            "config": self.config
        }
    
    def update_config(self, new_config: Dict[str, Any]):
        """Update service configuration."""
        self.config.update(new_config)
        self._save_config()
        logger.info("✅ Service configuration updated")


# Global service instance
_service = TTMBackgroundService()


def start_background_service() -> bool:
    """Start the TTM background service."""
    return _service.start_service()


def stop_background_service() -> bool:
    """Stop the TTM background service."""
    return _service.stop_service()


def get_service_status() -> Dict[str, Any]:
    """Get background service status."""
    return _service.get_service_status()


def update_service_config(config: Dict[str, Any]):
    """Update background service configuration."""
    _service.update_config(config)


def main():
    """Run the background service."""
    if not ALERT_SYSTEM_AVAILABLE:
        print("❌ TTM Alert System not available!")
        return
    
    print("🚀 Starting TTM Background Service...")
    
    try:
        if start_background_service():
            print("✅ Service started successfully")
            print("Press Ctrl+C to stop the service")
            
            # Keep running until interrupted
            while True:
                time.sleep(1)
                
    except KeyboardInterrupt:
        print("\n⏹️ Stopping service...")
        stop_background_service()
        print("✅ Service stopped")
    except Exception as e:
        print(f"❌ Service error: {e}")


if __name__ == "__main__":
    main()
