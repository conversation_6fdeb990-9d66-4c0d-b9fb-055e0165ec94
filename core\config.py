"""Configuration and environment management for Ultimate Trading Expert.

This module centralizes:
1. Loading of environment variables (supports a local `.env` file via python-dotenv).
2. Validation that mandatory API keys are present.
3. Simple helper accessors used by other modules.

All key names are UPPER_SNAKE_CASE to stay consistent across the codebase.
"""
from __future__ import annotations

import os
import sys
from pathlib import Path
from typing import Dict, List

from dotenv import load_dotenv

# Load environment variables from config files
# Try multiple locations for the .env file
env_paths = [
    Path(__file__).parent.parent / "config" / "config.env",  # config/config.env
    Path(__file__).parent.parent / ".env",                   # root .env
    Path(__file__).with_suffix(".env"),                      # core/config.env
]

for env_path in env_paths:
    if env_path.exists():
        load_dotenv(dotenv_path=env_path, override=False)
        break

# -- Mandatory keys required for program start -----------------------------
MANDATORY_KEYS: List[str] = [
    "FMP_API_KEY",      # Financial Modeling Prep
    "ALPACA_API_KEY",   # Alpaca Markets (paper or live)
    "ALPACA_API_SECRET",  # Alpaca secret key
    "OPENAI_API_KEY",   # OpenAI for intent parsing / explanations
]

class MissingAPIKeyError(EnvironmentError):
    """Raised when one or more mandatory API keys are not found."""


def _validate_env() -> None:
    """Ensure all required keys exist in the environment.

    Exits the program gracefully with a helpful message if any are missing.
    """
    missing: List[str] = [k for k in MANDATORY_KEYS if not os.getenv(k)]
    if missing:
        _pretty_exit(
            "\n❌  One or more API keys are missing: " + ", ".join(missing) +
            "\n   ➜ Please set them as environment variables or in a local .env file."
        )


def _pretty_exit(message: str, code: int = 1) -> None:
    """Display a human-friendly error then exit."""
    print(message)
    sys.exit(code)


# Validate as soon as the module is imported.
_validate_env()


def get_api_key(name: str) -> str:
    """Public accessor for API keys.

    Parameters
    ----------
    name : str
        The exact environment variable name.

    Returns
    -------
    str
        The key value.

    Raises
    ------
    KeyError
        If the key is not found.
    """
    value = os.getenv(name)
    if value is None:
        raise KeyError(f"Environment variable '{name}' is not set.")
    return value


def dump_env_details() -> Dict[str, str]:
    """Return a minimal (non-secret) snapshot for logging/health checks."""
    return {
        "python_version": sys.version.split()[0],
        "cwd": str(Path.cwd()),
        "has_dotenv": str(Path(__file__).with_suffix(".env").exists()),
    }


# ---------------------------------------------------------------------------
# Helper utilities (import-light)


def _mask(value: str, visible: int = 4) -> str:  # noqa: D401 – small util
    """Return a partially-redacted version of an API key for safe logging.

    Shows the first `visible` characters and the last 2; everything else
    replaced with * characters. If the key is shorter than `visible+2`, the
    whole key is replaced by ****.
    """
    if not value or len(value) <= visible + 2:
        return "****"
    return value[:visible] + "*" * (len(value) - visible - 2) + value[-2:]


def public_keys_summary() -> Dict[str, str]:
    """Return a dict of mandatory keys with values safely masked."""
    summary: Dict[str, str] = {}
    for key in MANDATORY_KEYS:
        val = os.getenv(key, "")
        summary[key] = _mask(val)
    return summary 