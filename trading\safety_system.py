"""Safety System – runtime risk checks and circuit breakers."""
from __future__ import annotations

from typing import Dict

from logger_util import warning

MAX_DAILY_LOSS = 0.05  # 5% of account equity
MAX_POSITION_PCT = 0.10  # 10% of account equity per position


def pre_trade_check(account: Dict, order_value: float) -> None:
    """Run pre-trade sanity checks.

    Currently validates position size vs equity.
    """
    equity = float(account.get("equity", 0))
    if not equity:
        warning("Unable to determine account equity – skipping size check")
        return

    if order_value / equity > MAX_POSITION_PCT:
        raise ValueError("Order value exceeds position limit (10% equity)") 