#!/usr/bin/env python3
"""
Alpaca API Client with proper endpoint handling for Paper Trading and Options.
Based on official Alpaca documentation requirements.
"""

import requests
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dotenv import load_dotenv
import logging

# Load environment variables - override existing system variables
load_dotenv("config.env", override=True)

class AlpacaClient:
    """Alpaca API client with proper endpoint and authentication handling."""

    def __init__(self):
        """Initialize Alpaca client with proper configuration."""
        self.api_key = os.getenv("ALPACA_API_KEY")
        self.secret_key = os.getenv("ALPACA_API_SECRET")
        self.account_type = os.getenv("ALPACA_ACCOUNT_TYPE", "paper")

        # Set base URLs based on account type
        if self.account_type == "paper":
            self.trading_base_url = os.getenv("ALPACA_TRADING_BASE_URL", "https://paper-api.alpaca.markets")
            self.data_base_url = os.getenv("ALPACA_DATA_BASE_URL", "https://data.alpaca.markets")
        else:
            self.trading_base_url = "https://api.alpaca.markets"
            self.data_base_url = "https://data.alpaca.markets"

        # Common headers for all requests
        self.headers = {
            "accept": "application/json",
            "APCA-API-KEY-ID": self.api_key,
            "APCA-API-SECRET-KEY": self.secret_key
        }

        self.logger = logging.getLogger(__name__)

    def _make_request(self, method: str, url: str, params: Optional[Dict] = None,
                     data: Optional[Dict] = None) -> Dict[str, Any]:
        """Make HTTP request with proper error handling."""
        try:
            response = requests.request(
                method=method,
                url=url,
                headers=self.headers,
                params=params,
                json=data,
                timeout=30
            )

            if response.status_code == 200:
                return response.json()
            elif response.status_code == 403:
                raise Exception(f"403 Forbidden: Check account permissions and options trading approval. Response: {response.text}")
            elif response.status_code == 404:
                raise Exception(f"404 Not Found: Endpoint may not exist or symbol not found. Response: {response.text}")
            else:
                raise Exception(f"HTTP {response.status_code}: {response.text}")

        except requests.exceptions.RequestException as e:
            raise Exception(f"Request failed: {e}")

    def get_account(self) -> Dict[str, Any]:
        """Get account information."""
        url = f"{self.trading_base_url}/v2/account"
        return self._make_request("GET", url)

    def get_account_config(self) -> Dict[str, Any]:
        """Get account configuration including options trading level."""
        url = f"{self.trading_base_url}/v2/account/configurations"
        return self._make_request("GET", url)

    def get_assets_with_options(self) -> List[Dict[str, Any]]:
        """Get assets that have options trading enabled."""
        url = f"{self.trading_base_url}/v2/assets"
        params = {
            "status": "active",
            "asset_class": "us_equity",
            "attributes": "options_enabled"
        }
        return self._make_request("GET", url, params=params)

    def get_option_contracts(self, underlying_symbols: List[str],
                           expiration_date_gte: Optional[str] = None,
                           expiration_date_lte: Optional[str] = None,
                           limit: int = 100) -> Dict[str, Any]:
        """
        Get option contracts for given underlying symbols.

        Args:
            underlying_symbols: List of underlying stock symbols
            expiration_date_gte: Minimum expiration date (YYYY-MM-DD)
            expiration_date_lte: Maximum expiration date (YYYY-MM-DD)
            limit: Maximum number of contracts to return
        """
        url = f"{self.trading_base_url}/v2/options/contracts"
        params = {
            "underlying_symbols": ",".join(underlying_symbols),
            "limit": limit
        }

        if expiration_date_gte:
            params["expiration_date_gte"] = expiration_date_gte
        if expiration_date_lte:
            params["expiration_date_lte"] = expiration_date_lte

        return self._make_request("GET", url, params=params)

    def get_option_snapshots(self, symbols: List[str]) -> Dict[str, Any]:
        """Get real-time option snapshots."""
        url = f"{self.data_base_url}/v1beta1/options/snapshots"
        params = {"symbols": ",".join(symbols)}
        return self._make_request("GET", url, params=params)

    def get_option_bars(self, symbols: List[str], timeframe: str = "1Day",
                       start: Optional[str] = None, end: Optional[str] = None,
                       limit: int = 1000) -> Dict[str, Any]:
        """
        Get historical option bars.

        Args:
            symbols: List of option symbols
            timeframe: Bar timeframe (1Min, 5Min, 15Min, 30Min, 1Hour, 1Day)
            start: Start date (YYYY-MM-DD or YYYY-MM-DDTHH:MM:SSZ)
            end: End date (YYYY-MM-DD or YYYY-MM-DDTHH:MM:SSZ)
            limit: Maximum number of bars
        """
        url = f"{self.data_base_url}/v1beta1/options/bars"
        params = {
            "symbols": ",".join(symbols),
            "timeframe": timeframe,
            "limit": limit
        }

        if start:
            params["start"] = start
        if end:
            params["end"] = end

        return self._make_request("GET", url, params=params)

    def get_stock_quotes(self, symbols: List[str]) -> Dict[str, Any]:
        """Get latest stock quotes."""
        url = f"{self.data_base_url}/v2/stocks/quotes/latest"
        params = {"symbols": ",".join(symbols)}
        return self._make_request("GET", url, params=params)

    def test_connection(self) -> Dict[str, Any]:
        """Test API connection and return account status."""
        try:
            account = self.get_account()
            config = self.get_account_config()

            return {
                "success": True,
                "account_id": account.get("id"),
                "account_status": account.get("status"),
                "trading_blocked": account.get("trading_blocked"),
                "options_approved": account.get("options_approved", False),
                "options_trading_level": account.get("options_trading_level", 0),
                "max_options_trading_level": config.get("max_options_trading_level", 0),
                "account_type": self.account_type,
                "trading_url": self.trading_base_url,
                "data_url": self.data_base_url
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "account_type": self.account_type,
                "trading_url": self.trading_base_url,
                "data_url": self.data_base_url
            }

def test_alpaca_setup():
    """Test the Alpaca setup and display results."""
    print("🧪 Testing Alpaca API Setup")
    print("=" * 60)

    client = AlpacaClient()
    result = client.test_connection()

    if result["success"]:
        print("✅ Connection successful!")
        print(f"   Account ID: {result['account_id']}")
        print(f"   Account Status: {result['account_status']}")
        print(f"   Trading Blocked: {result['trading_blocked']}")
        print(f"   Options Approved: {result['options_approved']}")
        print(f"   Options Trading Level: {result['options_trading_level']}")
        print(f"   Max Options Level: {result['max_options_trading_level']}")
        print(f"   Account Type: {result['account_type']}")
        print(f"   Trading URL: {result['trading_url']}")
        print(f"   Data URL: {result['data_url']}")

        # Test options-enabled assets
        try:
            print(f"\n📊 Testing Options-Enabled Assets...")
            assets = client.get_assets_with_options()
            print(f"   Found {len(assets)} assets with options enabled")
            if assets:
                sample_symbols = [asset["symbol"] for asset in assets[:3]]
                print(f"   Sample symbols: {', '.join(sample_symbols)}")
        except Exception as e:
            print(f"   ❌ Assets test failed: {e}")

        # Test option contracts
        try:
            print(f"\n📈 Testing Option Contracts...")
            contracts = client.get_option_contracts(["AAPL"], limit=5)
            if "option_contracts" in contracts:
                print(f"   Found {len(contracts['option_contracts'])} AAPL option contracts")
                if contracts["option_contracts"]:
                    sample = contracts["option_contracts"][0]
                    print(f"   Sample: {sample['symbol']} - {sample['name']}")
            else:
                print(f"   No option contracts found")
        except Exception as e:
            print(f"   ❌ Option contracts test failed: {e}")

    else:
        print("❌ Connection failed!")
        print(f"   Error: {result['error']}")
        print(f"   Account Type: {result['account_type']}")
        print(f"   Trading URL: {result['trading_url']}")
        print(f"   Data URL: {result['data_url']}")

        print(f"\n💡 Troubleshooting Tips:")
        print(f"   1. Verify your API credentials are correct")
        print(f"   2. Check if options trading is enabled in your Alpaca dashboard")
        print(f"   3. Ensure you have the required market data subscription")
        print(f"   4. For live accounts, verify options trading approval level")

if __name__ == "__main__":
    test_alpaca_setup()
