#!/usr/bin/env python3
"""
TotalRecall + <PERSON> Integration Setup

This script sets up <PERSON> to work with your alpaca-mcp-server,
giving you natural language trading capabilities alongside your existing TotalRecall system.
"""

import os
import json
from pathlib import Path

def main():
    """Main setup function."""
    print("🤖 TOTALRECALL + CLAUDE DESKTOP INTEGRATION")
    print("=" * 60)
    
    # Step 1: Locate MCP server
    print("\n📍 Step 1: Locating alpaca-mcp-server...")
    
    # Check integrations directory
    integrations_path = Path("integrations") / "alpaca-mcp-server"
    downloads_path = Path.home() / "Downloads" / "alpaca-mcp-server-main"
    
    mcp_server_path = None
    if integrations_path.exists() and (integrations_path / "alpaca_mcp_server.py").exists():
        mcp_server_path = integrations_path
        print(f"✅ Found MCP server at: {mcp_server_path}")
    elif downloads_path.exists() and (downloads_path / "alpaca_mcp_server.py").exists():
        mcp_server_path = downloads_path
        print(f"✅ Found MCP server at: {mcp_server_path}")
    else:
        print("❌ alpaca-mcp-server not found!")
        return False
    
    # Step 2: Get API credentials
    print("\n🔑 Step 2: Getting Alpaca API credentials...")
    
    api_key = "PKTD043BAZB9PBVJ5OI7"  # From your .env file
    secret_key = None
    
    # Try to get secret key from .env
    try:
        env_file = Path(".env")
        if env_file.exists():
            with open(env_file, 'r') as f:
                content = f.read()
                for line in content.split('\n'):
                    if 'ALPACA_API_SECRET' in line or 'ALPACA_SECRET' in line:
                        secret_key = line.split('=')[1].strip()
                        break
    except Exception as e:
        print(f"⚠️ Could not read .env file: {e}")
    
    if not secret_key:
        print("🔑 Please enter your Alpaca Secret Key:")
        secret_key = input("Alpaca Secret Key: ").strip()
    
    if not api_key or not secret_key:
        print("❌ Both API key and secret key are required")
        return False
    
    print("✅ API credentials configured")
    
    # Step 3: Create Claude config
    print("\n🔧 Step 3: Creating Claude Desktop configuration...")
    
    try:
        # Create Claude config
        config = {
            "mcpServers": {
                "alpaca-totalrecall": {
                    "command": "python",
                    "args": [str(mcp_server_path / "alpaca_mcp_server.py")],
                    "env": {
                        "ALPACA_API_KEY": api_key,
                        "ALPACA_SECRET_KEY": secret_key,
                        "PAPER": "true"
                    }
                }
            }
        }
        
        # Save config
        config_file = Path("integrations/claude_desktop_config.json")
        config_file.parent.mkdir(exist_ok=True)
        
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"✅ Claude config created: {config_file}")
        
        # Create .env for MCP server
        env_file = mcp_server_path / ".env"
        env_content = f"""ALPACA_API_KEY = "{api_key}"
ALPACA_SECRET_KEY = "{secret_key}"
PAPER = true
"""
        
        with open(env_file, 'w') as f:
            f.write(env_content)
        
        print("✅ MCP server environment configured")
        
    except Exception as e:
        print(f"❌ Configuration failed: {e}")
        return False
    
    # Step 4: Instructions
    print("\n📚 Step 4: Setup Instructions")
    print("\n📋 **CLAUDE DESKTOP SETUP:**")
    print("\n1️⃣ **Install Claude Desktop:**")
    print("   • Download: https://claude.ai/download")
    print("   • Install and create account")
    
    print("\n2️⃣ **Configure Claude:**")
    print("   • Open Claude Desktop")
    print("   • Settings → Developer → Edit Config")
    print("   • Copy from: integrations/claude_desktop_config.json")
    print("   • Paste and save")
    
    print("\n3️⃣ **Restart Claude:**")
    print("   • Close Claude completely")
    print("   • Reopen Claude")
    print("   • Look for 'alpaca-totalrecall' connection")
    
    print("\n4️⃣ **Test:**")
    print("   • 'What's my account balance?'")
    print("   • 'Show my positions'")
    print("   • 'Get AAPL quote'")
    
    print("\n🎯 **NATURAL LANGUAGE EXAMPLES:**")
    print("   💬 'Buy 10 shares of AAPL at market'")
    print("   💬 'What are the Greeks for TSLA options?'")
    print("   💬 'Show upcoming earnings'")
    print("   💬 'Create watchlist with tech stocks'")
    print("   💬 'Analyze my portfolio risk'")
    
    print("\n🎉 **SETUP COMPLETE!**")
    print("\n🚀 **You Now Have:**")
    print("✅ Enhanced TotalRecall chat")
    print("✅ Claude Desktop natural language trading")
    print("✅ Ultimate trading setup!")
    
    return True

if __name__ == "__main__":
    main()
