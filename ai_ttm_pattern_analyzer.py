#!/usr/bin/env python3
"""
AI TTM Pattern Analyzer
Analyzes training data to identify statistical patterns and sweet spots
for dynamic, adaptive filtering instead of rigid thresholds.
"""

import pandas as pd
import numpy as np
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class TTMPatternAnalyzer:
    """Analyzes training data to identify success patterns."""
    
    def __init__(self, dataset_path="ttm_training_dataset.csv"):
        self.dataset_path = dataset_path
        self.patterns = {}
        
    def load_and_analyze(self):
        """Load dataset and analyze success patterns."""
        print("📊 Loading training dataset for pattern analysis...")
        
        df = pd.read_csv(self.dataset_path)
        print(f"✅ Loaded {len(df):,} samples")
        
        # Separate winners and losers
        winners = df[df['label'] == 1]
        losers = df[df['label'] == 0]
        
        print(f"🏆 Winners: {len(winners):,} ({len(winners)/len(df)*100:.1f}%)")
        print(f"❌ Losers: {len(losers):,} ({len(losers)/len(df)*100:.1f}%)")
        
        return df, winners, losers
    
    def analyze_feature_distributions(self, df, winners, losers):
        """Analyze statistical distributions for each feature."""
        print("\n🔍 ANALYZING FEATURE DISTRIBUTIONS...")
        print("=" * 60)
        
        # Key features to analyze
        features = [
            'volume_ratio', 'volatility_ratio', 'rsi', 'momentum_histogram',
            'macd', 'macd_histogram', 'atr'
        ]
        
        patterns = {}
        
        for feature in features:
            if feature not in df.columns:
                continue
                
            # Calculate percentiles for winners
            winner_data = winners[feature].dropna()
            loser_data = losers[feature].dropna()
            
            if len(winner_data) == 0:
                continue
            
            # Winner distribution analysis
            winner_percentiles = {
                'p10': np.percentile(winner_data, 10),
                'p25': np.percentile(winner_data, 25),
                'p50': np.percentile(winner_data, 50),
                'p75': np.percentile(winner_data, 75),
                'p90': np.percentile(winner_data, 90),
                'mean': winner_data.mean(),
                'std': winner_data.std()
            }
            
            # Loser distribution analysis
            loser_percentiles = {
                'p10': np.percentile(loser_data, 10),
                'p25': np.percentile(loser_data, 25),
                'p50': np.percentile(loser_data, 50),
                'p75': np.percentile(loser_data, 75),
                'p90': np.percentile(loser_data, 90),
                'mean': loser_data.mean(),
                'std': loser_data.std()
            }
            
            # Identify "sweet spot" ranges where winners concentrate
            sweet_spot_low = winner_percentiles['p25']
            sweet_spot_high = winner_percentiles['p75']
            optimal_range = winner_percentiles['p50']
            
            patterns[feature] = {
                'winner_stats': winner_percentiles,
                'loser_stats': loser_percentiles,
                'sweet_spot_range': [sweet_spot_low, sweet_spot_high],
                'optimal_value': optimal_range,
                'winner_concentration': self.calculate_concentration_score(
                    winner_data, sweet_spot_low, sweet_spot_high
                )
            }
            
            print(f"📈 {feature.upper()}:")
            print(f"   Winner Sweet Spot: {sweet_spot_low:.3f} - {sweet_spot_high:.3f}")
            print(f"   Winner Optimal: {optimal_range:.3f}")
            print(f"   Concentration Score: {patterns[feature]['winner_concentration']:.1%}")
        
        return patterns
    
    def calculate_concentration_score(self, data, low, high):
        """Calculate what percentage of winners fall in the sweet spot range."""
        in_range = ((data >= low) & (data <= high)).sum()
        return in_range / len(data) if len(data) > 0 else 0
    
    def analyze_boolean_patterns(self, df, winners, losers):
        """Analyze boolean feature patterns."""
        print("\n🔍 ANALYZING BOOLEAN PATTERNS...")
        print("=" * 60)
        
        boolean_features = [
            'ttm_squeeze_active', 'ema5_above_ema8', 'ema8_above_ema21',
            'price_above_ema8', 'price_above_ema21', 'last_3_candles_green',
            'high_volume', 'momentum_rising', 'histogram_rising_3'
        ]
        
        boolean_patterns = {}
        
        for feature in boolean_features:
            if feature not in df.columns:
                continue
            
            # Calculate win rates for each boolean state
            feature_true_wins = winners[winners[feature] == 1]
            feature_false_wins = winners[winners[feature] == 0]
            feature_true_total = df[df[feature] == 1]
            feature_false_total = df[df[feature] == 0]
            
            true_win_rate = len(feature_true_wins) / len(feature_true_total) if len(feature_true_total) > 0 else 0
            false_win_rate = len(feature_false_wins) / len(feature_false_total) if len(feature_false_total) > 0 else 0
            
            boolean_patterns[feature] = {
                'true_win_rate': true_win_rate,
                'false_win_rate': false_win_rate,
                'preference': 'true' if true_win_rate > false_win_rate else 'false',
                'strength': abs(true_win_rate - false_win_rate)
            }
            
            print(f"🔘 {feature.upper()}:")
            print(f"   True: {true_win_rate:.1%} win rate")
            print(f"   False: {false_win_rate:.1%} win rate")
            print(f"   Preference: {boolean_patterns[feature]['preference']} (strength: {boolean_patterns[feature]['strength']:.1%})")
        
        return boolean_patterns
    
    def create_composite_scoring_weights(self, patterns, boolean_patterns):
        """Create weights for composite scoring based on feature importance."""
        print("\n🎯 CREATING COMPOSITE SCORING WEIGHTS...")
        print("=" * 60)
        
        # Load feature importance from trained model
        try:
            importance_df = pd.read_csv("ttm_feature_importance.csv")
            importance_dict = dict(zip(importance_df['feature'], importance_df['importance']))
        except:
            print("⚠️ Feature importance file not found, using equal weights")
            importance_dict = {}
        
        # Create scoring weights
        scoring_weights = {
            'continuous_features': {},
            'boolean_features': {},
            'total_weight_continuous': 0,
            'total_weight_boolean': 0
        }
        
        # Weight continuous features by importance and concentration
        for feature, pattern in patterns.items():
            importance = importance_dict.get(feature, 0.1)
            concentration = pattern['winner_concentration']
            weight = importance * concentration
            scoring_weights['continuous_features'][feature] = weight
            scoring_weights['total_weight_continuous'] += weight
        
        # Weight boolean features by importance and strength
        for feature, pattern in boolean_patterns.items():
            importance = importance_dict.get(feature, 0.1)
            strength = pattern['strength']
            weight = importance * strength
            scoring_weights['boolean_features'][feature] = weight
            scoring_weights['total_weight_boolean'] += weight
        
        # Normalize weights
        if scoring_weights['total_weight_continuous'] > 0:
            for feature in scoring_weights['continuous_features']:
                scoring_weights['continuous_features'][feature] /= scoring_weights['total_weight_continuous']
        
        if scoring_weights['total_weight_boolean'] > 0:
            for feature in scoring_weights['boolean_features']:
                scoring_weights['boolean_features'][feature] /= scoring_weights['total_weight_boolean']
        
        print("📊 Continuous Feature Weights:")
        for feature, weight in scoring_weights['continuous_features'].items():
            print(f"   {feature}: {weight:.3f}")
        
        print("📊 Boolean Feature Weights:")
        for feature, weight in scoring_weights['boolean_features'].items():
            print(f"   {feature}: {weight:.3f}")
        
        return scoring_weights
    
    def save_patterns(self, patterns, boolean_patterns, scoring_weights):
        """Save pattern analysis results."""
        pattern_config = {
            'continuous_patterns': patterns,
            'boolean_patterns': boolean_patterns,
            'scoring_weights': scoring_weights,
            'generated_at': datetime.now().isoformat(),
            'description': 'Dynamic pattern-based filtering configuration'
        }
        
        with open('ttm_pattern_config.json', 'w') as f:
            json.dump(pattern_config, f, indent=2)
        
        print(f"\n💾 Pattern configuration saved to: ttm_pattern_config.json")
        return pattern_config

def main():
    """Main pattern analysis execution."""
    print("🔍 TTM PATTERN ANALYZER - DYNAMIC FILTERING")
    print("=" * 60)
    
    analyzer = TTMPatternAnalyzer()
    
    try:
        # Load and analyze data
        df, winners, losers = analyzer.load_and_analyze()
        
        # Analyze feature distributions
        patterns = analyzer.analyze_feature_distributions(df, winners, losers)
        
        # Analyze boolean patterns
        boolean_patterns = analyzer.analyze_boolean_patterns(df, winners, losers)
        
        # Create composite scoring weights
        scoring_weights = analyzer.create_composite_scoring_weights(patterns, boolean_patterns)
        
        # Save pattern configuration
        pattern_config = analyzer.save_patterns(patterns, boolean_patterns, scoring_weights)
        
        print(f"\n🎉 PATTERN ANALYSIS COMPLETE!")
        print(f"🚀 Ready for dynamic, pattern-based filtering!")
        
    except FileNotFoundError:
        print("❌ Training dataset not found!")
        print("🔧 Run ai_ttm_dataset_builder.py first")
    except Exception as e:
        print(f"❌ Analysis failed: {e}")

if __name__ == "__main__":
    main()
