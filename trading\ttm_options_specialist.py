"""TTM Squeeze & Options Trading Specialist

This system is specifically designed to be the ultimate expert in:
1. TTM Squeeze analysis, detection, and trading strategies
2. All forms of options trading with complete mathematical models
3. Options pricing equations (Black-Scholes, Greeks, etc.)
4. Advanced options strategies and risk management
5. TTM Squeeze + Options combination strategies

The goal is to be the world's best TTM Squeeze and Options trading assistant.
"""
from __future__ import annotations

import math
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from scipy.stats import norm
import requests

from config import get_api_key
from logger_util import info, warning

# Import existing TTM capabilities
from ttm_squeeze_watchlist import TTMSqueezeWatchlist
from proper_ttm_squeeze_scanner import run_proper_ttm_scan
# from beginner_ttm_watchlist import run_beginner_watchlist  # Archived


class TTMOptionsSpecialist:
    """Ultimate specialist in TTM Squeeze and Options Trading."""
    
    def __init__(self):
        self.api_key = get_api_key('FMP_API_KEY')
        self.ttm_watchlist = TTMSqueezeWatchlist()
        
        # Options pricing models and Greeks
        self.risk_free_rate = 0.05  # 5% default risk-free rate
        
    # ==================== TTM SQUEEZE EXPERTISE ====================
    
    def explain_ttm_squeeze_theory(self) -> str:
        """Complete explanation of TTM Squeeze theory and mechanics."""
        return """
🎯 TTM SQUEEZE THEORY - COMPLETE GUIDE

📊 WHAT IS TTM SQUEEZE?
The TTM (Trade The Markets) Squeeze is a volatility-based indicator that identifies periods when price action is "squeezed" between Bollinger Bands and Keltner Channels, indicating low volatility that often precedes explosive moves.

🔧 MATHEMATICAL COMPONENTS:

1. BOLLINGER BANDS (20-period):
   • Middle Band = 20-period Simple Moving Average
   • Upper Band = SMA + (2 × Standard Deviation)
   • Lower Band = SMA - (2 × Standard Deviation)

2. KELTNER CHANNELS (20-period):
   • Middle Line = 20-period Exponential Moving Average
   • Upper Channel = EMA + (1.5 × Average True Range)
   • Lower Channel = EMA - (1.5 × Average True Range)

3. SQUEEZE CONDITIONS:
   • IN SQUEEZE: Bollinger Bands inside Keltner Channels
   • OUT OF SQUEEZE: Bollinger Bands outside Keltner Channels

4. MOMENTUM OSCILLATOR:
   • Linear Regression of (Close - Average of High/Low)
   • Length: 20 periods
   • Color coding: Green (up momentum), Red (down momentum)

🎯 TRADING SIGNALS:

ENTRY SIGNALS:
• Squeeze Release: First bar where Bollinger Bands move outside Keltner Channels
• Momentum Confirmation: Momentum oscillator changes color in direction of breakout
• Volume Confirmation: Above-average volume on breakout

EXIT SIGNALS:
• Momentum Divergence: Price makes new high/low but momentum doesn't
• Squeeze Re-entry: Bollinger Bands move back inside Keltner Channels
• Opposite momentum color change

🚀 ADVANCED TTM STRATEGIES:

1. PRE-SQUEEZE POSITIONING:
   • Identify stocks building pressure (long squeeze duration)
   • Enter before breakout with tight stops
   • Scale in as squeeze tightens

2. MULTI-TIMEFRAME ANALYSIS:
   • 5min: Entry timing
   • 15min: Trend confirmation
   • 1hour: Overall direction

3. VOLUME-WEIGHTED SQUEEZE:
   • Higher volume during squeeze = stronger breakout potential
   • Volume expansion on release = higher probability trade

4. SQUEEZE GRADING SYSTEM:
   • A+ Grade: Long squeeze (10+ periods) + high volume + momentum alignment
   • A Grade: Medium squeeze (5-10 periods) + good volume
   • B Grade: Short squeeze (3-5 periods) + average volume
   • C Grade and below: Lower probability setups
"""
    
    def analyze_ttm_squeeze_setup(self, symbol: str, timeframe: str = "15min") -> Dict:
        """Comprehensive TTM Squeeze analysis for a specific symbol."""
        try:
            # Get price data
            url = f"https://financialmodelingprep.com/api/v3/historical-chart/{timeframe}/{symbol}?apikey={self.api_key}"
            response = requests.get(url, timeout=15)
            data = response.json()
            
            if not data or len(data) < 50:
                return {"error": "Insufficient data for analysis"}
            
            # Calculate TTM Squeeze components
            prices = [float(bar['close']) for bar in data[:50]]
            highs = [float(bar['high']) for bar in data[:50]]
            lows = [float(bar['low']) for bar in data[:50]]
            volumes = [float(bar['volume']) for bar in data[:50]]
            
            # Bollinger Bands
            sma_20 = sum(prices[:20]) / 20
            std_dev = np.std(prices[:20])
            bb_upper = sma_20 + (2 * std_dev)
            bb_lower = sma_20 - (2 * std_dev)
            
            # Keltner Channels (simplified)
            ema_20 = prices[0]  # Simplified for demo
            atr = sum([(h - l) for h, l in zip(highs[:20], lows[:20])]) / 20
            kc_upper = ema_20 + (1.5 * atr)
            kc_lower = ema_20 - (1.5 * atr)
            
            # Squeeze detection
            in_squeeze = bb_upper < kc_upper and bb_lower > kc_lower
            
            # Momentum calculation (simplified)
            momentum = prices[0] - ((highs[0] + lows[0]) / 2)
            
            # Volume analysis
            avg_volume = sum(volumes[:20]) / 20
            current_volume = volumes[0]
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 0
            
            return {
                "symbol": symbol,
                "timeframe": timeframe,
                "current_price": prices[0],
                "in_squeeze": in_squeeze,
                "squeeze_status": "IN SQUEEZE" if in_squeeze else "OUT OF SQUEEZE",
                "bollinger_bands": {
                    "upper": round(bb_upper, 2),
                    "middle": round(sma_20, 2),
                    "lower": round(bb_lower, 2)
                },
                "keltner_channels": {
                    "upper": round(kc_upper, 2),
                    "middle": round(ema_20, 2),
                    "lower": round(kc_lower, 2)
                },
                "momentum": round(momentum, 4),
                "momentum_direction": "BULLISH" if momentum > 0 else "BEARISH",
                "volume_analysis": {
                    "current": int(current_volume),
                    "average": int(avg_volume),
                    "ratio": round(volume_ratio, 2),
                    "status": "HIGH" if volume_ratio > 1.5 else "NORMAL"
                },
                "trading_recommendation": self._get_ttm_recommendation(in_squeeze, momentum, volume_ratio)
            }
            
        except Exception as e:
            return {"error": f"Analysis failed: {str(e)}"}
    
    def _get_ttm_recommendation(self, in_squeeze: bool, momentum: float, volume_ratio: float) -> Dict:
        """Generate trading recommendation based on TTM analysis."""
        if in_squeeze:
            if volume_ratio > 1.2:
                return {
                    "action": "WATCH CLOSELY",
                    "reason": "In squeeze with elevated volume - breakout imminent",
                    "strategy": "Prepare for breakout trade with tight stops"
                }
            else:
                return {
                    "action": "MONITOR",
                    "reason": "In squeeze but volume normal - wait for volume expansion",
                    "strategy": "Add to watchlist, wait for volume confirmation"
                }
        else:
            if momentum > 0 and volume_ratio > 1.5:
                return {
                    "action": "BUY SIGNAL",
                    "reason": "Bullish breakout with volume confirmation",
                    "strategy": "Enter long position with stop below recent low"
                }
            elif momentum < 0 and volume_ratio > 1.5:
                return {
                    "action": "SELL SIGNAL",
                    "reason": "Bearish breakout with volume confirmation",
                    "strategy": "Enter short position with stop above recent high"
                }
            else:
                return {
                    "action": "NO SIGNAL",
                    "reason": "Out of squeeze but no clear momentum/volume confirmation",
                    "strategy": "Wait for better setup"
                }
    
    # ==================== OPTIONS TRADING EXPERTISE ====================
    
    def black_scholes_price(self, S: float, K: float, T: float, r: float, sigma: float, option_type: str = "call") -> float:
        """
        Calculate Black-Scholes option price.
        
        Parameters:
        S: Current stock price
        K: Strike price
        T: Time to expiration (in years)
        r: Risk-free rate
        sigma: Volatility (annualized)
        option_type: "call" or "put"
        """
        try:
            d1 = (math.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * math.sqrt(T))
            d2 = d1 - sigma * math.sqrt(T)
            
            if option_type.lower() == "call":
                price = S * norm.cdf(d1) - K * math.exp(-r * T) * norm.cdf(d2)
            else:  # put
                price = K * math.exp(-r * T) * norm.cdf(-d2) - S * norm.cdf(-d1)
            
            return round(price, 2)
            
        except Exception as e:
            warning(f"Black-Scholes calculation error: {e}")
            return 0.0
    
    def calculate_greeks(self, S: float, K: float, T: float, r: float, sigma: float, option_type: str = "call") -> Dict:
        """
        Calculate all option Greeks.
        
        Returns: Dictionary with Delta, Gamma, Theta, Vega, Rho
        """
        try:
            d1 = (math.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * math.sqrt(T))
            d2 = d1 - sigma * math.sqrt(T)
            
            # Delta
            if option_type.lower() == "call":
                delta = norm.cdf(d1)
            else:
                delta = norm.cdf(d1) - 1
            
            # Gamma (same for calls and puts)
            gamma = norm.pdf(d1) / (S * sigma * math.sqrt(T))
            
            # Theta
            if option_type.lower() == "call":
                theta = (-S * norm.pdf(d1) * sigma / (2 * math.sqrt(T)) 
                        - r * K * math.exp(-r * T) * norm.cdf(d2)) / 365
            else:
                theta = (-S * norm.pdf(d1) * sigma / (2 * math.sqrt(T)) 
                        + r * K * math.exp(-r * T) * norm.cdf(-d2)) / 365
            
            # Vega (same for calls and puts)
            vega = S * norm.pdf(d1) * math.sqrt(T) / 100
            
            # Rho
            if option_type.lower() == "call":
                rho = K * T * math.exp(-r * T) * norm.cdf(d2) / 100
            else:
                rho = -K * T * math.exp(-r * T) * norm.cdf(-d2) / 100
            
            return {
                "delta": round(delta, 4),
                "gamma": round(gamma, 4),
                "theta": round(theta, 4),
                "vega": round(vega, 4),
                "rho": round(rho, 4)
            }
            
        except Exception as e:
            warning(f"Greeks calculation error: {e}")
            return {"error": str(e)}
    
    def analyze_options_strategy(self, strategy_name: str, underlying_price: float, **kwargs) -> Dict:
        """
        Analyze specific options strategies with P&L calculations.
        
        Supported strategies:
        - long_call, long_put, short_call, short_put
        - bull_call_spread, bear_put_spread
        - iron_condor, butterfly_spread
        - straddle, strangle
        - covered_call, protective_put
        """
        
        strategies = {
            "long_call": self._long_call_analysis,
            "long_put": self._long_put_analysis,
            "bull_call_spread": self._bull_call_spread_analysis,
            "bear_put_spread": self._bear_put_spread_analysis,
            "iron_condor": self._iron_condor_analysis,
            "straddle": self._straddle_analysis,
            "strangle": self._strangle_analysis,
            "covered_call": self._covered_call_analysis
        }
        
        if strategy_name.lower() in strategies:
            return strategies[strategy_name.lower()](underlying_price, **kwargs)
        else:
            return {"error": f"Strategy '{strategy_name}' not supported"}
    
    def _long_call_analysis(self, S: float, **kwargs) -> Dict:
        """Analyze long call strategy."""
        K = kwargs.get('strike', S * 1.05)  # 5% OTM default
        T = kwargs.get('dte', 30) / 365  # 30 days default
        sigma = kwargs.get('iv', 0.25)  # 25% IV default

        option_price = self.black_scholes_price(S, K, T, self.risk_free_rate, sigma, "call")
        greeks = self.calculate_greeks(S, K, T, self.risk_free_rate, sigma, "call")

        # P&L analysis at different price levels
        price_levels = [S * i for i in [0.8, 0.9, 0.95, 1.0, 1.05, 1.1, 1.2]]
        pnl_analysis = []

        for price in price_levels:
            intrinsic = max(0, price - K)
            pnl = intrinsic - option_price
            pnl_analysis.append({
                "stock_price": round(price, 2),
                "option_value": round(intrinsic, 2),
                "pnl": round(pnl, 2),
                "pnl_percent": round((pnl / option_price) * 100, 1) if option_price > 0 else 0
            })

        return {
            "strategy": "Long Call",
            "current_stock_price": S,
            "strike_price": K,
            "option_price": option_price,
            "breakeven": round(K + option_price, 2),
            "max_profit": "Unlimited",
            "max_loss": option_price,
            "greeks": greeks,
            "pnl_analysis": pnl_analysis,
            "best_case": "Stock price rises significantly above strike",
            "worst_case": "Stock price stays below strike at expiration"
        }

    def _long_put_analysis(self, S: float, **kwargs) -> Dict:
        """Analyze long put strategy."""
        K = kwargs.get('strike', S * 0.95)  # 5% OTM default
        T = kwargs.get('dte', 30) / 365
        sigma = kwargs.get('iv', 0.25)

        option_price = self.black_scholes_price(S, K, T, self.risk_free_rate, sigma, "put")
        greeks = self.calculate_greeks(S, K, T, self.risk_free_rate, sigma, "put")

        return {
            "strategy": "Long Put",
            "current_stock_price": S,
            "strike_price": K,
            "option_price": option_price,
            "breakeven": round(K - option_price, 2),
            "max_profit": round(K - option_price, 2),
            "max_loss": option_price,
            "greeks": greeks
        }

    def _bull_call_spread_analysis(self, S: float, **kwargs) -> Dict:
        """Analyze bull call spread strategy."""
        K1 = kwargs.get('long_strike', S * 1.02)  # Long call strike
        K2 = kwargs.get('short_strike', S * 1.08)  # Short call strike
        T = kwargs.get('dte', 30) / 365
        sigma = kwargs.get('iv', 0.25)

        long_call_price = self.black_scholes_price(S, K1, T, self.risk_free_rate, sigma, "call")
        short_call_price = self.black_scholes_price(S, K2, T, self.risk_free_rate, sigma, "call")

        net_debit = long_call_price - short_call_price
        max_profit = (K2 - K1) - net_debit

        return {
            "strategy": "Bull Call Spread",
            "long_strike": K1,
            "short_strike": K2,
            "net_debit": round(net_debit, 2),
            "max_profit": round(max_profit, 2),
            "max_loss": round(net_debit, 2),
            "breakeven": round(K1 + net_debit, 2),
            "profit_probability": "Moderate - profits if stock rises moderately"
        }

    def _bear_put_spread_analysis(self, S: float, **kwargs) -> Dict:
        """Analyze bear put spread strategy."""
        K1 = kwargs.get('long_strike', S * 0.98)  # Long put strike
        K2 = kwargs.get('short_strike', S * 0.92)  # Short put strike
        T = kwargs.get('dte', 30) / 365
        sigma = kwargs.get('iv', 0.25)

        long_put_price = self.black_scholes_price(S, K1, T, self.risk_free_rate, sigma, "put")
        short_put_price = self.black_scholes_price(S, K2, T, self.risk_free_rate, sigma, "put")

        net_debit = long_put_price - short_put_price
        max_profit = (K1 - K2) - net_debit

        return {
            "strategy": "Bear Put Spread",
            "long_strike": K1,
            "short_strike": K2,
            "net_debit": round(net_debit, 2),
            "max_profit": round(max_profit, 2),
            "max_loss": round(net_debit, 2),
            "breakeven": round(K1 - net_debit, 2)
        }

    def _straddle_analysis(self, S: float, **kwargs) -> Dict:
        """Analyze long straddle strategy."""
        K = kwargs.get('strike', S)  # ATM straddle
        T = kwargs.get('dte', 30) / 365
        sigma = kwargs.get('iv', 0.25)

        call_price = self.black_scholes_price(S, K, T, self.risk_free_rate, sigma, "call")
        put_price = self.black_scholes_price(S, K, T, self.risk_free_rate, sigma, "put")

        total_cost = call_price + put_price

        return {
            "strategy": "Long Straddle",
            "strike_price": K,
            "call_price": call_price,
            "put_price": put_price,
            "total_cost": round(total_cost, 2),
            "upper_breakeven": round(K + total_cost, 2),
            "lower_breakeven": round(K - total_cost, 2),
            "max_profit": "Unlimited",
            "max_loss": round(total_cost, 2),
            "best_for": "High volatility expected, direction unknown"
        }

    def _strangle_analysis(self, S: float, **kwargs) -> Dict:
        """Analyze long strangle strategy."""
        call_strike = kwargs.get('call_strike', S * 1.05)
        put_strike = kwargs.get('put_strike', S * 0.95)
        T = kwargs.get('dte', 30) / 365
        sigma = kwargs.get('iv', 0.25)

        call_price = self.black_scholes_price(S, call_strike, T, self.risk_free_rate, sigma, "call")
        put_price = self.black_scholes_price(S, put_strike, T, self.risk_free_rate, sigma, "put")

        total_cost = call_price + put_price

        return {
            "strategy": "Long Strangle",
            "call_strike": call_strike,
            "put_strike": put_strike,
            "total_cost": round(total_cost, 2),
            "upper_breakeven": round(call_strike + total_cost, 2),
            "lower_breakeven": round(put_strike - total_cost, 2),
            "cheaper_than": "Straddle (wider breakeven range)"
        }

    def _iron_condor_analysis(self, S: float, **kwargs) -> Dict:
        """Analyze iron condor strategy."""
        # Default strikes: sell ATM, buy OTM
        short_put = kwargs.get('short_put', S * 0.97)
        long_put = kwargs.get('long_put', S * 0.93)
        short_call = kwargs.get('short_call', S * 1.03)
        long_call = kwargs.get('long_call', S * 1.07)
        T = kwargs.get('dte', 30) / 365
        sigma = kwargs.get('iv', 0.25)

        # Calculate all option prices
        sp_price = self.black_scholes_price(S, short_put, T, self.risk_free_rate, sigma, "put")
        lp_price = self.black_scholes_price(S, long_put, T, self.risk_free_rate, sigma, "put")
        sc_price = self.black_scholes_price(S, short_call, T, self.risk_free_rate, sigma, "call")
        lc_price = self.black_scholes_price(S, long_call, T, self.risk_free_rate, sigma, "call")

        net_credit = (sp_price + sc_price) - (lp_price + lc_price)
        max_loss = max((short_call - long_call), (short_put - long_put)) - net_credit

        return {
            "strategy": "Iron Condor",
            "net_credit": round(net_credit, 2),
            "max_profit": round(net_credit, 2),
            "max_loss": round(max_loss, 2),
            "profit_range": f"{short_put} to {short_call}",
            "best_for": "Low volatility, range-bound movement"
        }

    def _covered_call_analysis(self, S: float, **kwargs) -> Dict:
        """Analyze covered call strategy."""
        K = kwargs.get('strike', S * 1.05)  # 5% OTM call
        T = kwargs.get('dte', 30) / 365
        sigma = kwargs.get('iv', 0.25)
        shares = kwargs.get('shares', 100)

        call_price = self.black_scholes_price(S, K, T, self.risk_free_rate, sigma, "call")
        premium_income = call_price * shares

        return {
            "strategy": "Covered Call",
            "shares_owned": shares,
            "call_strike": K,
            "premium_received": round(premium_income, 2),
            "max_profit": round((K - S) * shares + premium_income, 2),
            "breakeven": round(S - call_price, 2),
            "income_yield": round((call_price / S) * 100, 2),
            "best_for": "Generate income on existing stock position"
        }
    
    def ttm_options_combo_strategy(self, symbol: str) -> Dict:
        """
        Combine TTM Squeeze analysis with options strategies.
        Recommend best options strategy based on TTM setup.
        """
        # Get TTM analysis
        ttm_analysis = self.analyze_ttm_squeeze_setup(symbol)
        
        if "error" in ttm_analysis:
            return ttm_analysis
        
        current_price = ttm_analysis["current_price"]
        in_squeeze = ttm_analysis["in_squeeze"]
        momentum = ttm_analysis["momentum"]
        
        # Recommend options strategy based on TTM setup
        if in_squeeze:
            # Low volatility - good for buying options
            if momentum > 0:
                strategy = "long_call"
                reason = "In squeeze with bullish momentum - expect upward breakout"
            elif momentum < 0:
                strategy = "long_put"
                reason = "In squeeze with bearish momentum - expect downward breakout"
            else:
                strategy = "straddle"
                reason = "In squeeze with neutral momentum - expect big move either direction"
        else:
            # Out of squeeze - higher volatility
            if momentum > 0:
                strategy = "bull_call_spread"
                reason = "Bullish breakout confirmed - use spread to reduce cost"
            elif momentum < 0:
                strategy = "bear_put_spread"
                reason = "Bearish breakout confirmed - use spread to reduce cost"
            else:
                strategy = "iron_condor"
                reason = "No clear direction - sell premium in range"
        
        # Get options analysis for recommended strategy
        options_analysis = self.analyze_options_strategy(strategy, current_price)
        
        return {
            "symbol": symbol,
            "ttm_analysis": ttm_analysis,
            "recommended_strategy": strategy,
            "strategy_reason": reason,
            "options_analysis": options_analysis,
            "combined_recommendation": {
                "entry_timing": "Enter when TTM momentum confirms direction",
                "risk_management": "Use TTM squeeze re-entry as exit signal",
                "profit_target": "Take profits on momentum divergence"
            }
        }


# Global instance
_specialist = None

def get_ttm_options_specialist() -> TTMOptionsSpecialist:
    """Get the global TTM Options specialist instance."""
    global _specialist
    if _specialist is None:
        _specialist = TTMOptionsSpecialist()
    return _specialist


def analyze_ttm_options_combo(symbol: str) -> str:
    """Analyze TTM Squeeze + Options combination for a symbol."""
    specialist = get_ttm_options_specialist()
    result = specialist.ttm_options_combo_strategy(symbol)
    
    if isinstance(result, dict):
        return f"TTM + Options Analysis for {symbol}:\n{result}"
    else:
        return str(result)


if __name__ == "__main__":
    print("🎯 TTM SQUEEZE & OPTIONS TRADING SPECIALIST")
    print("=" * 60)
    
    specialist = TTMOptionsSpecialist()
    
    # Test TTM theory explanation
    print("📚 TTM SQUEEZE THEORY:")
    print(specialist.explain_ttm_squeeze_theory())
    
    # Test options pricing
    print("\n💰 BLACK-SCHOLES EXAMPLE:")
    price = specialist.black_scholes_price(100, 105, 0.25, 0.05, 0.2, "call")
    print(f"Call option price: ${price}")
    
    # Test Greeks
    print("\n📊 GREEKS EXAMPLE:")
    greeks = specialist.calculate_greeks(100, 105, 0.25, 0.05, 0.2, "call")
    print(f"Greeks: {greeks}")
    
    print("\n🚀 System ready for TTM Squeeze and Options mastery!")
