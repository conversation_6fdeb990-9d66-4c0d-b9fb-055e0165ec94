# ⚙️ Configuration Guide

Complete setup and configuration guide for the Ultimate TTM Trading System with AI Self-Awareness.

## 🚀 **QUICK SETUP**

### **1. System Requirements**
- **Python 3.8+** (3.9+ recommended)
- **Windows 10/11, macOS 10.15+, or Linux**
- **4GB RAM minimum** (8GB+ recommended)
- **1GB free disk space**
- **Stable internet connection**

### **2. Installation**
```bash
# Clone or download the system
cd TTM-Trading-System

# Install dependencies
pip install -r config/requirements.txt

# Verify installation
python -c "import pandas, numpy, requests; print('Dependencies OK')"
```

### **3. API Key Configuration**
Create `config/config.json`:
```json
{
    "alpaca": {
        "api_key": "your_alpaca_api_key",
        "secret_key": "your_alpaca_secret_key",
        "base_url": "https://paper-api.alpaca.markets"
    },
    "fmp": {
        "api_key": "your_fmp_api_key"
    },
    "openai": {
        "api_key": "your_openai_api_key"
    }
}
```

### **4. Launch System**
```bash
python main.py
```

## 🔑 **API KEY SETUP**

### **Alpaca Markets (Required)**
1. **Sign up** at [alpaca.markets](https://alpaca.markets)
2. **Create API keys** in your dashboard
3. **Start with paper trading** (recommended)
4. **Add keys to config.json**

```json
{
    "alpaca": {
        "api_key": "PKTEST_...",
        "secret_key": "...",
        "base_url": "https://paper-api.alpaca.markets"
    }
}
```

**For Live Trading:**
```json
{
    "alpaca": {
        "api_key": "AKFZ_...",
        "secret_key": "...",
        "base_url": "https://api.alpaca.markets"
    }
}
```

### **Financial Modeling Prep (Required)**
1. **Sign up** at [financialmodelingprep.com](https://financialmodelingprep.com)
2. **Get free API key** (15+ years of data)
3. **Upgrade for real-time** (optional)

```json
{
    "fmp": {
        "api_key": "your_fmp_key"
    }
}
```

### **OpenAI (Optional)**
For enhanced AI features:
```json
{
    "openai": {
        "api_key": "sk-..."
    }
}
```

## ⚙️ **SYSTEM CONFIGURATION**

### **Trading Settings**
Create `config/trading_config.json`:
```json
{
    "risk_management": {
        "max_risk_per_trade": 2.0,
        "max_portfolio_risk": 10.0,
        "default_stop_loss": 3.0,
        "profit_target_ratio": 2.0,
        "position_sizing_method": "kelly_criterion"
    },
    
    "automation": {
        "auto_trading_enabled": false,
        "max_trades_per_day": 5,
        "min_confidence_score": 75.0,
        "min_grade": "B",
        "trading_hours_only": true
    },
    
    "scanning": {
        "default_symbols": ["AAPL", "TSLA", "NVDA", "MSFT", "GOOGL"],
        "scan_interval_minutes": 5,
        "min_volume": 1000000,
        "min_price": 10.0,
        "max_price": 1000.0
    },
    
    "ai_settings": {
        "confidence_threshold": 70.0,
        "learning_enabled": true,
        "memory_retention_days": 90,
        "explanation_detail_level": "detailed"
    }
}
```

### **Scanner Configuration**
Create `config/scanner_config.json`:
```json
{
    "ttm_squeeze": {
        "timeframes": ["5min", "15min", "1hour"],
        "lookback_periods": 20,
        "momentum_threshold": 0.1,
        "volume_confirmation": true,
        "min_squeeze_bars": 6
    },
    
    "confidence_scoring": {
        "technical_weight": 0.40,
        "sentiment_weight": 0.25,
        "options_weight": 0.20,
        "macro_weight": 0.10,
        "historical_weight": 0.05
    },
    
    "grading_system": {
        "A_plus_threshold": 90,
        "A_threshold": 80,
        "B_plus_threshold": 70,
        "B_threshold": 60,
        "C_plus_threshold": 50,
        "C_threshold": 40
    }
}
```

## 🧠 **AI CONFIGURATION**

### **AI Brain Settings**
Create `config/ai_config.json`:
```json
{
    "ai_brain": {
        "database_path": "data/ai_brain.db",
        "state_snapshot_interval": 300,
        "decision_log_retention": 1000,
        "memory_cleanup_days": 30,
        "explanation_style": "conversational"
    },
    
    "investment_judge": {
        "criteria_weights": {
            "ttm_squeeze": 0.30,
            "momentum": 0.25,
            "sentiment": 0.20,
            "volatility": 0.15,
            "risk_factors": 0.10
        },
        "verdict_threshold": 65.0,
        "confidence_levels": {
            "very_high": 85,
            "high": 75,
            "medium": 65,
            "low": 55
        }
    },
    
    "adaptive_learning": {
        "learning_rate": 0.1,
        "pattern_min_trades": 5,
        "confidence_adjustment_max": 10.0,
        "performance_window_days": 30
    }
}
```

### **Chat Interface Settings**
```json
{
    "chat_interface": {
        "response_style": "professional",
        "max_response_length": 2000,
        "include_emojis": true,
        "context_memory": 10,
        "command_timeout": 30
    },
    
    "natural_language": {
        "understanding_threshold": 0.7,
        "fallback_to_help": true,
        "suggestion_enabled": true,
        "conversation_logging": true
    }
}
```

## 📊 **PERFORMANCE CONFIGURATION**

### **Heatmap Settings**
```json
{
    "performance_heatmaps": {
        "default_timeframe": 30,
        "color_scheme": "RdYlBu_r",
        "interactive_charts": true,
        "export_formats": ["png", "html", "json"],
        "update_frequency": "real_time"
    },
    
    "analytics": {
        "performance_metrics": [
            "total_pnl",
            "win_rate",
            "sharpe_ratio",
            "max_drawdown",
            "profit_factor"
        ],
        "benchmark_symbol": "SPY",
        "risk_free_rate": 0.02
    }
}
```

### **Strategy Environment Settings**
```json
{
    "strategy_environment": {
        "market_regime_indicators": [
            "vix_level",
            "trend_strength",
            "volume_regime",
            "sector_rotation"
        ],
        "strategy_ranking_frequency": "hourly",
        "environment_score_weights": {
            "volatility": 0.30,
            "trend": 0.25,
            "volume": 0.20,
            "sentiment": 0.15,
            "calendar": 0.10
        }
    }
}
```

## 🛡️ **SECURITY CONFIGURATION**

### **API Security**
```json
{
    "security": {
        "api_key_encryption": true,
        "rate_limiting": {
            "alpaca_requests_per_minute": 200,
            "fmp_requests_per_minute": 300,
            "openai_requests_per_minute": 60
        },
        "request_timeout": 30,
        "retry_attempts": 3,
        "ssl_verification": true
    }
}
```

### **Trading Security**
```json
{
    "trading_security": {
        "confirmation_required": {
            "live_trading": true,
            "large_positions": true,
            "high_risk_trades": true
        },
        "position_limits": {
            "max_position_value": 50000,
            "max_positions": 10,
            "max_sector_exposure": 0.30
        },
        "emergency_stops": {
            "daily_loss_limit": 1000,
            "drawdown_limit": 0.10,
            "volatility_circuit_breaker": true
        }
    }
}
```

## 📁 **FILE STRUCTURE**

### **Configuration Files**
```
config/
├── config.json              # Main API configuration
├── trading_config.json      # Trading parameters
├── scanner_config.json      # Scanner settings
├── ai_config.json          # AI system settings
├── security_config.json    # Security settings
├── requirements.txt        # Python dependencies
└── config.env.template    # Environment template
```

### **Data Directories**
```
data/
├── ai_brain.db            # AI consciousness database
├── performance_heatmaps.db # Performance analytics
├── confidence_engine.db   # Confidence scoring data
├── strategy_ranking.db    # Strategy environment data
├── adaptive_learning.db   # Learning engine data
└── logs/                  # System logs
    ├── trading.log
    ├── ai_brain.log
    ├── scanner.log
    └── errors.log
```

## 🔧 **ENVIRONMENT VARIABLES**

### **Alternative Configuration**
Instead of JSON files, you can use environment variables:

```bash
# API Keys
export ALPACA_API_KEY="your_key"
export ALPACA_SECRET_KEY="your_secret"
export FMP_API_KEY="your_key"
export OPENAI_API_KEY="your_key"

# Trading Settings
export MAX_RISK_PER_TRADE="2.0"
export AUTO_TRADING_ENABLED="false"
export MIN_CONFIDENCE_SCORE="75.0"

# System Settings
export LOG_LEVEL="INFO"
export DATABASE_PATH="data/"
export BACKUP_ENABLED="true"
```

### **Docker Configuration**
For containerized deployment:
```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

ENV ALPACA_API_KEY=""
ENV FMP_API_KEY=""
ENV LOG_LEVEL="INFO"

CMD ["python", "main.py"]
```

## 🧪 **TESTING CONFIGURATION**

### **Test Settings**
Create `config/test_config.json`:
```json
{
    "testing": {
        "use_mock_data": true,
        "mock_account_size": 100000,
        "test_symbols": ["AAPL", "TSLA", "NVDA"],
        "simulation_speed": "1x",
        "test_duration_days": 30
    },
    
    "paper_trading": {
        "enabled": true,
        "starting_balance": 100000,
        "commission": 0.0,
        "slippage": 0.001
    }
}
```

## 📋 **CONFIGURATION VALIDATION**

### **Validation Script**
```python
# validate_config.py
import json
from pathlib import Path

def validate_configuration():
    """Validate all configuration files."""
    
    required_files = [
        "config/config.json",
        "config/trading_config.json"
    ]
    
    for file_path in required_files:
        if not Path(file_path).exists():
            print(f"❌ Missing: {file_path}")
            return False
        
        try:
            with open(file_path) as f:
                json.load(f)
            print(f"✅ Valid: {file_path}")
        except json.JSONDecodeError as e:
            print(f"❌ Invalid JSON in {file_path}: {e}")
            return False
    
    print("🎉 All configuration files valid!")
    return True

if __name__ == "__main__":
    validate_configuration()
```

## 🔄 **CONFIGURATION UPDATES**

### **Runtime Configuration Changes**
```python
from config import update_config, reload_config

# Update configuration at runtime
update_config("trading_config.json", {
    "risk_management.max_risk_per_trade": 1.5
})

# Reload configuration
reload_config()
```

### **Configuration Backup**
```python
from config import backup_config, restore_config

# Backup current configuration
backup_config("backups/config_backup_20240115.zip")

# Restore from backup
restore_config("backups/config_backup_20240115.zip")
```

## 🚨 **TROUBLESHOOTING**

### **Common Configuration Issues**

#### **API Connection Errors**
```bash
# Test API connections
python -c "
from config import test_api_connections
test_api_connections()
"
```

#### **Database Issues**
```bash
# Reset AI brain database
python -c "
from ai_self_awareness import AIBrain
brain = AIBrain()
brain.reset_database()
"
```

#### **Permission Errors**
```bash
# Fix file permissions (Linux/Mac)
chmod 755 main.py
chmod 644 config/*.json

# Create data directories
mkdir -p data logs backups
```

### **Configuration Reset**
```python
# reset_config.py
from config import reset_to_defaults

# Reset all configuration to defaults
reset_to_defaults()
print("Configuration reset to defaults")
```

---

**With proper configuration, your Ultimate TTM Trading System with AI Self-Awareness will be ready to dominate the markets with institutional-grade intelligence!** 🚀
