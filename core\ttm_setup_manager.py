#!/usr/bin/env python3
"""
TTM Setup Manager - Comprehensive storage and management of TTM squeeze setups
Stores, tracks, and displays all TTM setups found by the scanning system
"""

import sqlite3
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path

try:
    from core.logger_util import info, warning, error
except ImportError:
    def info(msg): print(f"ℹ️ {msg}")
    def warning(msg): print(f"⚠️ {msg}")
    def error(msg): print(f"❌ {msg}")


@dataclass
class TTMSetup:
    """Enhanced TTM Setup data structure."""
    id: Optional[int] = None
    symbol: str = ""
    timeframe: str = "15min"
    grade: str = "C"
    confidence: float = 0.0
    criteria_count: int = 0
    price: float = 0.0
    volume: float = 0.0
    entry_price: float = 0.0
    stop_loss: float = 0.0
    target_price: float = 0.0
    risk_reward_ratio: float = 0.0
    momentum_value: float = 0.0
    breakout_probability: float = 0.0
    scan_timestamp: datetime = None
    setup_key: str = ""
    is_new_setup: bool = True
    alert_sent: bool = False
    status: str = "ACTIVE"  # ACTIVE, TRIGGERED, EXPIRED, MANUAL_CLOSE
    outcome: Optional[str] = None  # WIN, LOSS, BREAKEVEN, PENDING
    actual_pnl: Optional[float] = None
    max_favorable: Optional[float] = None
    max_adverse: Optional[float] = None
    duration_hours: Optional[float] = None
    notes: str = ""
    sector: str = ""
    market_cap: float = 0.0
    
    def __post_init__(self):
        if self.scan_timestamp is None:
            self.scan_timestamp = datetime.now()
        if not self.setup_key:
            self.setup_key = f"{self.symbol}_{self.timeframe}_{int(self.scan_timestamp.timestamp())}"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON storage."""
        data = asdict(self)
        if self.scan_timestamp:
            data['scan_timestamp'] = self.scan_timestamp.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TTMSetup':
        """Create from dictionary."""
        if 'scan_timestamp' in data and isinstance(data['scan_timestamp'], str):
            data['scan_timestamp'] = datetime.fromisoformat(data['scan_timestamp'])
        return cls(**data)


class TTMSetupManager:
    """Comprehensive TTM Setup storage and management system."""
    
    def __init__(self, db_path: str = "data/ttm_setups.db"):
        self.db_path = db_path
        self._init_database()
        
        # Setup quality thresholds
        self.quality_thresholds = {
            "A+": {"min_criteria": 5, "min_confidence": 0.9, "min_rr": 2.0},
            "A": {"min_criteria": 4, "min_confidence": 0.8, "min_rr": 1.8},
            "B": {"min_criteria": 3, "min_confidence": 0.7, "min_rr": 1.5},
            "C": {"min_criteria": 2, "min_confidence": 0.6, "min_rr": 1.2},
            "D": {"min_criteria": 1, "min_confidence": 0.5, "min_rr": 1.0}
        }
    
    def _init_database(self):
        """Initialize the TTM setups database."""
        try:
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Main setups table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS ttm_setups (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        symbol TEXT NOT NULL,
                        timeframe TEXT NOT NULL,
                        grade TEXT NOT NULL,
                        confidence REAL NOT NULL,
                        criteria_count INTEGER NOT NULL,
                        price REAL NOT NULL,
                        volume REAL NOT NULL,
                        entry_price REAL NOT NULL,
                        stop_loss REAL NOT NULL,
                        target_price REAL NOT NULL,
                        risk_reward_ratio REAL NOT NULL,
                        momentum_value REAL NOT NULL,
                        breakout_probability REAL NOT NULL,
                        scan_timestamp TEXT NOT NULL,
                        setup_key TEXT NOT NULL UNIQUE,
                        is_new_setup BOOLEAN DEFAULT 1,
                        alert_sent BOOLEAN DEFAULT 0,
                        status TEXT DEFAULT 'ACTIVE',
                        outcome TEXT,
                        actual_pnl REAL,
                        max_favorable REAL,
                        max_adverse REAL,
                        duration_hours REAL,
                        notes TEXT DEFAULT '',
                        sector TEXT DEFAULT '',
                        market_cap REAL DEFAULT 0,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Performance tracking table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS setup_performance (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        setup_id INTEGER,
                        check_timestamp TEXT NOT NULL,
                        current_price REAL NOT NULL,
                        pnl_percent REAL NOT NULL,
                        status TEXT NOT NULL,
                        FOREIGN KEY (setup_id) REFERENCES ttm_setups (id)
                    )
                ''')
                
                # Create indexes for faster queries
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_symbol ON ttm_setups(symbol)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_grade ON ttm_setups(grade)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_timestamp ON ttm_setups(scan_timestamp)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_status ON ttm_setups(status)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_setup_key ON ttm_setups(setup_key)')
                
                conn.commit()
                info("✅ TTM Setup Manager database initialized")
                
        except Exception as e:
            error(f"Failed to initialize TTM Setup Manager database: {e}")
            raise
    
    def store_setup(self, setup_data: Dict[str, Any]) -> Optional[int]:
        """Store a new TTM setup."""
        try:
            # Create TTMSetup object
            setup = TTMSetup(
                symbol=setup_data.get('symbol', ''),
                timeframe=setup_data.get('timeframe', '15min'),
                grade=setup_data.get('grade', 'C'),
                confidence=setup_data.get('confidence', 0.0),
                criteria_count=setup_data.get('criteria_count', 0),
                price=setup_data.get('price', 0.0),
                volume=setup_data.get('volume', 0.0),
                entry_price=setup_data.get('entry_price', 0.0),
                stop_loss=setup_data.get('stop_loss', 0.0),
                target_price=setup_data.get('target_price', 0.0),
                momentum_value=setup_data.get('momentum_value', 0.0),
                breakout_probability=setup_data.get('breakout_probability', 0.0),
                sector=setup_data.get('sector', ''),
                market_cap=setup_data.get('market_cap', 0.0)
            )
            
            # Calculate risk/reward ratio
            if setup.entry_price > 0 and setup.stop_loss > 0 and setup.target_price > 0:
                risk = abs(setup.entry_price - setup.stop_loss)
                reward = abs(setup.target_price - setup.entry_price)
                setup.risk_reward_ratio = reward / risk if risk > 0 else 0.0
            
            # Check if setup already exists
            existing_id = self._get_setup_by_key(setup.setup_key)
            if existing_id:
                info(f"Setup {setup.setup_key} already exists, updating...")
                return self._update_setup(existing_id, setup)
            
            # Store new setup
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO ttm_setups (
                        symbol, timeframe, grade, confidence, criteria_count, price, volume,
                        entry_price, stop_loss, target_price, risk_reward_ratio, momentum_value,
                        breakout_probability, scan_timestamp, setup_key, is_new_setup, alert_sent,
                        sector, market_cap
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    setup.symbol, setup.timeframe, setup.grade, setup.confidence,
                    setup.criteria_count, setup.price, setup.volume, setup.entry_price,
                    setup.stop_loss, setup.target_price, setup.risk_reward_ratio,
                    setup.momentum_value, setup.breakout_probability,
                    setup.scan_timestamp.isoformat(), setup.setup_key, setup.is_new_setup,
                    setup.alert_sent, setup.sector, setup.market_cap
                ))
                
                setup_id = cursor.lastrowid
                conn.commit()
                
                info(f"✅ Stored TTM setup: {setup.symbol} Grade {setup.grade} (ID: {setup_id})")
                return setup_id
                
        except Exception as e:
            error(f"Failed to store TTM setup: {e}")
            return None
    
    def get_setups(self, filters: Dict[str, Any] = None, limit: int = 100) -> List[TTMSetup]:
        """Get TTM setups with optional filtering."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Build query with filters
                query = "SELECT * FROM ttm_setups WHERE 1=1"
                params = []
                
                if filters:
                    if 'min_grade' in filters:
                        grade_values = {"A+": 5, "A": 4, "B": 3, "C": 2, "D": 1}
                        min_grade_value = grade_values.get(filters['min_grade'], 1)
                        grade_conditions = []
                        for grade, value in grade_values.items():
                            if value >= min_grade_value:
                                grade_conditions.append(f"grade = '{grade}'")
                        if grade_conditions:
                            query += f" AND ({' OR '.join(grade_conditions)})"
                    
                    if 'symbol' in filters:
                        query += " AND symbol = ?"
                        params.append(filters['symbol'])
                    
                    if 'status' in filters:
                        query += " AND status = ?"
                        params.append(filters['status'])
                    
                    if 'min_confidence' in filters:
                        query += " AND confidence >= ?"
                        params.append(filters['min_confidence'])
                    
                    if 'hours_back' in filters:
                        cutoff_time = datetime.now() - timedelta(hours=filters['hours_back'])
                        query += " AND scan_timestamp >= ?"
                        params.append(cutoff_time.isoformat())
                
                query += " ORDER BY scan_timestamp DESC LIMIT ?"
                params.append(limit)
                
                cursor.execute(query, params)
                rows = cursor.fetchall()
                
                # Convert to TTMSetup objects
                setups = []
                columns = [desc[0] for desc in cursor.description]

                for row in rows:
                    setup_dict = dict(zip(columns, row))
                    setup_dict['scan_timestamp'] = datetime.fromisoformat(setup_dict['scan_timestamp'])

                    # Remove database-only fields that aren't in TTMSetup
                    db_only_fields = ['created_at', 'updated_at']
                    for field in db_only_fields:
                        setup_dict.pop(field, None)

                    setup = TTMSetup(**setup_dict)
                    setups.append(setup)
                
                return setups
                
        except Exception as e:
            error(f"Failed to get TTM setups: {e}")
            return []
    
    def get_setup_statistics(self, hours_back: int = 24) -> Dict[str, Any]:
        """Get comprehensive setup statistics."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cutoff_time = datetime.now() - timedelta(hours=hours_back)
                
                # Total setups
                cursor.execute("SELECT COUNT(*) FROM ttm_setups WHERE scan_timestamp >= ?", 
                             (cutoff_time.isoformat(),))
                total_setups = cursor.fetchone()[0]
                
                # Setups by grade
                cursor.execute("""
                    SELECT grade, COUNT(*) FROM ttm_setups 
                    WHERE scan_timestamp >= ? 
                    GROUP BY grade ORDER BY grade
                """, (cutoff_time.isoformat(),))
                grade_counts = dict(cursor.fetchall())
                
                # Active setups
                cursor.execute("SELECT COUNT(*) FROM ttm_setups WHERE status = 'ACTIVE'")
                active_setups = cursor.fetchone()[0]
                
                # Average confidence by grade
                cursor.execute("""
                    SELECT grade, AVG(confidence) FROM ttm_setups 
                    WHERE scan_timestamp >= ? 
                    GROUP BY grade
                """, (cutoff_time.isoformat(),))
                avg_confidence = dict(cursor.fetchall())
                
                # Top symbols
                cursor.execute("""
                    SELECT symbol, COUNT(*) FROM ttm_setups 
                    WHERE scan_timestamp >= ? 
                    GROUP BY symbol ORDER BY COUNT(*) DESC LIMIT 10
                """, (cutoff_time.isoformat(),))
                top_symbols = cursor.fetchall()
                
                return {
                    "total_setups": total_setups,
                    "active_setups": active_setups,
                    "grade_distribution": grade_counts,
                    "average_confidence": avg_confidence,
                    "top_symbols": top_symbols,
                    "hours_analyzed": hours_back,
                    "analysis_timestamp": datetime.now().isoformat()
                }
                
        except Exception as e:
            error(f"Failed to get setup statistics: {e}")
            return {}
    
    def _get_setup_by_key(self, setup_key: str) -> Optional[int]:
        """Get setup ID by setup key."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT id FROM ttm_setups WHERE setup_key = ?", (setup_key,))
                result = cursor.fetchone()
                return result[0] if result else None
        except Exception as e:
            warning(f"Failed to get setup by key: {e}")
            return None
    
    def _update_setup(self, setup_id: int, setup: TTMSetup) -> int:
        """Update existing setup."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE ttm_setups SET
                        confidence = ?, price = ?, volume = ?, momentum_value = ?,
                        breakout_probability = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (
                    setup.confidence, setup.price, setup.volume, setup.momentum_value,
                    setup.breakout_probability, setup_id
                ))
                conn.commit()
                return setup_id
        except Exception as e:
            error(f"Failed to update setup: {e}")
            return setup_id


# Global setup manager instance
_setup_manager = TTMSetupManager()


def get_setup_manager() -> TTMSetupManager:
    """Get the global setup manager instance."""
    return _setup_manager


def store_ttm_setup(setup_data: Dict[str, Any]) -> Optional[int]:
    """Store a TTM setup."""
    return _setup_manager.store_setup(setup_data)


def get_ttm_setups(filters: Dict[str, Any] = None, limit: int = 100) -> List[TTMSetup]:
    """Get TTM setups with filtering."""
    return _setup_manager.get_setups(filters, limit)


def get_ttm_setup_statistics(hours_back: int = 24) -> Dict[str, Any]:
    """Get TTM setup statistics."""
    return _setup_manager.get_setup_statistics(hours_back)
