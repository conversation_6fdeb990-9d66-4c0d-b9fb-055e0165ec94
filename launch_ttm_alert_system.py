#!/usr/bin/env python3
"""
TTM Alert System Launcher - Complete real-time monitoring solution
Launches the enhanced TTM squeeze monitoring system with notifications
"""

import sys
import os
import argparse
from pathlib import Path

# Add core directory to path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir / 'core'))
sys.path.insert(0, str(current_dir / 'scanners'))

def check_dependencies():
    """Check if all required dependencies are available."""
    missing_deps = []
    
    # Check core dependencies
    try:
        import tkinter
    except ImportError:
        missing_deps.append("tkinter (usually included with Python)")
    
    try:
        import requests
    except ImportError:
        missing_deps.append("requests")
    
    try:
        import pandas
    except ImportError:
        missing_deps.append("pandas")
    
    try:
        import numpy
    except ImportError:
        missing_deps.append("numpy")
    
    # Check optional dependencies
    optional_missing = []
    
    try:
        import plyer
    except ImportError:
        optional_missing.append("plyer (for desktop notifications)")
    
    try:
        import pyttsx3
    except ImportError:
        optional_missing.append("pyttsx3 (for voice alerts)")
    
    try:
        import pytz
    except ImportError:
        optional_missing.append("pytz (for market hours)")
    
    if missing_deps:
        print("❌ Missing required dependencies:")
        for dep in missing_deps:
            print(f"   • {dep}")
        print("\nInstall with: pip install " + " ".join([d.split()[0] for d in missing_deps]))
        return False
    
    if optional_missing:
        print("⚠️ Optional dependencies missing (features will be limited):")
        for dep in optional_missing:
            print(f"   • {dep}")
        print("\nInstall with: pip install " + " ".join([d.split()[0] for d in optional_missing]))
    
    return True


def launch_control_panel():
    """Launch the TTM Alert Control Panel GUI."""
    try:
        from core.ttm_alert_control_panel import main as control_panel_main
        print("🚀 Launching TTM Alert Control Panel...")
        control_panel_main()
    except ImportError as e:
        print(f"❌ Failed to import control panel: {e}")
        return False
    except Exception as e:
        print(f"❌ Error launching control panel: {e}")
        return False
    return True


def launch_background_service():
    """Launch the TTM Background Service."""
    try:
        from core.ttm_background_service import main as service_main
        print("🚀 Launching TTM Background Service...")
        service_main()
    except ImportError as e:
        print(f"❌ Failed to import background service: {e}")
        return False
    except Exception as e:
        print(f"❌ Error launching background service: {e}")
        return False
    return True


def quick_start_monitoring():
    """Quick start monitoring with default settings."""
    try:
        from core.ttm_alert_system import start_ttm_alerts, get_alert_status
        
        print("🚀 Quick Starting TTM Alert Monitoring...")
        print("📊 Using default settings: Grade A+, 2-minute intervals")
        
        # Start with default settings
        result = start_ttm_alerts(min_grade="A", scan_interval=120)
        print(result)
        
        # Show status
        status = get_alert_status()
        if status.get('is_running', False):
            print("\n✅ Monitoring started successfully!")
            print("🔔 You will receive alerts when high-grade TTM setups are found")
            print("\nPress Ctrl+C to stop monitoring")
            
            # Keep running
            import time
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                from core.ttm_alert_system import stop_ttm_alerts
                stop_ttm_alerts()
                print("\n⏹️ Monitoring stopped")
        else:
            print("❌ Failed to start monitoring")
            return False
            
    except ImportError as e:
        print(f"❌ Failed to import alert system: {e}")
        return False
    except Exception as e:
        print(f"❌ Error starting monitoring: {e}")
        return False
    return True


def show_system_status():
    """Show current system status."""
    try:
        from core.ttm_alert_system import get_alert_status, get_alert_statistics
        from core.ttm_background_service import get_service_status
        
        print("📊 TTM ALERT SYSTEM STATUS")
        print("=" * 50)
        
        # Alert system status
        alert_status = get_alert_status()
        print(f"\n🎯 ALERT MONITORING:")
        print(f"• Status: {'🟢 Running' if alert_status.get('is_running') else '🔴 Stopped'}")
        print(f"• Grade Filter: {alert_status.get('min_grade', 'N/A')}+")
        print(f"• Scan Interval: {alert_status.get('scan_interval', 0)//60} minutes")
        print(f"• Active Alerts: {alert_status.get('active_alerts', 0)}")
        
        # Statistics
        stats = get_alert_statistics()
        print(f"\n📈 STATISTICS:")
        print(f"• Total Scans: {stats.get('total_scans', 0):,}")
        print(f"• Alerts Sent: {stats.get('alerts_sent', 0):,}")
        print(f"• Success Rate: {stats.get('success_rate', 0):.1f}%")
        print(f"• Market Time: {'🟢 Yes' if stats.get('is_market_time') else '🔴 No'}")
        
        # Service status
        try:
            service_status = get_service_status()
            print(f"\n🔧 BACKGROUND SERVICE:")
            print(f"• Status: {'🟢 Running' if service_status.get('is_running') else '🔴 Stopped'}")
            print(f"• Uptime: {service_status.get('uptime_seconds', 0):.0f} seconds")
            print(f"• Restarts: {service_status.get('total_restarts', 0)}")
            print(f"• Health Checks: {service_status.get('health_checks', 0)}")
        except:
            print(f"\n🔧 BACKGROUND SERVICE: Not available")
        
    except ImportError as e:
        print(f"❌ Failed to import status modules: {e}")
    except Exception as e:
        print(f"❌ Error getting status: {e}")


def main():
    """Main launcher function."""
    parser = argparse.ArgumentParser(
        description="TTM Alert System - Real-time TTM squeeze monitoring",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python launch_ttm_alert_system.py --gui          # Launch control panel
  python launch_ttm_alert_system.py --service      # Launch background service  
  python launch_ttm_alert_system.py --quick        # Quick start monitoring
  python launch_ttm_alert_system.py --status       # Show system status
        """
    )
    
    parser.add_argument('--gui', action='store_true',
                       help='Launch the TTM Alert Control Panel GUI')
    parser.add_argument('--service', action='store_true',
                       help='Launch the background service')
    parser.add_argument('--quick', action='store_true',
                       help='Quick start monitoring with default settings')
    parser.add_argument('--status', action='store_true',
                       help='Show current system status')
    parser.add_argument('--check-deps', action='store_true',
                       help='Check dependencies')
    
    args = parser.parse_args()
    
    # Show banner
    print("""
🎯 TOTALRECALL TTM ALERT SYSTEM
================================
Real-time TTM Squeeze Monitoring with Comprehensive Notifications

Features:
• 🔍 Continuous scanning for TTM squeeze setups
• 🚨 Real-time desktop notifications & sound alerts  
• 📊 A-F grading system with 5-point criteria
• ⚙️ Comprehensive filtering options
• 🕐 Market hours detection
• 📈 Performance statistics & monitoring
• 🎛️ GUI control panel for easy management
""")
    
    # Check dependencies first
    if not check_dependencies():
        print("\n❌ Please install missing dependencies before continuing.")
        return 1
    
    # Handle arguments
    if args.check_deps:
        print("✅ All required dependencies are available!")
        return 0
    
    elif args.status:
        show_system_status()
        return 0
    
    elif args.gui:
        success = launch_control_panel()
        return 0 if success else 1
    
    elif args.service:
        success = launch_background_service()
        return 0 if success else 1
    
    elif args.quick:
        success = quick_start_monitoring()
        return 0 if success else 1
    
    else:
        # No arguments - show interactive menu
        print("🎛️ LAUNCH OPTIONS:")
        print("1. 🖥️  Launch Control Panel GUI (Recommended)")
        print("2. 🔧 Launch Background Service")
        print("3. ⚡ Quick Start Monitoring")
        print("4. 📊 Show System Status")
        print("5. ❌ Exit")
        
        while True:
            try:
                choice = input("\nSelect option (1-5): ").strip()
                
                if choice == '1':
                    return 0 if launch_control_panel() else 1
                elif choice == '2':
                    return 0 if launch_background_service() else 1
                elif choice == '3':
                    return 0 if quick_start_monitoring() else 1
                elif choice == '4':
                    show_system_status()
                    continue
                elif choice == '5':
                    print("👋 Goodbye!")
                    return 0
                else:
                    print("❌ Invalid choice. Please select 1-5.")
                    
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                return 0
            except Exception as e:
                print(f"❌ Error: {e}")
                return 1


if __name__ == "__main__":
    sys.exit(main())
