#!/usr/bin/env python3
"""
Simple Deep Search System
Lightweight version that works without complex dependencies
"""
import sqlite3
import json
import os
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import re
import logging

class SimpleDeepSearch:
    """
    Simple Deep Search for Trading Data
    
    Uses basic text search with SQLite FTS (Full Text Search)
    No complex embeddings required - just works!
    """
    
    def __init__(self, db_path: str = "data/simple_search.db"):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        self._setup_database()
    
    def _setup_database(self):
        """Setup SQLite database with FTS"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        with sqlite3.connect(self.db_path) as conn:
            # Create FTS table for fast text search
            conn.execute("""
                CREATE VIRTUAL TABLE IF NOT EXISTS search_index 
                USING fts5(
                    content,
                    metadata,
                    source,
                    timestamp,
                    symbol,
                    tags
                )
            """)
            
            # Create regular table for structured data
            conn.execute("""
                CREATE TABLE IF NOT EXISTS documents (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    content TEXT NOT NULL,
                    metadata TEXT NOT NULL,
                    source TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    symbol TEXT,
                    tags TEXT
                )
            """)
    
    def add_document(self, content: str, metadata: Dict[str, Any], source: str, symbol: str = "", tags: List[str] = None):
        """Add document to search index"""
        if not content.strip():
            return
        
        timestamp = datetime.now().isoformat()
        tags_str = ",".join(tags or [])
        metadata_str = json.dumps(metadata)
        
        with sqlite3.connect(self.db_path) as conn:
            # Add to regular table
            conn.execute("""
                INSERT INTO documents (content, metadata, source, timestamp, symbol, tags)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (content, metadata_str, source, timestamp, symbol, tags_str))
            
            # Add to FTS table
            conn.execute("""
                INSERT INTO search_index (content, metadata, source, timestamp, symbol, tags)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (content, metadata_str, source, timestamp, symbol, tags_str))
    
    def search(self, query: str, limit: int = 10, source_filter: str = None) -> List[Dict[str, Any]]:
        """Search documents"""
        try:
            # Clean query for FTS
            clean_query = self._clean_query(query)
            
            with sqlite3.connect(self.db_path) as conn:
                # Build FTS query
                if source_filter:
                    sql = """
                        SELECT content, metadata, source, timestamp, symbol, tags,
                               rank
                        FROM search_index 
                        WHERE search_index MATCH ? AND source = ?
                        ORDER BY rank
                        LIMIT ?
                    """
                    params = (clean_query, source_filter, limit)
                else:
                    sql = """
                        SELECT content, metadata, source, timestamp, symbol, tags,
                               rank
                        FROM search_index 
                        WHERE search_index MATCH ?
                        ORDER BY rank
                        LIMIT ?
                    """
                    params = (clean_query, limit)
                
                cursor = conn.execute(sql, params)
                results = []
                
                for row in cursor.fetchall():
                    content, metadata_str, source, timestamp, symbol, tags, rank = row
                    
                    try:
                        metadata = json.loads(metadata_str)
                    except:
                        metadata = {}
                    
                    results.append({
                        "content": content,
                        "metadata": metadata,
                        "source": source,
                        "timestamp": timestamp,
                        "symbol": symbol,
                        "tags": tags.split(",") if tags else [],
                        "score": float(rank) if rank else 0.0
                    })
                
                return results
                
        except Exception as e:
            self.logger.error(f"Search failed: {e}")
            return self._fallback_search(query, limit, source_filter)
    
    def _clean_query(self, query: str) -> str:
        """Clean query for FTS"""
        # Remove special characters that might break FTS
        clean = re.sub(r'[^\w\s]', ' ', query)
        # Split into words and join with OR
        words = clean.split()
        if len(words) > 1:
            return " OR ".join(f'"{word}"' for word in words if len(word) > 2)
        else:
            return f'"{words[0]}"' if words else '""'
    
    def _fallback_search(self, query: str, limit: int, source_filter: str) -> List[Dict[str, Any]]:
        """Fallback search using LIKE"""
        query_words = query.lower().split()
        
        with sqlite3.connect(self.db_path) as conn:
            where_conditions = []
            params = []
            
            # Add LIKE conditions for each word
            for word in query_words:
                where_conditions.append("LOWER(content) LIKE ?")
                params.append(f"%{word}%")
            
            if source_filter:
                where_conditions.append("source = ?")
                params.append(source_filter)
            
            where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
            params.append(limit)
            
            sql = f"""
                SELECT content, metadata, source, timestamp, symbol, tags
                FROM documents
                WHERE {where_clause}
                ORDER BY timestamp DESC
                LIMIT ?
            """
            
            cursor = conn.execute(sql, params)
            results = []
            
            for row in cursor.fetchall():
                content, metadata_str, source, timestamp, symbol, tags = row
                
                try:
                    metadata = json.loads(metadata_str)
                except:
                    metadata = {}
                
                # Simple relevance scoring
                score = sum(1 for word in query_words if word in content.lower()) / len(query_words)
                
                results.append({
                    "content": content,
                    "metadata": metadata,
                    "source": source,
                    "timestamp": timestamp,
                    "symbol": symbol,
                    "tags": tags.split(",") if tags else [],
                    "score": score
                })
            
            return sorted(results, key=lambda x: x["score"], reverse=True)
    
    def add_trade_decision(self, symbol: str, action: str, reasoning: str, metadata: Dict[str, Any]):
        """Add trade decision"""
        content = f"Trade Decision: {action} {symbol}\nReasoning: {reasoning}"
        tags = ["trade", "decision", action.lower(), symbol.lower()]
        self.add_document(content, metadata, "trade_decisions", symbol, tags)
    
    def add_scanner_result(self, symbol: str, grade: str, confidence: float, reasoning: str, metadata: Dict[str, Any]):
        """Add scanner result"""
        content = f"TTM Scanner: {symbol} Grade {grade} (Confidence: {confidence}%)\nAnalysis: {reasoning}"
        tags = ["scanner", "ttm", grade.lower(), symbol.lower()]
        self.add_document(content, metadata, "scanner_results", symbol, tags)
    
    def add_ai_insight(self, insight_type: str, content: str, metadata: Dict[str, Any], symbol: str = ""):
        """Add AI insight"""
        tags = ["ai", "insight", insight_type.lower()]
        if symbol:
            tags.append(symbol.lower())
        self.add_document(content, metadata, "ai_insights", symbol, tags)
    
    def add_chart_analysis(self, symbol: str, timeframe: str, analysis: str, metadata: Dict[str, Any]):
        """Add chart analysis"""
        content = f"Chart Analysis: {symbol} ({timeframe})\n{analysis}"
        tags = ["chart", "analysis", timeframe.lower(), symbol.lower()]
        self.add_document(content, metadata, "chart_analysis", symbol, tags)
    
    def search_by_symbol(self, symbol: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Search for all data related to a symbol"""
        return self.search(symbol, limit)
    
    def search_by_source(self, source: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Search within a specific source"""
        return self.search("*", limit, source)
    
    def get_recent_activity(self, days: int = 7) -> List[Dict[str, Any]]:
        """Get recent activity"""
        cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT content, metadata, source, timestamp, symbol, tags
                FROM documents
                WHERE timestamp >= ?
                ORDER BY timestamp DESC
                LIMIT 20
            """, (cutoff_date,))
            
            results = []
            for row in cursor.fetchall():
                content, metadata_str, source, timestamp, symbol, tags = row
                
                try:
                    metadata = json.loads(metadata_str)
                except:
                    metadata = {}
                
                results.append({
                    "content": content,
                    "metadata": metadata,
                    "source": source,
                    "timestamp": timestamp,
                    "symbol": symbol,
                    "tags": tags.split(",") if tags else [],
                    "score": 1.0
                })
            
            return results
    
    def get_stats(self) -> Dict[str, Any]:
        """Get search index statistics"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("SELECT COUNT(*) FROM documents")
            total_docs = cursor.fetchone()[0]
            
            cursor = conn.execute("""
                SELECT source, COUNT(*) 
                FROM documents 
                GROUP BY source
            """)
            by_source = dict(cursor.fetchall())
            
            cursor = conn.execute("""
                SELECT symbol, COUNT(*) 
                FROM documents 
                WHERE symbol != ''
                GROUP BY symbol
                ORDER BY COUNT(*) DESC
                LIMIT 10
            """)
            by_symbol = dict(cursor.fetchall())
        
        return {
            "total_documents": total_docs,
            "by_source": by_source,
            "by_symbol": by_symbol,
            "search_type": "SQLite FTS5"
        }

# Global instance
_simple_search = None

def get_simple_search() -> SimpleDeepSearch:
    """Get global simple search instance"""
    global _simple_search
    if _simple_search is None:
        _simple_search = SimpleDeepSearch()
    return _simple_search

def simple_search_query(query: str, limit: int = 5) -> str:
    """Simple search query with formatted response"""
    search = get_simple_search()
    results = search.search(query, limit)
    
    if not results:
        return f"🔍 No results found for: '{query}'"
    
    response = f"🔍 **Search Results for: '{query}'**\n\n"
    
    for i, result in enumerate(results, 1):
        content_preview = result["content"][:150] + "..." if len(result["content"]) > 150 else result["content"]
        
        response += f"**{i}. {result['source'].replace('_', ' ').title()}**"
        if result["symbol"]:
            response += f" ({result['symbol']})"
        response += f"\n{content_preview}\n"
        response += f"*{result['timestamp'][:16]}*\n\n"
    
    return response

def add_simple_search_data(content: str, metadata: Dict[str, Any], source: str, symbol: str = "", tags: List[str] = None):
    """Add data to simple search"""
    search = get_simple_search()
    search.add_document(content, metadata, source, symbol, tags)

if __name__ == "__main__":
    # Test the simple search
    search = SimpleDeepSearch()
    
    # Add test data
    search.add_trade_decision(
        "AAPL", "BUY",
        "Strong TTM squeeze with A+ grade",
        {"confidence": 92, "entry_price": 150.50}
    )
    
    search.add_scanner_result(
        "NVDA", "A", 88.5,
        "Volume surge with momentum confirmation",
        {"timeframe": "15min"}
    )
    
    # Test search
    results = search.search("AAPL trade")
    print(f"Found {len(results)} results")
    
    for result in results:
        print(f"Content: {result['content'][:100]}...")
        print(f"Source: {result['source']}")
        print("---")
    
    # Show stats
    stats = search.get_stats()
    print(f"Stats: {stats}")
