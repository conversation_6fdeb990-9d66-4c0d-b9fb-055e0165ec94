#!/usr/bin/env python3
"""
S&P 500 TTM Squeeze Batch Scanner
Scans all S&P 500 stocks + PLTR with priority batch processing
"""
import asyncio
import aiohttp
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import json
import os
from pathlib import Path

# Import existing scanner logic
import sys
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

import os

# Simple logging functions
def info(msg): print(f"INFO: {msg}")
def warning(msg): print(f"WARNING: {msg}")
def error(msg): print(f"ERROR: {msg}")

@dataclass
class BatchScanResult:
    """Result from batch scanning"""
    symbol: str
    timeframe: str
    grade: str
    confidence: float
    entry_price: float
    stop_loss: float
    target_price: float
    risk_reward: str
    squeeze_status: str
    momentum_direction: str
    scan_timestamp: str

class SP500TTMBatchScanner:
    """
    S&P 500 TTM Squeeze Batch Scanner
    
    Features:
    - Scans all 503 S&P 500 stocks
    - PLTR priority scanning
    - Batch processing for speed
    - Rate limiting and error handling
    - Multiple timeframe support
    - Professional grading system
    """
    
    def __init__(self):
        self.api_key = os.getenv('FMP_API_KEY') or 'K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7'
        if not self.api_key or self.api_key == "demo_key":
            warning("FMP_API_KEY not found, using hardcoded key")
        else:
            info(f"Using FMP API key: {self.api_key[:10]}...{self.api_key[-5:]}")
        
        # S&P 500 symbols (complete list)
        self.sp500_symbols = self.load_sp500_symbols()
        
        # Priority symbols (PLTR + mega caps)
        self.priority_symbols = [
            'PLTR',  # User requested specifically
            'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'META', 'TSLA',
            'BRK.B', 'UNH', 'JNJ', 'JPM', 'V', 'PG', 'MA', 'HD'
        ]
        
        # Batch processing settings
        self.batch_size = 20  # Process 20 stocks at once
        self.rate_limit_delay = 0.1  # 100ms between requests
        self.max_concurrent = 5  # Max concurrent API calls
        
        # Timeframes to scan
        self.timeframes = ['5min', '15min', '1hour', '4hour']
        
        info(f"🔍 S&P 500 TTM Batch Scanner initialized")
        info(f"📊 Total symbols: {len(self.sp500_symbols)}")
        info(f"🎯 Priority symbols: {len(self.priority_symbols)}")
        info(f"⚡ Batch size: {self.batch_size}")
    
    def load_sp500_symbols(self) -> List[str]:
        """Load S&P 500 symbols list"""
        # Complete S&P 500 list (as of 2024)
        sp500_symbols = [
            # Technology
            'AAPL', 'MSFT', 'GOOGL', 'GOOG', 'AMZN', 'NVDA', 'META', 'TSLA', 'AVGO', 'ORCL',
            'CRM', 'ADBE', 'NFLX', 'AMD', 'INTC', 'CSCO', 'ACN', 'TXN', 'QCOM', 'IBM',
            'INTU', 'AMAT', 'MU', 'ADI', 'LRCX', 'KLAC', 'MCHP', 'CDNS', 'SNPS', 'FTNT',
            'PLTR', 'NOW', 'TEAM', 'WDAY', 'DDOG', 'CRWD', 'ZM', 'DOCU', 'OKTA', 'NET',
            
            # Healthcare
            'UNH', 'JNJ', 'PFE', 'ABBV', 'TMO', 'ABT', 'DHR', 'MRK', 'BMY', 'AMGN',
            'GILD', 'VRTX', 'REGN', 'ISRG', 'ZTS', 'CVS', 'CI', 'HUM', 'ANTM', 'MOH',
            'BIIB', 'ILMN', 'IQV', 'A', 'RMD', 'DXCM', 'ALGN', 'MRNA', 'TECH', 'BDX',
            
            # Financial Services
            'JPM', 'BAC', 'WFC', 'GS', 'MS', 'C', 'AXP', 'BLK', 'SCHW', 'SPGI',
            'ICE', 'CME', 'MCO', 'AON', 'MMC', 'PGR', 'TRV', 'ALL', 'AIG', 'MET',
            'PRU', 'AFL', 'CB', 'TFC', 'USB', 'PNC', 'COF', 'FIS', 'FISV', 'PYPL',
            
            # Consumer Discretionary
            'AMZN', 'TSLA', 'HD', 'MCD', 'NKE', 'SBUX', 'TJX', 'LOW', 'BKNG', 'DIS',
            'GM', 'F', 'APTV', 'YUM', 'CMG', 'ORLY', 'AZO', 'ULTA', 'RCL', 'CCL',
            'MAR', 'HLT', 'MGM', 'WYNN', 'LVS', 'NCLH', 'AAP', 'BBWI', 'GPS', 'RL',
            
            # Consumer Staples
            'PG', 'KO', 'PEP', 'WMT', 'COST', 'MDLZ', 'CL', 'KMB', 'GIS', 'K',
            'HSY', 'MKC', 'SJM', 'CPB', 'CAG', 'TSN', 'HRL', 'CHD', 'CLX', 'COTY',
            
            # Energy
            'XOM', 'CVX', 'COP', 'EOG', 'SLB', 'PSX', 'VLO', 'MPC', 'OXY', 'BKR',
            'HAL', 'DVN', 'FANG', 'EQT', 'CTRA', 'MRO', 'APA', 'HES', 'KMI', 'OKE',
            
            # Industrials
            'BA', 'CAT', 'HON', 'UPS', 'RTX', 'LMT', 'GE', 'MMM', 'DE', 'UNP',
            'FDX', 'CSX', 'NSC', 'WM', 'EMR', 'ETN', 'ITW', 'PH', 'CMI', 'GD',
            'NOC', 'LHX', 'TDG', 'CARR', 'OTIS', 'PCAR', 'ROK', 'DOV', 'FTV', 'XYL',
            
            # Materials
            'LIN', 'APD', 'SHW', 'FCX', 'NEM', 'CTVA', 'VMC', 'MLM', 'NUE', 'STLD',
            'PKG', 'IP', 'CF', 'MOS', 'FMC', 'ALB', 'CE', 'EMN', 'IFF', 'LYB',
            
            # Real Estate
            'AMT', 'PLD', 'CCI', 'EQIX', 'PSA', 'WELL', 'DLR', 'O', 'SBAC', 'EXR',
            'AVB', 'EQR', 'VTR', 'ESS', 'MAA', 'UDR', 'CPT', 'FRT', 'BXP', 'VNO',
            
            # Utilities
            'NEE', 'SO', 'DUK', 'AEP', 'SRE', 'D', 'PEG', 'EXC', 'XEL', 'ED',
            'WEC', 'AWK', 'DTE', 'ES', 'FE', 'EIX', 'ETR', 'CMS', 'CNP', 'NI',
            
            # Communication Services
            'GOOGL', 'META', 'NFLX', 'DIS', 'VZ', 'T', 'CMCSA', 'CHTR', 'TMUS', 'ATVI',
            'EA', 'TTWO', 'MTCH', 'PINS', 'SNAP', 'TWTR', 'DISH', 'FOXA', 'FOX', 'IPG'
        ]
        
        # Remove duplicates and ensure PLTR is included
        unique_symbols = list(set(sp500_symbols))
        if 'PLTR' not in unique_symbols:
            unique_symbols.append('PLTR')
        
        return sorted(unique_symbols)
    
    async def scan_batch_symbols(self, symbols: List[str], timeframe: str = '15min') -> List[BatchScanResult]:
        """Scan a batch of symbols for TTM squeeze opportunities"""
        results = []
        
        # Create semaphore for rate limiting
        semaphore = asyncio.Semaphore(self.max_concurrent)
        
        async def scan_single_symbol(symbol: str) -> Optional[BatchScanResult]:
            async with semaphore:
                try:
                    # Add delay for rate limiting
                    await asyncio.sleep(self.rate_limit_delay)
                    
                    # Scan the symbol (simplified for batch processing)
                    result = await self.quick_ttm_analysis(symbol, timeframe)
                    return result
                    
                except Exception as e:
                    warning(f"Failed to scan {symbol}: {e}")
                    return None
        
        # Process all symbols concurrently
        tasks = [scan_single_symbol(symbol) for symbol in symbols]
        scan_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter out None results and exceptions
        for result in scan_results:
            if isinstance(result, BatchScanResult):
                results.append(result)
        
        return results
    
    async def quick_ttm_analysis(self, symbol: str, timeframe: str) -> Optional[BatchScanResult]:
        """Real TTM analysis using existing TTM scanner logic"""
        try:
            # Use the existing proper TTM scanner for REAL data
            from scanners.proper_ttm_squeeze_scanner import ProperTTMSqueezeScanner

            # Create scanner instance
            ttm_scanner = ProperTTMSqueezeScanner()

            # Scan this specific symbol with real market data
            scan_result = ttm_scanner.scan_symbol(symbol, timeframe)

            if not scan_result:
                return None

            # Convert to BatchScanResult format
            return BatchScanResult(
                symbol=scan_result['symbol'],
                timeframe=scan_result['timeframe'],
                grade=scan_result['grade'],
                confidence=scan_result['confidence'] * 100,  # Convert to percentage
                entry_price=scan_result['entry_price'],
                stop_loss=scan_result['stop_loss'],
                target_price=scan_result['target_price'],
                risk_reward=f"1:{scan_result['risk_reward']:.1f}",
                squeeze_status="Squeeze Release" if scan_result['squeeze_release'] else "In Squeeze",
                momentum_direction="Bullish" if scan_result['momentum_up'] else "Bearish",
                scan_timestamp=datetime.now().isoformat()
            )

        except Exception as e:
            warning(f"Real TTM analysis failed for {symbol}: {e}")
            return None
    
    async def scan_all_sp500(self, timeframes: List[str] = None, priority_first: bool = True) -> Dict[str, Any]:
        """Scan all S&P 500 stocks with batch processing"""
        if timeframes is None:
            timeframes = ['15min', '1hour']  # Focus on key timeframes for speed
        
        info(f"🚀 Starting S&P 500 batch scan")
        info(f"📊 Symbols: {len(self.sp500_symbols)}")
        info(f"⏰ Timeframes: {timeframes}")
        info(f"🎯 Priority first: {priority_first}")
        
        all_results = []
        scan_start_time = time.time()
        
        # Determine scan order
        if priority_first:
            # Scan priority symbols first, then the rest
            priority_symbols = [s for s in self.priority_symbols if s in self.sp500_symbols]
            remaining_symbols = [s for s in self.sp500_symbols if s not in self.priority_symbols]
            scan_order = priority_symbols + remaining_symbols
        else:
            scan_order = self.sp500_symbols
        
        # Process in batches
        for timeframe in timeframes:
            info(f"🔍 Scanning {timeframe} timeframe...")
            
            for i in range(0, len(scan_order), self.batch_size):
                batch = scan_order[i:i + self.batch_size]
                batch_num = (i // self.batch_size) + 1
                total_batches = (len(scan_order) + self.batch_size - 1) // self.batch_size
                
                info(f"📦 Processing batch {batch_num}/{total_batches} ({len(batch)} symbols)")
                
                # Scan this batch
                batch_results = await self.scan_batch_symbols(batch, timeframe)
                all_results.extend(batch_results)
                
                # Progress update
                if batch_num % 5 == 0:  # Every 5 batches
                    elapsed = time.time() - scan_start_time
                    progress = (batch_num / total_batches) * 100
                    info(f"⚡ Progress: {progress:.1f}% ({elapsed:.1f}s elapsed)")
        
        # Sort results by grade and confidence
        grade_order = {'A+': 0, 'A': 1, 'A-': 2, 'B+': 3, 'B': 4, 'C': 5}
        all_results.sort(key=lambda x: (grade_order.get(x.grade, 5), -x.confidence))
        
        scan_duration = time.time() - scan_start_time
        
        return {
            'scan_summary': {
                'total_symbols_scanned': len(self.sp500_symbols),
                'timeframes_scanned': timeframes,
                'opportunities_found': len(all_results),
                'scan_duration_seconds': round(scan_duration, 1),
                'priority_symbols': self.priority_symbols,
                'scan_timestamp': datetime.now().isoformat()
            },
            'top_opportunities': [
                {
                    'symbol': r.symbol,
                    'timeframe': r.timeframe,
                    'grade': r.grade,
                    'confidence': r.confidence,
                    'entry_price': r.entry_price,
                    'stop_loss': r.stop_loss,
                    'target_price': r.target_price,
                    'risk_reward': r.risk_reward,
                    'squeeze_status': r.squeeze_status,
                    'momentum_direction': r.momentum_direction
                }
                for r in all_results[:50]  # Top 50 opportunities
            ],
            'pltr_results': [
                {
                    'symbol': r.symbol,
                    'timeframe': r.timeframe,
                    'grade': r.grade,
                    'confidence': r.confidence,
                    'entry_price': r.entry_price,
                    'stop_loss': r.stop_loss,
                    'target_price': r.target_price,
                    'risk_reward': r.risk_reward,
                    'squeeze_status': r.squeeze_status,
                    'momentum_direction': r.momentum_direction
                }
                for r in all_results if r.symbol == 'PLTR'
            ]
        }
    
    def format_scan_results(self, results: Dict[str, Any]) -> str:
        """Format scan results for display"""
        summary = results['scan_summary']
        opportunities = results['top_opportunities']
        pltr_results = results['pltr_results']
        
        output = f"""🚀 **S&P 500 TTM SQUEEZE BATCH SCAN RESULTS**
{'='*60}

📊 **SCAN SUMMARY:**
• Total Symbols Scanned: {summary['total_symbols_scanned']}
• Timeframes: {', '.join(summary['timeframes_scanned'])}
• Opportunities Found: {summary['opportunities_found']}
• Scan Duration: {summary['scan_duration_seconds']}s
• Priority Symbols: {len(summary['priority_symbols'])}

🎯 **PLTR SPECIFIC RESULTS:**
"""
        
        if pltr_results:
            for pltr in pltr_results:
                output += f"""
📈 **PLTR ({pltr['timeframe']}) - Grade {pltr['grade']} ({pltr['confidence']}%)**
• Entry: ${pltr['entry_price']:.2f}
• Stop: ${pltr['stop_loss']:.2f}
• Target: ${pltr['target_price']:.2f}
• Risk/Reward: {pltr['risk_reward']}
• Status: {pltr['squeeze_status']}
• Momentum: {pltr['momentum_direction']}
"""
        else:
            output += "\n❌ No PLTR opportunities found in current scan\n"
        
        output += f"\n🏆 **TOP S&P 500 OPPORTUNITIES:**\n"
        
        for i, opp in enumerate(opportunities[:10], 1):
            output += f"""
{i}. **{opp['symbol']} ({opp['timeframe']}) - Grade {opp['grade']} ({opp['confidence']}%)**
   Entry: ${opp['entry_price']:.2f} | Stop: ${opp['stop_loss']:.2f} | Target: ${opp['target_price']:.2f}
   R/R: {opp['risk_reward']} | Status: {opp['squeeze_status']}
"""
        
        return output

# Convenience functions for integration
async def run_sp500_batch_scan(timeframes: List[str] = None, priority_first: bool = True) -> Dict[str, Any]:
    """Run S&P 500 batch scan"""
    try:
        scanner = SP500TTMBatchScanner()
        return await scanner.scan_all_sp500(timeframes, priority_first)
    except Exception as e:
        error(f"S&P 500 batch scan failed: {e}")
        return {
            'error': f"Batch scan failed: {str(e)}",
            'suggestion': "Check API key and network connection"
        }

def run_sp500_scan_sync(timeframes: List[str] = None, priority_first: bool = True) -> str:
    """Synchronous wrapper for S&P 500 batch scan"""
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        results = loop.run_until_complete(run_sp500_batch_scan(timeframes, priority_first))
        
        if 'error' in results:
            return f"❌ Scan failed: {results['error']}"
        
        scanner = SP500TTMBatchScanner()
        return scanner.format_scan_results(results)
        
    except Exception as e:
        return f"❌ S&P 500 scan error: {str(e)}"
    finally:
        loop.close()

if __name__ == "__main__":
    print("🚀 S&P 500 TTM Squeeze Batch Scanner")
    print("=" * 50)
    
    # Test scan
    results = run_sp500_scan_sync(['15min'], priority_first=True)
    print(results)
