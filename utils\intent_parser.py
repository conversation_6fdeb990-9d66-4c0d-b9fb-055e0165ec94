"""Intent parser v2 – converts free-form user queries into structured intents.

For speed and to avoid heavy ML dependencies, we rely on:
1. Keyword matching for broad categories.
2. OpenAI function calling (optional) when `use_llm=True`.

The result is always a list of strings representing high-level intents
(e.g. ["profit_target", "market_analysis"]).
"""
from __future__ import annotations

import os
from typing import List

import re

# ----------------------------------------------------------------------------
_KEYWORDS_MAP = {
    "profit": "profit_target",
    "make me": "profit_target",
    "target": "profit_target",
    "analyze": "market_analysis",
    "analysis": "market_analysis",
    "squeeze": "ttm_squeeze_scan",
    "options": "options_strategy",
    "portfolio": "portfolio_analysis",
    "risk": "risk_assessment",
    "strategy": "strategy_brainstorming",
    "research": "stock_research",
    "news": "market_news_analysis",
    "technical": "technical_analysis",
    "hello": "greeting",
    "hi": "greeting",
    "hey": "greeting",
    "options strategy analyzer": "options_strategy_analyzer",
    "options analyzer": "options_strategy_analyzer",
    "options trading calculator": "options_strategy_analyzer",
}

_PATTERN_CACHE = {k: re.compile(k, re.IGNORECASE) for k in _KEYWORDS_MAP}


def parse_intent(message: str, use_llm: bool = False) -> List[str]:
    """Parse a user message into a list of intents.

    Parameters
    ----------
    message : str
        The raw user input.
    use_llm : bool, default False
        If True, route through OpenAI for more accurate classification.
    """
    intents: List[str] = []
    for kw, intent in _KEYWORDS_MAP.items():
        if _PATTERN_CACHE[kw].search(message):
            intents.append(intent)
    intents = list(dict.fromkeys(intents))  # Deduplicate while preserving order

    if not intents and use_llm and os.getenv("OPENAI_API_KEY"):
        try:
            import openai

            openai.api_key = os.getenv("OPENAI_API_KEY")
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[
                    {
                        "role": "system",
                        "content": (
                            "You are an intent classifier. Possible intents: "
                            + ", ".join(set(_KEYWORDS_MAP.values()))
                        ),
                    },
                    {"role": "user", "content": message},
                ],
                temperature=0.0,
                max_tokens=10,
            )
            prediction = response.choices[0].message["content"].strip()
            intents = [i.strip() for i in prediction.split(",") if i.strip()]
        except Exception:  # noqa: BLE001
            # Fallback: unknown intent
            intents = ["unknown"]

    return intents or ["unknown"] 