#!/usr/bin/env python3
"""
Add Missing Tools to TotalRecall TOOLS Registry

This script adds all the missing specialized trading tools that should be
in the TOOLS registry but aren't currently loaded.
"""

def add_missing_tools():
    """Add all missing tools to the TOOLS registry."""
    
    print("🔧 ADDING MISSING TOOLS TO TOTALRECALL")
    print("=" * 50)
    
    try:
        from core.chat_core import TOOLS
        original_count = len(TOOLS)
        print(f"📊 Original tools count: {original_count}")
        
        # Additional tools that should be in the registry
        additional_tools = {}
        
        # 1. SCANNER TOOLS
        try:
            from scanners.sp500_ttm_batch_scanner import run_sp500_batch_scan, run_sp500_scan_sync
            additional_tools["run_sp500_batch_scan"] = {
                "function": lambda timeframes=['15min'], priority_first=True: run_sp500_scan_sync(timeframes, priority_first),
                "schema": {
                    "name": "run_sp500_batch_scan",
                    "description": "Run comprehensive S&P 500 TTM squeeze batch scan",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "timeframes": {"type": "array", "items": {"type": "string"}, "default": ["15min"]},
                            "priority_first": {"type": "boolean", "default": True}
                        }
                    }
                }
            }
            print("✅ Added S&P 500 batch scanner")
        except ImportError:
            print("⚠️ S&P 500 batch scanner not available")
        
        # 2. AUTOMATION TOOLS
        try:
            from core.automation_engine import get_automation_engine
            automation = get_automation_engine()

            additional_tools["start_automation_aggressive"] = {
                "function": lambda: automation.start_automation("aggressive"),
                "schema": {
                    "name": "start_automation_aggressive",
                    "description": "Start automation in aggressive mode for maximum opportunities",
                    "parameters": {"type": "object", "properties": {}}
                }
            }

            # Only add methods that exist
            if hasattr(automation, 'change_mode'):
                additional_tools["change_automation_mode"] = {
                    "function": automation.change_mode,
                    "schema": {
                        "name": "change_automation_mode",
                        "description": "Change automation mode while running",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "mode": {"type": "string", "enum": ["conservative", "balanced", "aggressive"]}
                            },
                            "required": ["mode"]
                        }
                    }
                }

            print("✅ Added automation engine tools")
        except Exception as e:
            print(f"⚠️ Automation engine not available: {e}")
        
        # 3. PERFORMANCE ANALYTICS TOOLS (AVAILABLE)
        try:
            from core.performance_analytics import (
                get_performance_summary, get_ttm_grade_analysis,
                get_strategy_comparison, get_risk_analysis,
                export_performance_data
            )

            additional_tools["get_performance_summary"] = {
                "function": lambda days=30: get_performance_summary(days),
                "schema": {
                    "name": "get_performance_summary",
                    "description": "Get comprehensive performance summary for specified period",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "days": {"type": "integer", "default": 30, "description": "Number of days to analyze"}
                        }
                    }
                }
            }

            additional_tools["get_ttm_grade_analysis"] = {
                "function": get_ttm_grade_analysis,
                "schema": {
                    "name": "get_ttm_grade_analysis",
                    "description": "Analyze performance by TTM grade (A+, A, B+, etc.)",
                    "parameters": {"type": "object", "properties": {}}
                }
            }

            additional_tools["get_strategy_comparison"] = {
                "function": get_strategy_comparison,
                "schema": {
                    "name": "get_strategy_comparison",
                    "description": "Compare performance across different trading strategies",
                    "parameters": {"type": "object", "properties": {}}
                }
            }

            additional_tools["get_risk_analysis"] = {
                "function": get_risk_analysis,
                "schema": {
                    "name": "get_risk_analysis",
                    "description": "Comprehensive risk analysis and metrics",
                    "parameters": {"type": "object", "properties": {}}
                }
            }

            additional_tools["export_performance_data"] = {
                "function": lambda format_type='summary': export_performance_data(format_type),
                "schema": {
                    "name": "export_performance_data",
                    "description": "Export performance data to file",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "format_type": {"type": "string", "default": "summary"}
                        }
                    }
                }
            }
            print("✅ Added performance analytics tools")
        except Exception as e:
            print(f"⚠️ Performance analytics not available: {e}")

        # 4. TTM SCANNER TOOLS (AVAILABLE)
        try:
            from scanners.ttm_squeeze_scanner import scan_ttm_squeeze_opportunities

            additional_tools["scan_ttm_squeeze_opportunities"] = {
                "function": lambda symbols=None, min_grade="C", max_results=10: scan_ttm_squeeze_opportunities(symbols, min_grade, max_results),
                "schema": {
                    "name": "scan_ttm_squeeze_opportunities",
                    "description": "Scan for TTM squeeze opportunities with grade filtering",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "symbols": {"type": "array", "items": {"type": "string"}},
                            "min_grade": {"type": "string", "enum": ["A", "B", "C", "D"], "default": "C"},
                            "max_results": {"type": "integer", "default": 10}
                        }
                    }
                }
            }
            print("✅ Added TTM scanner tools")
        except Exception as e:
            print(f"⚠️ TTM scanner not available: {e}")

        # 5. SIMPLE ADDITIONAL TOOLS
        additional_tools["get_system_info"] = {
            "function": lambda: f"TotalRecall Enhanced Trading System - {len(TOOLS)} tools available",
            "schema": {
                "name": "get_system_info",
                "description": "Get system information and tool count",
                "parameters": {"type": "object", "properties": {}}
            }
        }

        additional_tools["list_all_tools"] = {
            "function": lambda: f"Available tools ({len(TOOLS)}): " + ", ".join(sorted(TOOLS.keys())),
            "schema": {
                "name": "list_all_tools",
                "description": "List all available tools in the system",
                "parameters": {"type": "object", "properties": {}}
            }
        }

        print("✅ Added system utility tools")
        
        # Add all new tools to TOOLS registry
        TOOLS.update(additional_tools)
        
        new_count = len(TOOLS)
        added_count = new_count - original_count
        
        print(f"\n📊 TOOL RESTORATION COMPLETE")
        print("=" * 35)
        print(f"Original tools: {original_count}")
        print(f"Added tools: {added_count}")
        print(f"Total tools: {new_count}")
        
        if new_count >= 100:
            print(f"\n🎉 EXCELLENT! You now have {new_count} tools!")
            print("✅ Your system is fully equipped with all specialized functions")
        else:
            print(f"\n✅ GOOD! You now have {new_count} tools")
            print("Your system has been significantly enhanced")
        
        return True
        
    except Exception as e:
        print(f"❌ Error adding tools: {e}")
        return False

if __name__ == "__main__":
    add_missing_tools()
