#!/usr/bin/env python3
"""
TTM Setup Viewer - GUI for viewing and managing TTM squeeze setups
Provides comprehensive display and management of all stored TTM setups
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any

try:
    from core.ttm_setup_manager import get_setup_manager, get_ttm_setups, get_ttm_setup_statistics
    SETUP_MANAGER_AVAILABLE = True
except ImportError:
    SETUP_MANAGER_AVAILABLE = False


class TTMSetupViewer:
    """GUI for viewing and managing TTM setups."""
    
    def __init__(self, parent=None):
        if parent:
            self.root = tk.Toplevel(parent)
        else:
            self.root = tk.Tk()
        
        self.root.title("TotalRecall - TTM Setup Viewer")
        self.root.geometry("1200x800")
        self.root.resizable(True, True)
        
        # Setup manager reference
        self.setup_manager = get_setup_manager() if SETUP_MANAGER_AVAILABLE else None
        
        # Current setups data
        self.current_setups = []
        self.filtered_setups = []
        
        # Auto-refresh settings
        self.auto_refresh = True
        self.refresh_interval = 30  # seconds
        self.refresh_thread = None
        
        self.setup_gui()
        self.load_setups()
        self.start_auto_refresh()
    
    def setup_gui(self):
        """Setup the main GUI layout."""
        # Create main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Setup toolbar
        self.setup_toolbar(main_frame)
        
        # Setup filters
        self.setup_filters(main_frame)
        
        # Setup setups table
        self.setup_setups_table(main_frame)
        
        # Setup status bar
        self.setup_status_bar(main_frame)
    
    def setup_toolbar(self, parent):
        """Setup toolbar with action buttons."""
        toolbar = ttk.Frame(parent)
        toolbar.pack(fill=tk.X, pady=(0, 10))
        
        # Title
        title_label = tk.Label(toolbar, text="🎯 TTM Squeeze Setups", 
                              font=("Arial", 16, "bold"))
        title_label.pack(side=tk.LEFT)
        
        # Buttons
        button_frame = ttk.Frame(toolbar)
        button_frame.pack(side=tk.RIGHT)
        
        ttk.Button(button_frame, text="🔄 Refresh", 
                  command=self.load_setups).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(button_frame, text="📊 Statistics", 
                  command=self.show_statistics).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(button_frame, text="📤 Export", 
                  command=self.export_setups).pack(side=tk.LEFT, padx=2)
        
        # Auto-refresh toggle
        self.auto_refresh_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(button_frame, text="Auto Refresh", 
                       variable=self.auto_refresh_var,
                       command=self.toggle_auto_refresh).pack(side=tk.LEFT, padx=5)
    
    def setup_filters(self, parent):
        """Setup filtering controls."""
        filter_frame = ttk.LabelFrame(parent, text="🔍 Filters", padding=10)
        filter_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Filter controls in a grid
        controls_frame = ttk.Frame(filter_frame)
        controls_frame.pack(fill=tk.X)
        
        # Grade filter
        ttk.Label(controls_frame, text="Min Grade:").grid(row=0, column=0, sticky=tk.W, padx=5)
        self.grade_var = tk.StringVar(value="All")
        grade_combo = ttk.Combobox(controls_frame, textvariable=self.grade_var, 
                                 values=["All", "A+", "A", "B", "C", "D"], width=8)
        grade_combo.grid(row=0, column=1, padx=5)
        grade_combo.bind('<<ComboboxSelected>>', lambda e: self.apply_filters())
        
        # Symbol filter
        ttk.Label(controls_frame, text="Symbol:").grid(row=0, column=2, sticky=tk.W, padx=5)
        self.symbol_var = tk.StringVar()
        symbol_entry = ttk.Entry(controls_frame, textvariable=self.symbol_var, width=10)
        symbol_entry.grid(row=0, column=3, padx=5)
        symbol_entry.bind('<KeyRelease>', lambda e: self.apply_filters())
        
        # Status filter
        ttk.Label(controls_frame, text="Status:").grid(row=0, column=4, sticky=tk.W, padx=5)
        self.status_var = tk.StringVar(value="All")
        status_combo = ttk.Combobox(controls_frame, textvariable=self.status_var,
                                  values=["All", "ACTIVE", "TRIGGERED", "EXPIRED"], width=10)
        status_combo.grid(row=0, column=5, padx=5)
        status_combo.bind('<<ComboboxSelected>>', lambda e: self.apply_filters())
        
        # Time filter
        ttk.Label(controls_frame, text="Last:").grid(row=0, column=6, sticky=tk.W, padx=5)
        self.time_var = tk.StringVar(value="24h")
        time_combo = ttk.Combobox(controls_frame, textvariable=self.time_var,
                                values=["1h", "6h", "24h", "7d", "30d", "All"], width=8)
        time_combo.grid(row=0, column=7, padx=5)
        time_combo.bind('<<ComboboxSelected>>', lambda e: self.apply_filters())
        
        # Min confidence
        ttk.Label(controls_frame, text="Min Conf:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.confidence_var = tk.StringVar(value="0")
        confidence_spin = ttk.Spinbox(controls_frame, from_=0, to=100, increment=5,
                                    textvariable=self.confidence_var, width=8)
        confidence_spin.grid(row=1, column=1, padx=5, pady=5)
        confidence_spin.bind('<KeyRelease>', lambda e: self.apply_filters())
        
        # Clear filters button
        ttk.Button(controls_frame, text="🗑️ Clear Filters", 
                  command=self.clear_filters).grid(row=1, column=7, padx=5, pady=5)
    
    def setup_setups_table(self, parent):
        """Setup the setups table with scrollbars."""
        table_frame = ttk.Frame(parent)
        table_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create treeview with scrollbars
        columns = ('Symbol', 'Grade', 'Confidence', 'Price', 'R/R', 'Entry', 'Stop', 'Target', 
                  'Volume', 'Time', 'Status')
        
        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=20)
        
        # Configure columns
        column_widths = {
            'Symbol': 80, 'Grade': 60, 'Confidence': 80, 'Price': 80, 'R/R': 60,
            'Entry': 80, 'Stop': 80, 'Target': 80, 'Volume': 100, 'Time': 120, 'Status': 80
        }
        
        for col in columns:
            self.tree.heading(col, text=col, command=lambda c=col: self.sort_by_column(c))
            self.tree.column(col, width=column_widths.get(col, 100), anchor=tk.CENTER)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack everything
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Bind double-click for setup details
        self.tree.bind('<Double-1>', self.show_setup_details)
        
        # Configure row colors based on grade
        self.tree.tag_configure('A+', background='#e8f5e8')  # Light green
        self.tree.tag_configure('A', background='#f0f8ff')   # Light blue
        self.tree.tag_configure('B', background='#fff8dc')   # Light yellow
        self.tree.tag_configure('C', background='#ffe4e1')   # Light pink
        self.tree.tag_configure('D', background='#f5f5f5')   # Light gray
    
    def setup_status_bar(self, parent):
        """Setup status bar."""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.status_label = tk.Label(status_frame, text="Ready", relief=tk.SUNKEN, anchor=tk.W)
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        self.count_label = tk.Label(status_frame, text="0 setups", relief=tk.SUNKEN, anchor=tk.E)
        self.count_label.pack(side=tk.RIGHT)
    
    def load_setups(self):
        """Load setups from the database."""
        if not SETUP_MANAGER_AVAILABLE:
            messagebox.showerror("Error", "Setup Manager not available!")
            return
        
        try:
            self.status_label.config(text="Loading setups...")
            self.root.update()
            
            # Get all setups (we'll filter in memory for better performance)
            self.current_setups = get_ttm_setups(limit=1000)
            
            # Apply current filters
            self.apply_filters()
            
            self.status_label.config(text=f"Loaded {len(self.current_setups)} setups")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load setups: {e}")
            self.status_label.config(text="Error loading setups")
    
    def apply_filters(self):
        """Apply current filters to the setups."""
        if not self.current_setups:
            return
        
        try:
            filtered = self.current_setups.copy()
            
            # Grade filter
            if self.grade_var.get() != "All":
                grade_values = {"A+": 5, "A": 4, "B": 3, "C": 2, "D": 1}
                min_grade_value = grade_values.get(self.grade_var.get(), 1)
                filtered = [s for s in filtered if grade_values.get(s.grade, 0) >= min_grade_value]
            
            # Symbol filter
            if self.symbol_var.get().strip():
                symbol_filter = self.symbol_var.get().strip().upper()
                filtered = [s for s in filtered if symbol_filter in s.symbol.upper()]
            
            # Status filter
            if self.status_var.get() != "All":
                filtered = [s for s in filtered if s.status == self.status_var.get()]
            
            # Time filter
            if self.time_var.get() != "All":
                time_map = {"1h": 1, "6h": 6, "24h": 24, "7d": 168, "30d": 720}
                hours_back = time_map.get(self.time_var.get(), 24)
                cutoff_time = datetime.now() - timedelta(hours=hours_back)
                filtered = [s for s in filtered if s.scan_timestamp >= cutoff_time]
            
            # Confidence filter
            try:
                min_confidence = float(self.confidence_var.get()) / 100
                filtered = [s for s in filtered if s.confidence >= min_confidence]
            except ValueError:
                pass
            
            self.filtered_setups = filtered
            self.update_table()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to apply filters: {e}")
    
    def update_table(self):
        """Update the table with filtered setups."""
        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # Add filtered setups
        for setup in self.filtered_setups:
            values = (
                setup.symbol,
                setup.grade,
                f"{setup.confidence*100:.0f}%",
                f"${setup.price:.2f}",
                f"{setup.risk_reward_ratio:.1f}:1",
                f"${setup.entry_price:.2f}",
                f"${setup.stop_loss:.2f}",
                f"${setup.target_price:.2f}",
                f"{setup.volume:,.0f}",
                setup.scan_timestamp.strftime("%m/%d %H:%M"),
                setup.status
            )
            
            # Insert with grade-based tag for coloring
            self.tree.insert('', tk.END, values=values, tags=(setup.grade,))
        
        # Update count
        self.count_label.config(text=f"{len(self.filtered_setups)} setups")
    
    def clear_filters(self):
        """Clear all filters."""
        self.grade_var.set("All")
        self.symbol_var.set("")
        self.status_var.set("All")
        self.time_var.set("24h")
        self.confidence_var.set("0")
        self.apply_filters()
    
    def sort_by_column(self, column):
        """Sort table by column."""
        # This is a simplified sort - in a real implementation you'd want more sophisticated sorting
        pass
    
    def show_setup_details(self, event):
        """Show detailed information for selected setup."""
        selection = self.tree.selection()
        if not selection:
            return
        
        item = self.tree.item(selection[0])
        symbol = item['values'][0]
        
        # Find the setup
        setup = None
        for s in self.filtered_setups:
            if s.symbol == symbol:
                setup = s
                break
        
        if setup:
            self.show_setup_detail_window(setup)
    
    def show_setup_detail_window(self, setup):
        """Show detailed setup information in a new window."""
        detail_window = tk.Toplevel(self.root)
        detail_window.title(f"TTM Setup Details - {setup.symbol}")
        detail_window.geometry("500x600")
        
        # Create scrolled text widget
        text_widget = tk.Text(detail_window, wrap=tk.WORD, font=("Consolas", 10))
        scrollbar = ttk.Scrollbar(detail_window, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)
        
        # Setup details
        details = f"""🎯 TTM SQUEEZE SETUP DETAILS
{'='*50}

📊 BASIC INFORMATION:
• Symbol: {setup.symbol}
• Grade: {setup.grade} ({setup.criteria_count}/5 criteria)
• Confidence: {setup.confidence*100:.1f}%
• Timeframe: {setup.timeframe}
• Status: {setup.status}

💰 PRICING:
• Current Price: ${setup.price:.2f}
• Entry Price: ${setup.entry_price:.2f}
• Stop Loss: ${setup.stop_loss:.2f}
• Target Price: ${setup.target_price:.2f}
• Risk/Reward: {setup.risk_reward_ratio:.2f}:1

📈 TECHNICAL DATA:
• Volume: {setup.volume:,.0f}
• Momentum: {setup.momentum_value:.4f}
• Breakout Probability: {setup.breakout_probability*100:.1f}%

🏢 COMPANY INFO:
• Sector: {setup.sector or 'N/A'}
• Market Cap: ${setup.market_cap:,.0f}

⏰ TIMING:
• Scan Time: {setup.scan_timestamp.strftime('%Y-%m-%d %H:%M:%S')}
• Setup Key: {setup.setup_key}

📝 NOTES:
{setup.notes or 'No notes available'}

🎯 SETUP QUALITY ANALYSIS:
"""
        
        # Add quality analysis
        if setup.grade == "A+":
            details += "🟢 EXCELLENT - Perfect setup with all criteria met\n"
        elif setup.grade == "A":
            details += "🔵 VERY GOOD - Strong setup with high probability\n"
        elif setup.grade == "B":
            details += "🟡 GOOD - Decent setup worth considering\n"
        elif setup.grade == "C":
            details += "🟠 FAIR - Marginal setup, use caution\n"
        else:
            details += "🔴 POOR - Low quality setup, avoid\n"
        
        text_widget.insert(tk.END, details)
        text_widget.config(state=tk.DISABLED)
        
        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def show_statistics(self):
        """Show setup statistics."""
        if not SETUP_MANAGER_AVAILABLE:
            messagebox.showerror("Error", "Setup Manager not available!")
            return
        
        try:
            stats = get_ttm_setup_statistics(24)
            
            stats_text = f"""📊 TTM SETUP STATISTICS (Last 24 Hours)
{'='*50}

📈 OVERVIEW:
• Total Setups: {stats.get('total_setups', 0):,}
• Active Setups: {stats.get('active_setups', 0):,}

🏆 GRADE DISTRIBUTION:
"""
            
            grade_dist = stats.get('grade_distribution', {})
            for grade in ['A+', 'A', 'B', 'C', 'D']:
                count = grade_dist.get(grade, 0)
                stats_text += f"• {grade}: {count:,}\n"
            
            stats_text += f"\n🎯 TOP SYMBOLS:\n"
            top_symbols = stats.get('top_symbols', [])
            for symbol, count in top_symbols[:10]:
                stats_text += f"• {symbol}: {count} setups\n"
            
            messagebox.showinfo("Setup Statistics", stats_text)
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to get statistics: {e}")
    
    def export_setups(self):
        """Export setups to CSV."""
        messagebox.showinfo("Export", "Export functionality coming soon!")
    
    def toggle_auto_refresh(self):
        """Toggle auto-refresh."""
        self.auto_refresh = self.auto_refresh_var.get()
        if self.auto_refresh and not self.refresh_thread:
            self.start_auto_refresh()
    
    def start_auto_refresh(self):
        """Start auto-refresh thread."""
        if self.auto_refresh:
            def refresh_loop():
                while self.auto_refresh:
                    time.sleep(self.refresh_interval)
                    if self.auto_refresh:
                        try:
                            self.root.after(0, self.load_setups)
                        except:
                            break
            
            self.refresh_thread = threading.Thread(target=refresh_loop, daemon=True)
            self.refresh_thread.start()
    
    def run(self):
        """Run the setup viewer."""
        self.root.mainloop()


def main():
    """Launch the TTM Setup Viewer."""
    if not SETUP_MANAGER_AVAILABLE:
        print("❌ TTM Setup Manager not available!")
        return
    
    print("🚀 Launching TTM Setup Viewer...")
    
    try:
        viewer = TTMSetupViewer()
        viewer.run()
    except Exception as e:
        print(f"❌ Error launching setup viewer: {e}")


if __name__ == "__main__":
    main()
